<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\SalePayment;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CustomerPaymentController extends Controller
{
    public function index(Request $request)
    {
        $query = SalePayment::with(['sale.customer', 'user'])
            ->orderBy('payment_date', 'desc');

        // Filter by customer if provided
        if ($request->filled('customer_id')) {
            $query->whereHas('sale', function ($q) use ($request) {
                $q->where('customer_id', $request->customer_id);
            });
        }

        // Filter by payment method if provided
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by date range if provided
        if ($request->filled('date_from')) {
            $query->whereDate('payment_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('payment_date', '<=', $request->date_to);
        }

        $payments = $query->paginate(15);
        $customers = Customer::orderBy('name')->get();

        return view('customer-payments.index', compact('payments', 'customers'));
    }

    public function create(Request $request)
    {
        $sale = null;
        if ($request->filled('sale_id')) {
            $sale = Sale::with('customer')->findOrFail($request->sale_id);

            if (!$sale->canReceivePayment()) {
                return redirect()->back()->with('error', 'هذه العملية مدفوعة بالكامل');
            }
        }

        $customers = Customer::orderBy('name')->get();
        $unpaidSales = Sale::with(['customer', 'payments'])
            ->get()
            ->filter(function ($sale) {
                return $sale->getActualRemainingAmountAttribute() > 0;
            })
            ->sortByDesc('created_at');

        return view('customer-payments.create', compact('sale', 'customers', 'unpaidSales'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'sale_id' => 'required|exists:sales,id',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:cash,card,bank_transfer,check,other',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
            'payment_date' => 'required|date',
        ]);

        try {
            DB::beginTransaction();

            $sale = Sale::findOrFail($request->sale_id);

            if (!$sale->canReceivePayment()) {
                return redirect()->back()->with('error', 'هذه العملية مدفوعة بالكامل');
            }

            if ($request->amount > $sale->getActualRemainingAmountAttribute()) {
                return redirect()->back()->with('error', 'المبلغ المدخل أكبر من المبلغ المتبقي');
            }

            $payment = $sale->makePayment(
                $request->amount,
                $request->payment_method,
                $request->reference_number,
                $request->notes
            );

            // Update payment date if different from now
            if ($request->payment_date !== now()->format('Y-m-d')) {
                $payment->update(['payment_date' => $request->payment_date]);
            }

            DB::commit();

            return redirect()->to(user_route('customer-payments.index'))
                ->with('success', 'تم إضافة الدفعة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating customer payment: ' . $e->getMessage());

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إضافة الدفعة: ' . $e->getMessage());
        }
    }

    public function show(SalePayment $payment)
    {
        $payment->load(['sale.customer', 'user']);

        return view('customer-payments.show', compact('payment'));
    }

    public function destroy(SalePayment $payment)
    {
        try {
            DB::beginTransaction();

            $sale = $payment->sale;
            $amount = $payment->amount;

            // Remove payment amount from sale
            $sale->update([
                'paid_amount' => $sale->paid_amount - $amount
            ]);

            // Remove account transaction if exists
            if ($sale->customer && $sale->customer->account) {
                $sale->customer->account->withdraw(
                    $amount,
                    "إلغاء دفعة مقابل عملية بيع رقم {$sale->invoice_number}"
                );
            }

            $payment->delete();

            DB::commit();

            return redirect()->to(user_route('customer-payments.index'))
                ->with('success', 'تم حذف الدفعة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting customer payment: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف الدفعة: ' . $e->getMessage());
        }
    }

    public function getSaleDetails(Request $request)
    {
        $sale = Sale::with(['customer', 'payments'])->find($request->sale_id);

        if (!$sale) {
            return response()->json(['error' => 'العملية غير موجودة'], 404);
        }

        return response()->json([
            'sale' => [
                'id' => $sale->id,
                'invoice_number' => $sale->invoice_number,
                'customer_name' => $sale->customer?->name ?? 'عميل نقدي',
                'total_amount' => $sale->total_amount,
                'discount_amount' => $sale->discount_amount,
                'paid_amount' => $sale->getTotalPaymentsAttribute(),
                'remaining_amount' => $sale->getActualRemainingAmountAttribute(),
                'can_receive_payment' => $sale->canReceivePayment(),
            ]
        ]);
    }
}

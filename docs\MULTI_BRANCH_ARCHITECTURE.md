# Multi-Branch POS System Architecture

## Overview

This document describes the multi-branch and multi-store architecture implemented in the POS system. The system supports hierarchical organization with branches containing multiple stores, and role-based access control to manage user permissions across different organizational levels.

## Architecture Components

### 1. Organizational Hierarchy

```
Company
├── Branch 1
│   ├── Store 1.1
│   ├── Store 1.2
│   └── Store 1.3
├── Branch 2
│   ├── Store 2.1
│   └── Store 2.2
└── Branch 3
    └── Store 3.1
```

### 2. User Roles

#### Admin Role
- **Access Level**: Full system access
- **Branch Access**: All branches and stores
- **Permissions**: Complete CRUD operations on all entities
- **Dashboard**: Admin dashboard with multi-branch overview
- **Key Features**:
  - Manage all branches and stores
  - View cross-branch reports
  - Manage users and roles
  - Access financial data across all branches

#### Manager Role
- **Access Level**: Branch-level access
- **Branch Access**: Single assigned branch and its stores
- **Permissions**: Management operations within assigned branch
- **Dashboard**: Branch-specific management dashboard
- **Key Features**:
  - Manage branch operations
  - View branch-specific reports
  - Manage branch inventory
  - Oversee branch sales and purchases

#### Seller Role
- **Access Level**: Store-level access
- **Branch Access**: Single assigned branch and store
- **Permissions**: Limited to selling operations
- **Dashboard**: Seller dashboard with sales interface
- **Key Features**:
  - Process sales transactions
  - View assigned store inventory
  - Access customer information
  - Limited reporting (own sales only)

### 3. Database Models

#### Branch Model
```php
class Branch extends Model
{
    protected $fillable = [
        'code', 'name', 'address', 'phone', 'email',
        'opening_balance', 'manager_id', 'is_active'
    ];
    
    // Relationships
    public function stores() // hasMany
    public function users() // hasMany
    public function manager() // belongsTo User
}
```

#### Store Model
```php
class Store extends Model
{
    protected $fillable = [
        'code', 'name', 'branch_id', 'address', 'phone', 'email',
        'opening_balance', 'manager_id', 'is_active'
    ];
    
    // Relationships
    public function branch() // belongsTo
    public function users() // hasMany
    public function manager() // belongsTo User
    public function inventory() // hasMany StoreInventory
}
```

#### Enhanced User Model
```php
class User extends Model
{
    protected $fillable = [
        'name', 'email', 'phone', 'password',
        'role_id', 'branch_id', 'store_id', 'is_active'
    ];
    
    // Role checking methods
    public function isAdmin(): bool
    public function isSeller(): bool
    public function hasRole(string $role): bool
    public function hasPermission(string $permission): bool
    public function canAccessBranch(int $branchId): bool
}
```

### 4. Access Control Implementation

#### Middleware
- **CheckRole**: Validates user role for route access
- **BranchAccessMiddleware**: Ensures users can only access their assigned branch data
- **CheckPermission**: Validates specific permissions for actions

#### Route Protection
```php
// Admin routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard']);
    Route::resource('branches', BranchController::class);
    Route::resource('stores', StoreController::class);
});

// Seller routes
Route::middleware(['auth', 'role:seller', 'branch.access'])->prefix('seller')->group(function () {
    Route::get('/dashboard', [SellerController::class, 'dashboard']);
    Route::resource('sales', SaleController::class)->only(['index', 'create', 'store']);
});
```

### 5. Data Filtering

#### Query Scopes
- **BranchScoped**: Automatically filters data by user's branch
- **StoreScoped**: Filters data by user's store assignment

#### Implementation Example
```php
// In models that need branch filtering
use App\Traits\BranchScoped;

class Sale extends Model
{
    use BranchScoped;
    
    protected static function booted()
    {
        static::addGlobalScope(new BranchScope);
    }
}
```

## Financial Tracking

### Account Management
- **Customer Accounts**: Track customer balances and credit limits
- **Supplier Accounts**: Monitor supplier payments and outstanding amounts
- **Branch Accounts**: Manage branch-level financial data

### Transaction Tracking
- **Account Transactions**: Record all financial movements
- **Balance Calculation**: Real-time balance updates
- **Payment History**: Complete audit trail

## Security Features

### Authentication
- Laravel's built-in authentication system
- Email verification support
- Password reset functionality

### Authorization
- Role-based permissions system
- Route-level access control
- Data-level filtering based on user assignments

### Data Isolation
- Branch-level data separation
- Store-level inventory isolation
- User access restrictions based on assignments

## Installation and Setup

### 1. Database Migration
```bash
php artisan migrate
```

### 2. Seed Default Data
```bash
php artisan db:seed
```

### 3. Default Users Created
- **Admin**: <EMAIL> / admin123
- **Manager**: <EMAIL> / manager123
- **Seller**: <EMAIL> / seller123

## API Endpoints

### Admin Endpoints
- `GET /admin/dashboard` - Admin dashboard
- `GET /admin/branches` - List all branches
- `POST /admin/branches` - Create new branch
- `GET /admin/stores` - List all stores
- `POST /admin/stores` - Create new store

### Seller Endpoints
- `GET /seller/dashboard` - Seller dashboard
- `GET /seller/sales` - List sales (branch-filtered)
- `POST /seller/sales` - Create new sale
- `GET /seller/products` - List products (branch-filtered)

## Testing

### Test Coverage
- Multi-branch access control tests
- Role-based permission tests
- Data filtering tests
- Financial tracking tests

### Running Tests
```bash
php artisan test --filter=MultiBranchSystemTest
php artisan test --filter=RoleBasedAccessTest
```

## Future Enhancements

### Planned Features
1. **Multi-currency Support**: Handle different currencies per branch
2. **Advanced Reporting**: Cross-branch analytics and insights
3. **Inventory Transfer**: Move products between stores/branches
4. **Franchise Management**: Support for franchise operations
5. **Mobile App Integration**: Mobile POS application
6. **Real-time Notifications**: Branch-level alerts and updates

### Scalability Considerations
- Database indexing for large datasets
- Caching strategies for frequently accessed data
- Load balancing for multiple branches
- Backup and disaster recovery procedures

@extends('layouts.app')

@section('title', 'تقارير المدفوعات')

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تقارير المدفوعات</h1>
                <p class="text-muted mb-0">تحليلات شاملة للمدفوعات والأرصدة المعلقة</p>
            </div>
            <div>
                <a href="{{ user_route('payment-reports.export', ['type' => 'outstanding']) }}" class="btn btn-success me-2">
                    <i class="fas fa-file-excel me-2"></i>تصدير الأرصدة المعلقة
                </a>
                <a href="{{ user_route('payment-reports.export', ['type' => 'payments', 'date_from' => $dateFrom, 'date_to' => $dateTo]) }}"
                    class="btn btn-info">
                    <i class="fas fa-file-csv me-2"></i>تصدير المدفوعات
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="{{ $dateFrom }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="{{ $dateTo }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">المورد</label>
                        <select name="supplier_id" class="form-select">
                            <option value="">جميع الموردين</option>
                            @foreach ($suppliers as $supplier)
                                <option value="{{ $supplier->id }}" {{ $supplierId == $supplier->id ? 'selected' : '' }}>
                                    {{ $supplier->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    إجمالي الأرصدة المعلقة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ format_currency($totalOutstanding) }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    عدد العمليات المعلقة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $outstandingCount }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    إجمالي المدفوعات ({{ $dateFrom }} - {{ $dateTo }})
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ format_currency($totalPayments) }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    عدد المدفوعات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $paymentsCount }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-receipt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Top Outstanding Suppliers -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">أعلى الموردين بالأرصدة المعلقة</h6>
                        <a href="{{ user_route('payment-reports.outstanding-balances') }}"
                            class="btn btn-sm btn-outline-primary">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        @if ($topOutstandingSuppliers->count() > 0)
                            @foreach ($topOutstandingSuppliers as $supplier)
                                <div class="d-flex align-items-center mb-3">
                                    <div class="avatar-circle bg-danger text-white me-3">
                                        {{ substr($supplier->name, 0, 1) }}
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">{{ $supplier->name }}</div>
                                        <small
                                            class="text-muted">{{ format_currency($supplier->total_outstanding) }}</small>
                                    </div>
                                    <div>
                                        <a href="{{ user_route('payment-reports.supplier-summary', $supplier) }}"
                                            class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-3">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <p class="text-muted">لا توجد أرصدة معلقة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">آخر المدفوعات</h6>
                        <a href="{{ user_route('payment-reports.payment-history') }}"
                            class="btn btn-sm btn-outline-primary">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        @if ($recentPayments->count() > 0)
                            @foreach ($recentPayments as $payment)
                                <div class="d-flex align-items-center mb-3">
                                    <div class="avatar-circle bg-success text-white me-3">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">{{ $payment->transactionable->supplier->name }}</div>
                                        <small class="text-muted">
                                            عملية #{{ $payment->transactionable->id }} -
                                            {{ $payment->created_at->format('Y-m-d H:i') }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-success">{{ format_currency($payment->amount) }}</div>
                                        <small class="text-muted">{{ $payment->user->name ?? 'غير محدد' }}</small>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-3">
                                <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد مدفوعات حديثة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Trends Chart -->
        @if ($monthlyTrends->count() > 0)
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">اتجاهات المدفوعات الشهرية</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyTrendsChart" width="400" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .border-left-danger {
            border-left: 0.25rem solid #e74a3b !important;
        }

        .border-left-warning {
            border-left: 0.25rem solid #f39c12 !important;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }
    </style>

    @if ($monthlyTrends->count() > 0)
        @push('scripts')
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <script>
                const ctx = document.getElementById('monthlyTrendsChart').getContext('2d');
                const monthlyTrendsChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: {!! json_encode(
                            $monthlyTrends->pluck('month')->map(function ($month) {
                                return \Carbon\Carbon::createFromFormat('Y-m', $month)->format('M Y');
                            }),
                        ) !!},
                        datasets: [{
                            label: 'إجمالي المدفوعات',
                            data: {!! json_encode($monthlyTrends->pluck('total_amount')) !!},
                            borderColor: '#1cc88a',
                            backgroundColor: 'rgba(28, 200, 138, 0.1)',
                            tension: 0.3,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value.toLocaleString() + ' {{ currency_symbol() }}';
                                    }
                                }
                            }
                        }
                    }
                });
            </script>
        @endpush
    @endif
@endsection

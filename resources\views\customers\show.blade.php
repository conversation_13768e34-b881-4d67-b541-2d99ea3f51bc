<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('تفاصيل العميل') }} - {{ $customer->name }}
            </h2>
            <div>
                <a href="{{ user_route('customers.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> {{ __('العودة إلى العملاء') }}
                </a>
                <a href="{{ user_route('customers.edit', $customer) }}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> {{ __('تعديل') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Customer Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ __('معلومات العميل') }}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{{ __('اسم العميل') }}</th>
                                            <td>{{ $customer->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('البريد الإلكتروني') }}</th>
                                            <td>{{ $customer->email ?: 'غير متوفر' }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('رقم الهاتف') }}</th>
                                            <td>{{ $customer->phone ?: 'غير متوفر' }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('العنوان') }}</th>
                                            <td>{{ $customer->address ?: 'غير متوفر' }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('تاريخ التسجيل') }}</th>
                                            <td>{{ $customer->created_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('آخر تحديث') }}</th>
                                            <td>{{ $customer->updated_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ __('ملخص العميل') }}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{{ __('عدد المشتريات') }}</th>
                                            <td>{{ $customer->sales->count() }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('إجمالي المشتريات') }}</th>
                                            <td>{{ number_format($customer->sales->sum('total_amount'), 2) }} ج.م</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('إجمالي المدفوع') }}</th>
                                            <td>{{ number_format($customer->sales->sum('paid_amount'), 2) }} ج.م</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('المبلغ المتبقي') }}</th>
                                            <td>
                                                @php
                                                    $totalRemaining = $customer->sales->sum(function ($sale) {
                                                        return $sale->total_amount -
                                                            $sale->discount_amount -
                                                            $sale->paid_amount;
                                                    });
                                                @endphp
                                                @if ($totalRemaining > 0)
                                                    <span
                                                        class="badge bg-warning fs-6">{{ number_format($totalRemaining, 2) }}
                                                        ج.م</span>
                                                @else
                                                    <span class="badge bg-success fs-6">مسدد بالكامل</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('متوسط قيمة المشتريات') }}</th>
                                            <td>
                                                @php
                                                    $avg =
                                                        $customer->sales->count() > 0
                                                            ? $customer->sales->sum('total_amount') /
                                                                $customer->sales->count()
                                                            : 0;
                                                @endphp
                                                {{ number_format($avg, 2) }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('آخر عملية شراء') }}</th>
                                            <td>
                                                @if ($customer->sales->count() > 0)
                                                    {{ $customer->sales->sortByDesc('created_at')->first()->created_at->format('Y-m-d H:i') }}
                                                @else
                                                    -
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('الحالة') }}</th>
                                            <td>
                                                @if ($customer->sales->count() > 0)
                                                    <span class="badge bg-success">{{ __('نشط') }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ __('غير نشط') }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Search -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('البحث في المنتجات المشتراة') }}</h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="{{ user_route('customers.show', $customer) }}">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="input-group">
                                            <input type="text" name="product_search" class="form-control"
                                                placeholder="ابحث في المنتجات التي اشتراها هذا العميل..."
                                                value="{{ $productSearch }}">
                                            <button class="btn btn-primary" type="submit">
                                                <i class="fas fa-search"></i> بحث
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        @if ($productSearch)
                                            <a href="{{ user_route('customers.show', $customer) }}"
                                                class="btn btn-secondary">
                                                <i class="fas fa-times"></i> مسح البحث
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </form>

                            @if ($productSearch && $purchasedProducts->count() > 0)
                                <div class="mt-4">
                                    <h6>نتائج البحث: {{ $purchasedProducts->count() }} منتج</h6>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>إجمالي الكمية</th>
                                                    <th>إجمالي المبلغ</th>
                                                    <th>متوسط السعر</th>
                                                    <th>عدد مرات الشراء</th>
                                                    <th>آخر شراء</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($purchasedProducts as $item)
                                                    <tr>
                                                        <td>
                                                            <strong>{{ $item->product->name }}</strong>
                                                            @if ($item->product->sku)
                                                                <br><small
                                                                    class="text-muted">{{ $item->product->sku }}</small>
                                                            @endif
                                                        </td>
                                                        <td>{{ $item->total_quantity }}</td>
                                                        <td>{{ number_format($item->total_amount, 2) }} ج.م</td>
                                                        <td>{{ number_format($item->average_price, 2) }} ج.م</td>
                                                        <td>{{ $item->purchase_count }}</td>
                                                        <td>{{ $item->last_purchase_date->format('Y-m-d') }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            @elseif($productSearch)
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle"></i>
                                    لم يتم العثور على منتجات تحتوي على "{{ $productSearch }}" في مشتريات هذا العميل.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Sales History -->
                    @if ($customer->sales->count() > 0)
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">{{ __('سجل المشتريات') }}</h5>
                                <span class="badge bg-primary">{{ $customer->sales->count() }} فاتورة</span>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>{{ __('رقم الفاتورة') }}</th>
                                                <th>{{ __('التاريخ') }}</th>
                                                <th>{{ __('الفرع') }}</th>
                                                <th>{{ __('عدد المنتجات') }}</th>
                                                <th>{{ __('المجموع') }}</th>
                                                <th>{{ __('الخصم') }}</th>
                                                <th>{{ __('المدفوع') }}</th>
                                                <th>{{ __('المتبقي') }}</th>
                                                {{-- <th>{{ __('الحالة') }}</th> --}}
                                                <th>{{ __('الإجراءات') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($customer->sales->sortByDesc('created_at') as $sale)
                                                <tr>
                                                    <td>{{ $sale->invoice_number }}</td>
                                                    <td>{{ $sale->created_at->format('Y-m-d H:i') }}</td>
                                                    <td>{{ $sale->branch->name }}</td>
                                                    <td>{{ $sale->items->count() }}</td>
                                                    <td>{{ number_format($sale->total_amount, 2) }}</td>
                                                    <td>{{ number_format($sale->discount_amount, 2) }}</td>
                                                    <td>{{ number_format($sale->paid_amount, 2) }}</td>
                                                    <td>
                                                        @php
                                                            $remaining =
                                                                $sale->total_amount -
                                                                $sale->discount_amount -
                                                                $sale->paid_amount;
                                                        @endphp
                                                        @if ($remaining > 0)
                                                            <span
                                                                class="badge bg-warning">{{ number_format($remaining, 2) }}
                                                                ج.م</span>
                                                        @else
                                                            <span class="badge bg-success">مسدد</span>
                                                        @endif
                                                    </td>
                                                    {{-- <td>
                                                        @if ($sale->status === 'completed')
                                                            <span class="badge bg-success">{{ __('مكتمل') }}</span>
                                                        @elseif($sale->status === 'pending')
                                                            <span
                                                                class="badge bg-warning">{{ __('قيد الانتظار') }}</span>
                                                        @else
                                                            <span class="badge bg-danger">{{ __('ملغي') }}</span>
                                                        @endif
                                                    </td> --}}
                                                    <td>
                                                        <a href="{{ user_route('sales.show', $sale) }}"
                                                            class="btn btn-sm btn-info"
                                                            title="{{ __('عرض التفاصيل') }}">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ user_route('sales.print', $sale) }}"
                                                            class="btn btn-sm btn-secondary" target="_blank"
                                                            title="{{ __('طباعة') }}">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="alert alert-info">
                            {{ __('لا يوجد سجل مشتريات لهذا العميل') }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            .rtl {
                direction: rtl;
                text-align: right;
            }

            .rtl .table th,
            .rtl .table td {
                text-align: right;
            }

            .rtl .btn-group {
                flex-direction: row-reverse;
            }

            .rtl .btn-group .btn {
                margin-right: 0;
                margin-left: 0.25rem;
            }

            .rtl .text-start {
                text-align: right !important;
            }

            .rtl .text-end {
                text-align: left !important;
            }
        </style>
    @endpush
</x-app-layout>

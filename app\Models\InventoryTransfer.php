<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class InventoryTransfer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'transfer_number',
        'type',
        'source_type',
        'source_id',
        'destination_type',
        'destination_id',
        'requested_by',
        'approved_by',
        'received_by',
        'status',
        'notes',
        'rejection_reason',
        'requested_at',
        'approved_at',
        'shipped_at',
        'received_at',
    ];

    protected $casts = [
        'requested_at' => 'datetime',
        'approved_at' => 'datetime',
        'shipped_at' => 'datetime',
        'received_at' => 'datetime',
    ];

    // Relationships
    public function items(): HasMany
    {
        return $this->hasMany(InventoryTransferItem::class);
    }

    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function receivedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    // Polymorphic relationships for source and destination
    public function source(): MorphTo
    {
        return $this->morphTo('source', 'source_type', 'source_id')->withDefault();
    }

    public function destination(): MorphTo
    {
        return $this->morphTo('destination', 'destination_type', 'destination_id')->withDefault();
    }

    // Specific relationships for branches and stores
    public function sourceBranch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'source_id');
    }

    public function destinationBranch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'destination_id');
    }

    public function sourceStore(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'source_id');
    }

    public function destinationStore(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'destination_id');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeForUser($query, User $user)
    {
        if ($user->isAdmin()) {
            return $query;
        }

        $accessibleBranchIds = $user->getAccessibleBranchIds();
        $accessibleStoreIds = $user->getAccessibleStoreIds();

        // For non-admin users, show transfers related to their accessible branches/stores
        return $query->where(function ($q) use ($accessibleBranchIds, $accessibleStoreIds) {
            // Source is an accessible branch
            if (!empty($accessibleBranchIds)) {
                $q->orWhere(function ($subQ) use ($accessibleBranchIds) {
                    $subQ->where('source_type', 'branch')
                        ->whereIn('source_id', $accessibleBranchIds);
                });
            }

            // Destination is an accessible branch
            if (!empty($accessibleBranchIds)) {
                $q->orWhere(function ($subQ) use ($accessibleBranchIds) {
                    $subQ->where('destination_type', 'branch')
                        ->whereIn('destination_id', $accessibleBranchIds);
                });
            }

            // Source is an accessible store
            if (!empty($accessibleStoreIds)) {
                $q->orWhere(function ($subQ) use ($accessibleStoreIds) {
                    $subQ->where('source_type', 'store')
                        ->whereIn('source_id', $accessibleStoreIds);
                });
            }

            // Destination is an accessible store
            if (!empty($accessibleStoreIds)) {
                $q->orWhere(function ($subQ) use ($accessibleStoreIds) {
                    $subQ->where('destination_type', 'store')
                        ->whereIn('destination_id', $accessibleStoreIds);
                });
            }
        });
    }

    // Helper methods
    public function canBeApproved(): bool
    {
        return $this->status === 'pending';
    }

    public function canBeShipped(): bool
    {
        return $this->status === 'approved';
    }

    public function canBeReceived(): bool
    {
        return $this->status === 'in_transit';
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'approved']);
    }

    public function getTotalItemsCount(): int
    {
        return $this->items()->count();
    }

    public function getTotalRequestedQuantity(): float
    {
        return $this->items()->sum('requested_quantity');
    }

    public function getTotalApprovedQuantity(): float
    {
        return $this->items()->sum('approved_quantity');
    }

    public function getTotalReceivedQuantity(): float
    {
        return $this->items()->sum('received_quantity');
    }

    public function getTotalCost(): float
    {
        return $this->items()->sum('total_cost') ?? 0;
    }

    public function getSourceName(): string
    {
        return $this->source?->name ?? 'Unknown';
    }

    public function getDestinationName(): string
    {
        return $this->destination?->name ?? 'Unknown';
    }

    public function getStatusBadgeClass(): string
    {
        return match ($this->status) {
            'pending' => 'bg-warning',
            'approved' => 'bg-info',
            'in_transit' => 'bg-primary',
            'completed' => 'bg-success',
            'cancelled' => 'bg-danger',
            default => 'bg-secondary'
        };
    }

    public function getStatusText(): string
    {
        return match ($this->status) {
            'pending' => 'في الانتظار',
            'approved' => 'موافق عليه',
            'in_transit' => 'في الطريق',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            default => 'غير معروف'
        };
    }

    // Generate unique transfer number
    public static function generateTransferNumber(): string
    {
        $prefix = 'TRF';
        $date = now()->format('Ymd');
        $lastTransfer = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastTransfer ? (int)substr($lastTransfer->transfer_number, -4) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}

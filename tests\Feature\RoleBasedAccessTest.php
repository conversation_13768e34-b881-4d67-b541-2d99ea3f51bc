<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RoleBasedAccessTest extends TestCase
{
    use RefreshDatabase;

    protected $adminRole;
    protected $sellerRole;
    protected $viewUsersPermission;
    protected $createSalesPermission;

    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions
        $this->viewUsersPermission = Permission::create([
            'name' => 'users.view',
            'description' => 'View users',
            'group' => 'users',
        ]);

        $this->createSalesPermission = Permission::create([
            'name' => 'sales.create',
            'description' => 'Create sales',
            'group' => 'sales',
        ]);

        // Create roles with permissions
        $this->adminRole = Role::create([
            'name' => 'admin',
            'description' => 'Administrator',
            'permissions' => ['users.view', 'sales.create', 'users.create', 'users.edit'],
            'is_active' => true,
        ]);

        $this->sellerRole = Role::create([
            'name' => 'seller',
            'description' => 'Seller',
            'permissions' => ['sales.create'],
            'is_active' => true,
        ]);
    }

    public function test_admin_has_all_permissions(): void
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $this->adminRole->id,
            'is_active' => true,
        ]);

        $this->assertTrue($admin->hasPermission('users.view'));
        $this->assertTrue($admin->hasPermission('sales.create'));
        $this->assertTrue($admin->hasPermission('users.create'));
        $this->assertTrue($admin->hasPermission('users.edit'));
    }

    public function test_seller_has_limited_permissions(): void
    {
        $seller = User::create([
            'name' => 'Seller User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $this->sellerRole->id,
            'is_active' => true,
        ]);

        $this->assertFalse($seller->hasPermission('users.view'));
        $this->assertTrue($seller->hasPermission('sales.create'));
        $this->assertFalse($seller->hasPermission('users.create'));
        $this->assertFalse($seller->hasPermission('users.edit'));
    }

    public function test_role_methods(): void
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $this->adminRole->id,
            'is_active' => true,
        ]);

        $seller = User::create([
            'name' => 'Seller User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $this->sellerRole->id,
            'is_active' => true,
        ]);

        $this->assertTrue($admin->hasRole('admin'));
        $this->assertFalse($admin->hasRole('seller'));
        $this->assertTrue($admin->isAdmin());
        $this->assertFalse($admin->isSeller());

        $this->assertTrue($seller->hasRole('seller'));
        $this->assertFalse($seller->hasRole('admin'));
        $this->assertFalse($seller->isAdmin());
        $this->assertTrue($seller->isSeller());
    }

    public function test_inactive_role_restrictions(): void
    {
        $inactiveRole = Role::create([
            'name' => 'inactive',
            'description' => 'Inactive Role',
            'permissions' => ['sales.create'],
            'is_active' => false,
        ]);

        $user = User::create([
            'name' => 'Inactive User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $inactiveRole->id,
            'is_active' => true,
        ]);

        // User with inactive role should not have permissions
        $this->assertFalse($user->hasPermission('sales.create'));
    }
}

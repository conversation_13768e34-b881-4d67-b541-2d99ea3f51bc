<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class Supplier extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'contact_person',
        'phone',
        'email',
        'address',
        'opening_balance',
        'is_active',
    ];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    /**
     * Get purchases with outstanding balances.
     */
    public function purchasesWithOutstandingBalance()
    {
        return $this->purchases()->where('remaining_amount', '>', 0)->orderBy('purchase_date', 'asc');
    }

    /**
     * Get total outstanding amount from unpaid purchases.
     */
    public function getTotalOutstandingAmount(): float
    {
        return $this->purchases()->sum('remaining_amount');
    }

    /**
     * Get total paid amount to this supplier.
     */
    public function getTotalPaidAmount(): float
    {
        return $this->purchases()->sum('paid_amount');
    }

    public function account(): MorphOne
    {
        return $this->morphOne(Account::class, 'accountable');
    }

    public function getRemainingBalance(): float
    {
        return $this->account?->getRemainingBalance() ?? 0;
    }

    public function getOwedAmount(): float
    {
        return $this->account?->getOwedAmount() ?? 0;
    }

    public function getCreditAmount(): float
    {
        return $this->account?->getCreditAmount() ?? 0;
    }



    public function getTotalPurchases(): float
    {
        return $this->purchases()->sum('total_amount');
    }

    public function getTotalPaid(): float
    {
        return $this->account?->transactions()
            ->where('type', 'withdrawal')
            ->sum('amount') ?? 0;
    }

    public function getOutstandingAmount(): float
    {
        return $this->getTotalPurchases() - $this->getTotalPaid();
    }
}

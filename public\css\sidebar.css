/* Modern Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow-y: auto;
    overflow-x: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.sidebar-header h4 {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.sidebar-header small {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
}

/* User Info */
.user-info {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.03);
}

.user-avatar {
    color: rgba(255, 255, 255, 0.9);
}

.user-details h6 {
    color: white;
    font-weight: 500;
    font-size: 0.95rem;
}

.user-details small {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
}

/* Navigation */
.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.sidebar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-5px);
}

.sidebar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-right: 4px solid #ffd700;
}

.sidebar-nav .nav-link.active::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: #ffd700;
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-left: 0.75rem;
    text-align: center;
    font-size: 1rem;
}

.sidebar-nav .nav-link span {
    flex: 1;
}

.sidebar-nav .nav-link .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* Collapsible Menu */
.sidebar-nav .nav-link.collapsed {
    position: relative;
}

.sidebar-nav .nav-link .fa-chevron-down {
    transition: transform 0.3s ease;
    font-size: 0.8rem;
}

.sidebar-nav .nav-link[aria-expanded="true"] .fa-chevron-down {
    transform: rotate(180deg);
}

.sidebar-nav .collapse .nav-link {
    padding: 0.5rem 1rem 0.5rem 3rem;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    border-right: 2px solid transparent;
}

.sidebar-nav .collapse .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.08);
    border-right-color: rgba(255, 255, 255, 0.3);
}

.sidebar-nav .collapse .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.12);
    border-right-color: #ffd700;
}

.sidebar-nav .collapse .nav-link i {
    width: 16px;
    margin-left: 0.5rem;
    font-size: 0.9rem;
}

/* Special styling for logout */
.sidebar-nav .nav-link.text-danger {
    color: #ff6b6b !important;
    margin-top: 0.5rem;
}

.sidebar-nav .nav-link.text-danger:hover {
    background: rgba(255, 107, 107, 0.1);
    color: #ff5252 !important;
}

/* Border styling */
.sidebar-nav .border-top {
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 100%;
        max-width: 320px;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0 !important;
    }
}

@media (min-width: 769px) {
    .main-content {
        margin-right: 280px;
        transition: margin-right 0.3s ease;
    }

    .sidebar.collapsed {
        width: 70px;
    }

    .sidebar.collapsed .sidebar-header,
    .sidebar.collapsed .user-info {
        display: none;
    }

    .sidebar.collapsed .nav-link span,
    .sidebar.collapsed .nav-link .badge,
    .sidebar.collapsed .nav-link .fa-chevron-down {
        display: none;
    }

    .sidebar.collapsed .nav-link {
        justify-content: center;
        padding: 0.75rem;
    }

    .sidebar.collapsed .nav-link i {
        margin: 0;
    }

    .sidebar.collapsed + .main-content {
        margin-right: 70px;
    }
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: #5a67d8;
    transform: scale(1.05);
}

@media (min-width: 769px) {
    .sidebar-toggle {
        display: none;
    }
}

/* Animation for menu items */
.sidebar-nav .nav-item {
    animation: slideInRight 0.3s ease forwards;
    opacity: 0;
    transform: translateX(20px);
}

.sidebar-nav .nav-item:nth-child(1) {
    animation-delay: 0.1s;
}
.sidebar-nav .nav-item:nth-child(2) {
    animation-delay: 0.2s;
}
.sidebar-nav .nav-item:nth-child(3) {
    animation-delay: 0.3s;
}
.sidebar-nav .nav-item:nth-child(4) {
    animation-delay: 0.4s;
}
.sidebar-nav .nav-item:nth-child(5) {
    animation-delay: 0.5s;
}
.sidebar-nav .nav-item:nth-child(6) {
    animation-delay: 0.6s;
}
.sidebar-nav .nav-item:nth-child(7) {
    animation-delay: 0.7s;
}
.sidebar-nav .nav-item:nth-child(8) {
    animation-delay: 0.8s;
}

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .sidebar {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    }

    .sidebar-toggle {
        background: #4a5568;
    }

    .sidebar-toggle:hover {
        background: #2d3748;
    }
}

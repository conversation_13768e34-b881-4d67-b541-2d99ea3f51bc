<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('branch_inventory', function (Blueprint $table) {
            $table->decimal('cost_price', 10, 2)->nullable()->after('quantity');
            $table->decimal('sale_price_1', 10, 2)->nullable()->after('cost_price');
            $table->decimal('sale_price_2', 10, 2)->nullable()->after('sale_price_1');
            $table->decimal('sale_price_3', 10, 2)->nullable()->after('sale_price_2');
        });
    }

    public function down()
    {
        Schema::table('branch_inventory', function (Blueprint $table) {
            $table->dropColumn(['cost_price', 'sale_price_1', 'sale_price_2', 'sale_price_3']);
        });
    }
};

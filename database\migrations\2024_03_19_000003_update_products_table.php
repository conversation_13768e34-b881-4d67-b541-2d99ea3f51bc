<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('sku')->unique()->after('id');
            $table->decimal('cost_price', 10, 2)->after('price');
            $table->decimal('minimum_stock', 10, 2)->default(10)->after('stock');
            $table->boolean('is_active')->default(true)->after('minimum_stock');
        });
    }

    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn(['sku', 'cost_price', 'minimum_stock', 'is_active']);
        });
    }
};

<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('تفاصيل المصروف') }}
            </h2>
            <div>
                <a href="{{ route('expenses.edit', $expense) }}" class="btn btn-primary me-2">
                    <i class="fas fa-edit"></i> {{ __('تعديل') }}
                </a>
                <a href="{{ route('expenses.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> {{ __('عودة') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="row">
                <!-- Expense Details -->
                <div class="col-md-8">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-4">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <h3 class="mb-4">{{ __('معلومات المصروف') }}</h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">{{ __('المرجع') }}</label>
                                        <p class="mb-0">{{ $expense->reference_number }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">{{ __('التاريخ') }}</label>
                                        <p class="mb-0">{{ $expense->expense_date->format('Y-m-d') }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">{{ __('الفرع') }}</label>
                                        <p class="mb-0">{{ $expense->branch->name }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">{{ __('التصنيف') }}</label>
                                        <p class="mb-0">
                                            <span class="badge" style="background-color: {{ $expense->category->color ?: '#6c757d' }}">
                                                {{ $expense->category->name }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">{{ __('المبلغ') }}</label>
                                        <p class="mb-0">{{ number_format($expense->amount, 2) }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">{{ __('طريقة الدفع') }}</label>
                                        <p class="mb-0">
                                            @if($expense->payment_method == 'cash')
                                                <span class="badge bg-success">{{ __('نقدي') }}</span>
                                            @else
                                                <span class="badge bg-info">{{ __('بنكي') }}</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">{{ __('الوصف') }}</label>
                                        <p class="mb-0">{{ $expense->description ?: '-' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="col-md-4">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-4">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <h3 class="mb-4">{{ __('معلومات إضافية') }}</h3>

                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('تم الإنشاء بواسطة') }}</label>
                                <p class="mb-0">{{ $expense->user->name }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('تاريخ الإنشاء') }}</label>
                                <p class="mb-0">{{ $expense->created_at->format('Y-m-d H:i') }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('آخر تحديث') }}</label>
                                <p class="mb-0">{{ $expense->updated_at->format('Y-m-d H:i') }}</p>
                            </div>

                            @if($expense->cashTransaction)
                                <div class="mb-3">
                                    <label class="form-label text-muted">{{ __('معاملة النقدية') }}</label>
                                    <p class="mb-0">
                                        <span class="badge bg-{{ $expense->cashTransaction->amount < 0 ? 'danger' : 'success' }}">
                                            {{ number_format(abs($expense->cashTransaction->amount), 2) }}
                                        </span>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Delete Form -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <form action="{{ route('expenses.destroy', $expense) }}" method="POST" onsubmit="return confirm('{{ __('هل أنت متأكد من حذف هذا المصروف؟') }}')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-trash"></i> {{ __('حذف المصروف') }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .badge {
            padding: 0.5em 0.75em;
        }
        .form-label {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }
        .btn-group {
            flex-direction: row-reverse;
        }
        .btn-group .btn {
            margin-right: 0;
            margin-left: 0.25rem;
        }
    </style>
    @endpush
</x-app-layout>

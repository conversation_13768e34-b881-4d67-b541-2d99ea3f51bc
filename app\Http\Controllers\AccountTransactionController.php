<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\AccountTransaction;
use App\Models\Customer;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Helpers\AlertHelper;

class AccountTransactionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = AccountTransaction::with(['account', 'fromAccount', 'toAccount', 'user']);

        // Filter by account
        if ($request->has('account_id') && $request->account_id) {
            $query->where('account_id', $request->account_id);
        }

        // Filter by type
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhereHas('account', function ($accountQuery) use ($search) {
                        $accountQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        $transactions = $query->latest()->paginate(15);
        $accounts = Account::where('is_active', true)->get();

        return view('account_transactions.index', compact('transactions', 'accounts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $accounts = Account::where('is_active', true)->get();
        $customers = Customer::get();
        $suppliers = Supplier::where('is_active', true)->get();

        // Pre-select account if provided
        $selectedAccount = null;
        if ($request->has('account_id')) {
            $selectedAccount = Account::find($request->account_id);
        }

        return view('account_transactions.create', compact('accounts', 'customers', 'suppliers', 'selectedAccount'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'account_id' => 'required|exists:accounts,id',
            'type' => 'required|in:debit,credit,transfer',
            'amount' => 'required|numeric|min:0.01',
            'to_account_id' => 'nullable|exists:accounts,id|different:account_id',
            'description' => 'nullable|string|max:500',
            'payment_method' => 'nullable|string|in:cash,card,bank_transfer,check',
            'reference_number' => 'nullable|string|max:100',
        ]);

        try {
            DB::beginTransaction();

            $account = Account::findOrFail($validated['account_id']);
            $balanceBefore = $account->current_balance;

            // Generate reference number if not provided
            if (empty($validated['reference_number'])) {
                $validated['reference_number'] = 'TXN-' . Str::upper(Str::random(8));
            }

            // Calculate new balance
            if ($validated['type'] === 'credit') {
                $balanceAfter = $balanceBefore + $validated['amount'];
            } else {
                $balanceAfter = $balanceBefore - $validated['amount'];
            }

            // Create transaction
            $transaction = AccountTransaction::create([
                'account_id' => $validated['account_id'],
                'reference_number' => $validated['reference_number'],
                'type' => $validated['type'],
                'amount' => $validated['amount'],
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'to_account_id' => $validated['to_account_id'] ?? null,
                'description' => $validated['description'] ?? '',
                'transactionable_type' => null,
                'transactionable_id' => null,
                'user_id' => auth()->id(),
            ]);

            // Update account balance
            $account->update(['current_balance' => $balanceAfter]);

            // Handle transfer to another account
            if ($validated['type'] === 'transfer' && $validated['to_account_id']) {
                $toAccount = Account::findOrFail($validated['to_account_id']);
                $toBalanceBefore = $toAccount->current_balance;
                $toBalanceAfter = $toBalanceBefore + $validated['amount'];

                // Create corresponding credit transaction
                AccountTransaction::create([
                    'account_id' => $validated['to_account_id'],
                    'reference_number' => $validated['reference_number'],
                    'type' => 'credit',
                    'amount' => $validated['amount'],
                    'balance_before' => $toBalanceBefore,
                    'balance_after' => $toBalanceAfter,
                    'from_account_id' => $validated['account_id'],
                    'description' => 'تحويل من: ' . $account->name,
                    'transactionable_type' => null,
                    'transactionable_id' => null,
                    'user_id' => auth()->id(),
                ]);

                // Update to account balance
                $toAccount->update(['current_balance' => $toBalanceAfter]);
            }

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تسجيل المعاملة بنجاح',
                    'transaction' => $transaction->load(['account', 'user'])
                ]);
            }

            AlertHelper::success('تم تسجيل المعاملة بنجاح');
            return redirect()->route('account-transactions.index');
        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء تسجيل المعاملة: ' . $e->getMessage()
                ], 422);
            }

            AlertHelper::error('حدث خطأ أثناء تسجيل المعاملة: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(AccountTransaction $accountTransaction)
    {
        $accountTransaction->load(['account', 'fromAccount', 'toAccount', 'user', 'transactionable']);

        return view('account_transactions.show', compact('accountTransaction'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AccountTransaction $accountTransaction)
    {
        $accounts = Account::where('is_active', true)->get();

        return view('account_transactions.edit', compact('accountTransaction', 'accounts'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AccountTransaction $accountTransaction)
    {
        $validated = $request->validate([
            'description' => 'nullable|string|max:500',
            'reference_number' => 'nullable|string|max:100',
        ]);

        try {
            $accountTransaction->update($validated);

            AlertHelper::success('تم تحديث المعاملة بنجاح');
            return redirect()->route('account-transactions.index');
        } catch (\Exception $e) {
            AlertHelper::error('حدث خطأ أثناء تحديث المعاملة: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AccountTransaction $accountTransaction)
    {
        try {
            // Only allow deletion of manual transactions, not system-generated ones
            if ($accountTransaction->transactionable_type) {
                AlertHelper::error('لا يمكن حذف المعاملات المرتبطة بالنظام');
                return back();
            }

            DB::beginTransaction();

            // Reverse the transaction effect on account balance
            $account = $accountTransaction->account;
            if ($accountTransaction->type === 'credit') {
                $newBalance = $account->current_balance - $accountTransaction->amount;
            } else {
                $newBalance = $account->current_balance + $accountTransaction->amount;
            }

            $account->update(['current_balance' => $newBalance]);

            // Delete the transaction
            $accountTransaction->delete();

            DB::commit();

            AlertHelper::success('تم حذف المعاملة بنجاح');
            return redirect()->route('account-transactions.index');
        } catch (\Exception $e) {
            DB::rollBack();
            AlertHelper::error('حدث خطأ أثناء حذف المعاملة: ' . $e->getMessage());
            return back();
        }
    }
}

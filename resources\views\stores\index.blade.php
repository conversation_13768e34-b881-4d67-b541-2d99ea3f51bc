<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="h3 mb-0"> المخازن</h4>
                    {{-- <a href="{{ user_route('stores.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة مخزن جديد
                    </a> --}}
                </div>
            </div>
        </div>

        <!-- Stores Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        @if ($stores->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الكود</th>
                                            <th>اسم المخزن</th>
                                            <th>الفرع</th>
                                            <th>المدير</th>
                                            <th>العنوان</th>
                                            <th>الهاتف</th>
                                            <th>مخزون المخزن</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($stores as $store)
                                            <tr>
                                                <td>
                                                    <span class="badge bg-secondary">{{ $store->code }}</span>
                                                </td>
                                                <td>
                                                    <div class="fw-bold">{{ $store->name }}</div>
                                                    @if ($store->description)
                                                        <small
                                                            class="text-muted">{{ Str::limit($store->description, 50) }}</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">{{ $store->branch->name }}</span>
                                                </td>
                                                <td>
                                                    @if ($store->manager)
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-user-tie text-primary me-2"></i>
                                                            {{ $store->manager->name }}
                                                        </div>
                                                    @else
                                                        <span class="text-muted">غير محدد</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if ($store->address)
                                                        <small>{{ Str::limit($store->address, 30) }}</small>
                                                    @else
                                                        <span class="text-muted">غير محدد</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if ($store->phone)
                                                        <a href="tel:{{ $store->phone }}" class="text-decoration-none">
                                                            <i class="fas fa-phone text-success"></i>
                                                            {{ $store->phone }}
                                                        </a>
                                                    @else
                                                        <span class="text-muted">غير محدد</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if (request()->routeIs('admin.*'))
                                                        <a href="{{ route('admin.stores.inventory', $store) }}"
                                                            class="btn btn-sm btn-outline-primary d-flex align-items-center justify-content-center"
                                                            title="عرض مخزون المخزن">
                                                            <i class="fas fa-warehouse me-1"></i>
                                                            <span
                                                                class="badge bg-primary">{{ $store->storeInventories->count() ?? 0 }}</span>
                                                        </a>
                                                    @else
                                                        <span
                                                            class="badge bg-secondary">{{ $store->storeInventories->count() ?? 0 }}</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if ($store->is_active)
                                                        <span class="badge bg-success">نشط</span>
                                                    @else
                                                        <span class="badge bg-danger">غير نشط</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ user_route('stores.show', $store) }}"
                                                            class="btn btn-sm btn-outline-info" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ user_route('stores.edit', $store) }}"
                                                            class="btn btn-sm btn-outline-primary" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        {{-- <form action="{{ user_route('stores.destroy', $store) }}"
                                                            method="POST" class="d-inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                title="حذف"
                                                                onclick="return confirm('هل أنت متأكد من حذف هذا المخزن؟')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form> --}}
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-center mt-4">
                                {{ $stores->withQueryString()->links() }}
                            </div>
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد مخازن</h5>
                                <p class="text-muted">لم يتم العثور على أي مخازن مطابقة للبحث</p>
                                <a href="{{ user_route('stores.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة مخزن جديد
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

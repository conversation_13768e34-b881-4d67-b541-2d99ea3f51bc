<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    public function run(): void
    {
        $permissions = [
            // User Management
            ['name' => 'users.view', 'description' => 'View users', 'group' => 'users'],
            ['name' => 'users.create', 'description' => 'Create users', 'group' => 'users'],
            ['name' => 'users.edit', 'description' => 'Edit users', 'group' => 'users'],
            ['name' => 'users.delete', 'description' => 'Delete users', 'group' => 'users'],

            // Role Management
            ['name' => 'roles.view', 'description' => 'View roles', 'group' => 'roles'],
            ['name' => 'roles.create', 'description' => 'Create roles', 'group' => 'roles'],
            ['name' => 'roles.edit', 'description' => 'Edit roles', 'group' => 'roles'],
            ['name' => 'roles.delete', 'description' => 'Delete roles', 'group' => 'roles'],

            // Product Management
            ['name' => 'products.view', 'description' => 'View products', 'group' => 'products'],
            ['name' => 'products.create', 'description' => 'Create products', 'group' => 'products'],
            ['name' => 'products.edit', 'description' => 'Edit products', 'group' => 'products'],
            ['name' => 'products.delete', 'description' => 'Delete products', 'group' => 'products'],

            // Category Management
            ['name' => 'categories.view', 'description' => 'View categories', 'group' => 'categories'],
            ['name' => 'categories.create', 'description' => 'Create categories', 'group' => 'categories'],
            ['name' => 'categories.edit', 'description' => 'Edit categories', 'group' => 'categories'],
            ['name' => 'categories.delete', 'description' => 'Delete categories', 'group' => 'categories'],

            // Branch Management
            ['name' => 'branches.view', 'description' => 'View branches', 'group' => 'branches'],
            ['name' => 'branches.create', 'description' => 'Create branches', 'group' => 'branches'],
            ['name' => 'branches.edit', 'description' => 'Edit branches', 'group' => 'branches'],
            ['name' => 'branches.delete', 'description' => 'Delete branches', 'group' => 'branches'],

            // Store Management
            ['name' => 'stores.view', 'description' => 'View stores', 'group' => 'stores'],
            ['name' => 'stores.create', 'description' => 'Create stores', 'group' => 'stores'],
            ['name' => 'stores.edit', 'description' => 'Edit stores', 'group' => 'stores'],
            ['name' => 'stores.delete', 'description' => 'Delete stores', 'group' => 'stores'],

            // Sales Management
            ['name' => 'sales.view', 'description' => 'View sales', 'group' => 'sales'],
            ['name' => 'sales.create', 'description' => 'Create sales', 'group' => 'sales'],
            ['name' => 'sales.edit', 'description' => 'Edit sales', 'group' => 'sales'],
            ['name' => 'sales.delete', 'description' => 'Delete sales', 'group' => 'sales'],
            ['name' => 'sales.complete', 'description' => 'Complete sales', 'group' => 'sales'],
            ['name' => 'sales.cancel', 'description' => 'Cancel sales', 'group' => 'sales'],

            // Purchase Management
            ['name' => 'purchases.view', 'description' => 'View purchases', 'group' => 'purchases'],
            ['name' => 'purchases.create', 'description' => 'Create purchases', 'group' => 'purchases'],
            ['name' => 'purchases.edit', 'description' => 'Edit purchases', 'group' => 'purchases'],
            ['name' => 'purchases.delete', 'description' => 'Delete purchases', 'group' => 'purchases'],
            ['name' => 'purchases.complete', 'description' => 'Complete purchases', 'group' => 'purchases'],
            ['name' => 'purchases.cancel', 'description' => 'Cancel purchases', 'group' => 'purchases'],

            // Customer Management
            ['name' => 'customers.view', 'description' => 'View customers', 'group' => 'customers'],
            ['name' => 'customers.create', 'description' => 'Create customers', 'group' => 'customers'],
            ['name' => 'customers.edit', 'description' => 'Edit customers', 'group' => 'customers'],
            ['name' => 'customers.delete', 'description' => 'Delete customers', 'group' => 'customers'],

            // Supplier Management
            ['name' => 'suppliers.view', 'description' => 'View suppliers', 'group' => 'suppliers'],
            ['name' => 'suppliers.create', 'description' => 'Create suppliers', 'group' => 'suppliers'],
            ['name' => 'suppliers.edit', 'description' => 'Edit suppliers', 'group' => 'suppliers'],
            ['name' => 'suppliers.delete', 'description' => 'Delete suppliers', 'group' => 'suppliers'],

            // Expense Management
            ['name' => 'expenses.view', 'description' => 'View expenses', 'group' => 'expenses'],
            ['name' => 'expenses.create', 'description' => 'Create expenses', 'group' => 'expenses'],
            ['name' => 'expenses.edit', 'description' => 'Edit expenses', 'group' => 'expenses'],
            ['name' => 'expenses.delete', 'description' => 'Delete expenses', 'group' => 'expenses'],

            // Reports
            ['name' => 'reports.sales', 'description' => 'View sales reports', 'group' => 'reports'],
            ['name' => 'reports.purchases', 'description' => 'View purchases reports', 'group' => 'reports'],
            ['name' => 'reports.inventory', 'description' => 'View inventory reports', 'group' => 'reports'],
            ['name' => 'reports.profit', 'description' => 'View profit reports', 'group' => 'reports'],

            // Settings
            ['name' => 'settings.company', 'description' => 'Manage company settings', 'group' => 'settings'],

            // Account Management
            ['name' => 'accounts.view', 'description' => 'View accounts', 'group' => 'accounts'],
            ['name' => 'accounts.create', 'description' => 'Create accounts', 'group' => 'accounts'],
            ['name' => 'accounts.edit', 'description' => 'Edit accounts', 'group' => 'accounts'],
            ['name' => 'accounts.delete', 'description' => 'Delete accounts', 'group' => 'accounts'],

            // Account Transaction Management
            ['name' => 'account-transactions.view', 'description' => 'View account transactions', 'group' => 'account-transactions'],
            ['name' => 'account-transactions.create', 'description' => 'Create account transactions', 'group' => 'account-transactions'],
            ['name' => 'account-transactions.edit', 'description' => 'Edit account transactions', 'group' => 'account-transactions'],
            ['name' => 'account-transactions.delete', 'description' => 'Delete account transactions', 'group' => 'account-transactions'],

            // Store (Warehouse) Operations
            ['name' => 'stores.inventory.manage', 'description' => 'Manage store inventory (warehouse operations)', 'group' => 'stores'],
            ['name' => 'stores.inventory.add-products', 'description' => 'Add products to store inventory', 'group' => 'stores'],
            ['name' => 'stores.inventory.adjust', 'description' => 'Adjust store inventory quantities', 'group' => 'stores'],
            ['name' => 'stores.inventory.transfer-out', 'description' => 'Transfer products out of store', 'group' => 'stores'],
            ['name' => 'stores.inventory.receive', 'description' => 'Receive products into store', 'group' => 'stores'],
            ['name' => 'stores.inventory.reports', 'description' => 'View store inventory reports', 'group' => 'stores'],

            // Branch (Sales) Operations
            ['name' => 'branches.sales-inventory.view', 'description' => 'View branch sales inventory', 'group' => 'branches'],
            ['name' => 'branches.sales-inventory.manage', 'description' => 'Manage branch sales inventory', 'group' => 'branches'],
            ['name' => 'branches.sales-inventory.add-products', 'description' => 'Add products to branch inventory', 'group' => 'branches'],
            ['name' => 'branches.sales-inventory.receive', 'description' => 'Receive products from stores', 'group' => 'branches'],
            ['name' => 'branches.sales-inventory.adjust', 'description' => 'Adjust branch inventory for sales', 'group' => 'branches'],
            ['name' => 'branches.sales-inventory.pricing', 'description' => 'Manage sales pricing for branch products', 'group' => 'branches'],
            ['name' => 'branches.sales-inventory.request-transfer', 'description' => 'Request product transfers from stores', 'group' => 'branches'],

            // Inventory Transfer Operations
            ['name' => 'inventory-transfers.view', 'description' => 'View inventory transfers', 'group' => 'inventory-transfers'],
            ['name' => 'inventory-transfers.create', 'description' => 'Create inventory transfer requests', 'group' => 'inventory-transfers'],
            ['name' => 'inventory-transfers.approve', 'description' => 'Approve inventory transfer requests', 'group' => 'inventory-transfers'],
            ['name' => 'inventory-transfers.ship', 'description' => 'Ship approved transfers', 'group' => 'inventory-transfers'],
            ['name' => 'inventory-transfers.receive', 'description' => 'Receive transferred products', 'group' => 'inventory-transfers'],
            ['name' => 'inventory-transfers.cancel', 'description' => 'Cancel inventory transfers', 'group' => 'inventory-transfers'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                $permission
            );
        }
    }
}

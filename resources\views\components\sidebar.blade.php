@props(['user' => auth()->user()])

<div class="sidebar" id="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="d-flex align-items-center">
            {{-- <i class="fas fa-store text-primary me-2"></i> --}}
            <div>
                <h5 class="mb-0">شركة الاتحاد لتجارة و توزيع الأدوات الصحية </h5>
                {{-- <small class="text-muted">{{ $user->branch->name ?? 'النظام الرئيسي' }}</small> --}}
            </div>
        </div>
    </div>

    <!-- User Info -->
    <div class="user-info">
        <div class="d-flex align-items-center p-3">
            <div class="user-avatar me-3">
                <i class="fas fa-user-circle fa-2x text-primary"></i>
            </div>
            <div class="user-details">
                <h6 class="mb-0">{{ $user->name }}</h6>
                <small class="">
                    @if ($user->isAdmin())
                        <i class="fas fa-crown text-warning"></i> مدير النظام
                    @elseif($user->isSeller())
                        <i class="fas fa-cash-register text-success"></i> بائع
                    @else
                        <i class="fas fa-user text-info"></i> مستخدم
                    @endif
                </small>
            </div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <ul class="nav flex-column">

            @if ($user->isAdmin())
                {{-- Admin Navigation --}}
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}"
                        href="{{ route('admin.dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم الرئيسية</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#productsMenu" aria-expanded="false"
                        aria-controls="productsMenu">
                        <i class="fas fa-boxes"></i>
                        <span>المنتجات والأصناف</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.products*', 'admin.categories*') ? 'show' : '' }}"
                        id="productsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.categories*') ? 'active' : '' }}"
                                    href="{{ route('admin.categories.index') }}">
                                    <i class="fas fa-tags"></i> الفئات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.products*') ? 'active' : '' }}"
                                    href="{{ route('admin.products.index') }}">
                                    <i class="fas fa-box"></i> المنتجات
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                <!-- Branch Management -->
                {{-- <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#branchMenu" aria-expanded="false"
                        aria-controls="branchMenu">
                        <i class="fas fa-building"></i>
                        <span>إدارة الفروع</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.branches*') ? 'show' : '' }}" id="branchMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.branches.index') ? 'active' : '' }}"
                                    href="{{ route('admin.branches.index') }}">
                                    <i class="fas fa-list"></i> قائمة الفروع
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.branches.analytics-dashboard') ? 'active' : '' }}"
                                    href="{{ route('admin.branches.analytics-dashboard') }}">
                                    <i class="fas fa-chart-line"></i> تحليلات الفروع
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.branches.create') ? 'active' : '' }}"
                                    href="{{ route('admin.branches.create') }}">
                                    <i class="fas fa-plus"></i> إضافة فرع جديد
                                </a>
                            </li>
                        </ul>
                    </div>
                </li> --}}
                <!-- Branches & Analytics -->
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#branchAnalyticsMenu"
                        aria-expanded="false" aria-controls="branchAnalyticsMenu">
                        <i class="fas fa-building"></i>
                        <span>الفروع والمخازن</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.branches*', 'admin.stores*', 'admin.inventory.low-stock', 'admin.inventory.overview') ? 'show' : '' }}"
                        id="branchAnalyticsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.branches*') ? 'active' : '' }}"
                                    href="{{ route('admin.branches.index') }}">
                                    <i class="fas fa-list"></i> الفروع
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.stores*') ? 'active' : '' }}"
                                    href="{{ route('admin.stores.index') }}">
                                    <i class="fas fa-store"></i> المخازن
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.inventory.overview') ? 'active' : '' }}"
                                    href="{{ route('admin.inventory.overview') }}">
                                    <i class="fas fa-eye"></i> نظرة عامة على المخزون
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.inventory.low-stock') ? 'active' : '' }}"
                                    href="{{ route('admin.inventory.low-stock') }}">
                                    <i class="fas fa-exclamation-triangle text-warning"></i> المخزون المنخفض
                                </a>
                            </li>
                            {{-- <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.branches.analytics-dashboard') ? 'active' : '' }}"
                                    href="{{ route('admin.branches.analytics-dashboard') }}">
                                    <i class="fas fa-chart-line"></i> تحليلات الفروع
                                </a>
                            </li> --}}
                            {{-- <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.branches.create') ? 'active' : '' }}"
                                    href="{{ route('admin.branches.create') }}">
                                    <i class="fas fa-plus"></i> إضافة فرع جديد
                                </a>
                            </li> --}}
                        </ul>
                    </div>
                </li>

                <!-- Store Management -->
                {{-- <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.stores*') ? 'active' : '' }}"
                        href="{{ route('admin.stores.index') }}">
                        <i class="fas fa-store"></i>
                        <span> المخازن</span>
                    </a>
                </li> --}}

                <!-- Products & Inventory -->


                <!-- Inventory Management -->
                {{-- <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#inventoryMenu" aria-expanded="false"
                        aria-controls="inventoryMenu">
                        <i class="fas fa-warehouse"></i>
                        <span>إدارة المخزون</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.inventory*') ? 'show' : '' }}"
                        id="inventoryMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.inventory.overview') ? 'active' : '' }}"
                                    href="{{ route('admin.inventory.overview') }}">
                                    <i class="fas fa-eye"></i> نظرة عامة على المخزون
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.inventory.branches') ? 'active' : '' }}"
                                    href="{{ route('admin.inventory.branches') }}">
                                    <i class="fas fa-building"></i> مخزون الفروع
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.inventory.stores') ? 'active' : '' }}"
                                    href="{{ route('admin.inventory.stores') }}">
                                    <i class="fas fa-store"></i> مخزون المخازن
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.inventory.low-stock') ? 'active' : '' }}"
                                    href="{{ route('admin.inventory.low-stock') }}">
                                    <i class="fas fa-exclamation-triangle text-warning"></i> المخزون المنخفض
                                </a>
                            </li>
                        </ul>
                    </div>
                </li> --}}

                <!-- Transfer Management -->
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#transferMenu" aria-expanded="false"
                        aria-controls="transferMenu">
                        <i class="fas fa-exchange-alt"></i>
                        <span>إدارة النقل</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.transfers*', 'admin.inventory-transfers*') ? 'show' : '' }}"
                        id="transferMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.transfers.direct.create') ? 'active' : '' }}"
                                    href="{{ route('admin.transfers.direct.create') }}">
                                    <i class="fas fa-plus"></i> نقل مباشر
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.transfers.history') ? 'active' : '' }}"
                                    href="{{ route('admin.transfers.history') }}">
                                    <i class="fas fa-history"></i> سجل النقل
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Sales & Customers Management -->
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#salesCustomersMenu"
                        aria-expanded="false" aria-controls="salesCustomersMenu">
                        <i class="fas fa-shopping-cart"></i>
                        <span>المبيعات والعملاء</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.sales*', 'admin.customers*', 'admin.customer-payments*', 'admin.sale-returns*') ? 'show' : '' }}"
                        id="salesCustomersMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.sales*') ? 'active' : '' }}"
                                    href="{{ route('admin.sales.index') }}">
                                    <i class="fas fa-cash-register"></i> المبيعات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.customers*') ? 'active' : '' }}"
                                    href="{{ route('admin.customers.index') }}">
                                    <i class="fas fa-users"></i> العملاء
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.sale-returns*') ? 'active' : '' }}"
                                    href="{{ route('admin.sale-returns.index') }}">
                                    <i class="fas fa-undo"></i> مرتجعات المبيعات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.customer-payments*') ? 'active' : '' }}"
                                    href="{{ route('admin.customer-payments.index') }}">
                                    <i class="fas fa-money-bill-wave"></i> دفعات العملاء
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Purchase Management -->
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#purchaseMenu"
                        aria-expanded="false" aria-controls="purchaseMenu">
                        <i class="fas fa-truck"></i>
                        <span>المشتريات والموردين</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.purchases*', 'admin.suppliers*', 'admin.supplier-payments*', 'admin.purchase-returns*') ? 'show' : '' }}"
                        id="purchaseMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.purchases*') ? 'active' : '' }}"
                                    href="{{ route('admin.purchases.index') }}">
                                    <i class="fas fa-shopping-bag"></i> المشتريات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.suppliers*') ? 'active' : '' }}"
                                    href="{{ route('admin.suppliers.index') }}">
                                    <i class="fas fa-truck-loading"></i> الموردين
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.purchase-returns*') ? 'active' : '' }}"
                                    href="{{ route('admin.purchase-returns.index') }}">
                                    <i class="fas fa-undo"></i> مرتجعات المشتريات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.supplier-payments*') ? 'active' : '' }}"
                                    href="{{ route('admin.supplier-payments.index') }}">
                                    <i class="fas fa-money-bill-wave"></i> مدفوعات الموردين
                                </a>
                            </li>
                            {{-- <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.payment-reports*') ? 'active' : '' }}"
                                    href="{{ route('admin.payment-reports.index') }}">
                                    <i class="fas fa-chart-line"></i> تقارير المدفوعات
                                </a>
                            </li> --}}
                            {{-- <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.inventory-transfer-reports*') ? 'active' : '' }}"
                                    href="{{ route('admin.inventory-transfer-reports.index') }}">
                                    <i class="fas fa-chart-bar"></i> تقارير نقل المخزون
                                </a>
                            </li> --}}
                        </ul>
                    </div>
                </li>

                <!-- Financial Management -->
                {{-- <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#financeMenu"
                        aria-expanded="false" aria-controls="financeMenu">
                        <i class="fas fa-chart-line"></i>
                        <span>الإدارة المالية</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.expenses*', 'admin.accounts*') ? 'show' : '' }}"
                        id="financeMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.expenses*') ? 'active' : '' }}"
                                    href="{{ route('admin.expenses.index') }}">
                                    <i class="fas fa-receipt"></i> المصروفات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.accounts*') ? 'active' : '' }}"
                                    href="{{ route('admin.accounts.index') }}">
                                    <i class="fas fa-university"></i> الحسابات
                                </a>
                            </li>
                        </ul>
                    </div>
                </li> --}}

                <!-- Reports -->
                {{-- <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#reportsMenu"
                        aria-expanded="false" aria-controls="reportsMenu">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير والإحصائيات</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.reports*') ? 'show' : '' }}" id="reportsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.reports.sales') ? 'active' : '' }}"
                                    href="{{ route('admin.reports.sales') }}">
                                    <i class="fas fa-chart-pie"></i> تقارير المبيعات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.reports.financial') ? 'active' : '' }}"
                                    href="{{ route('admin.reports.financial') }}">
                                    <i class="fas fa-money-bill-wave"></i> التقارير المالية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.reports.inventory') ? 'active' : '' }}"
                                    href="{{ route('admin.reports.inventory') }}">
                                    <i class="fas fa-warehouse"></i> تقارير المخزون
                                </a>
                            </li>
                        </ul>
                    </div>
                </li> --}}

                <!-- Settings -->
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#settingsMenu"
                        aria-expanded="false" aria-controls="settingsMenu">
                        <i class="fas fa-cogs"></i>
                        <span>إعدادات النظام</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.settings*') ? 'show' : '' }}"
                        id="settingsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings.users*') ? 'active' : '' }}"
                                    href="{{ route('admin.settings.users') }}">
                                    <i class="fas fa-users-cog"></i> إدارة المستخدمين
                                </a>
                            </li>
                            {{-- <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings.company') ? 'active' : '' }}"
                                    href="{{ route('admin.settings.company') }}">
                                    <i class="fas fa-building"></i> إعدادات الشركة
                                </a>
                            </li> --}}
                            {{-- <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings.system') ? 'active' : '' }}"
                                    href="{{ route('admin.settings.system') }}">
                                    <i class="fas fa-server"></i> إعدادات النظام
                                </a>
                            </li> --}}
                        </ul>
                    </div>
                </li>
            @elseif($user->isSeller())
                {{-- Seller Navigation --}}
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('seller.dashboard') ? 'active' : '' }}"
                        href="{{ route('seller.dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>

                <!-- Quick Sale -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('seller.quick-sale*') ? 'active' : '' }}"
                        href="{{ route('seller.quick-sale') }}">
                        <i class="fas fa-cash-register"></i>
                        <span>البيع السريع</span>
                        {{-- <span class="badge bg-success ms-auto">جديد</span> --}}
                    </a>
                </li>

                <!-- Sales -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('seller.sales*') ? 'active' : '' }}"
                        href="{{ route('seller.sales.index') }}">
                        <i class="fas fa-cash-register"></i>
                        <span>المبيعات</span>
                    </a>
                </li>

                <!-- Sale Returns -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('seller.sale-returns*') ? 'active' : '' }}"
                        href="{{ route('seller.sale-returns.index') }}">
                        <i class="fas fa-undo"></i>
                        <span>مرتجعات المبيعات</span>
                    </a>
                </li>

                <!-- Customer Payments -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('seller.customer-payments*') ? 'active' : '' }}"
                        href="{{ route('seller.customer-payments.index') }}">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>دفعات العملاء</span>
                    </a>
                </li>

                <!-- Customers -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('seller.customers*') ? 'active' : '' }}"
                        href="{{ route('seller.customers.index') }}">
                        <i class="fas fa-users"></i>
                        <span>العملاء</span>
                    </a>
                </li>

                <!-- Products -->
                {{-- <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('seller.products*') ? 'active' : '' }}"
                        href="{{ route('seller.products.index') }}">
                        <i class="fas fa-box"></i>
                        <span>المنتجات</span>
                    </a>
                </li> --}}

                <!-- Inventory -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('seller.inventory*') ? 'active' : '' }}"
                        href="{{ route('seller.inventory') }}">
                        <i class="fas fa-warehouse"></i>
                        <span>المخزون</span>
                    </a>
                </li>

                <!-- Direct Transfer -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('seller.transfers.direct.create') ? 'active' : '' }}"
                        href="{{ route('seller.transfers.direct.create') }}">
                        <i class="fas fa-plus"></i>
                        <span>نقل مباشر</span>
                    </a>
                </li>

                <!-- Transfer History -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('seller.transfers.history') ? 'active' : '' }}"
                        href="{{ route('seller.transfers.history') }}">
                        <i class="fas fa-history"></i>
                        <span>سجل النقل</span>
                    </a>
                </li>

                <!-- Cash Management -->
                {{-- <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#cashMenu" aria-expanded="false"
                        aria-controls="cashMenu">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>إدارة النقدية</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('seller.cash*') ? 'show' : '' }}" id="cashMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('seller.cash.register') ? 'active' : '' }}"
                                    href="{{ route('seller.cash.register') }}">
                                    <i class="fas fa-cash-register"></i> صندوق النقدية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('seller.cash.count') ? 'active' : '' }}"
                                    href="{{ route('seller.cash.count') }}">
                                    <i class="fas fa-calculator"></i> جرد النقدية
                                </a>
                            </li>
                        </ul>
                    </div>
                </li> --}}

                <!-- Reports -->
                {{-- <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#sellerReportsMenu"
                        aria-expanded="false" aria-controls="sellerReportsMenu">
                        <i class="fas fa-chart-bar"></i>
                        <span>تقاريري</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('seller.reports*') ? 'show' : '' }}"
                        id="sellerReportsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('seller.reports.my-sales') ? 'active' : '' }}"
                                    href="{{ route('seller.reports.my-sales') }}">
                                    <i class="fas fa-chart-line"></i> مبيعاتي
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('seller.reports.daily-summary') ? 'active' : '' }}"
                                    href="{{ route('seller.reports.daily-summary') }}">
                                    <i class="fas fa-calendar-day"></i> ملخص يومي
                                </a>
                            </li>
                        </ul>
                    </div>
                </li> --}}
            @endif

            <!-- Profile (Available to all users) -->
            {{-- <li class="nav-item mt-3 pt-3 border-top">
                <a class="nav-link {{ request()->routeIs('profile*') ? 'active' : '' }}"
                    href="{{ route('profile.edit') }}">
                    <i class="fas fa-user-edit"></i>
                    <span>الملف الشخصي</span>
                </a>
            </li> --}}

            <!-- Logout -->
            <li class="nav-item">
                <form method="POST" action="{{ route('logout') }}" class="d-inline">
                    @csrf
                    <a class="nav-link text-danger" href="#"
                        onclick="event.preventDefault(); this.closest('form').submit();">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </a>
                </form>
            </li>
        </ul>
    </nav>
</div>

<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-boxes text-primary"></i> {{ __('إدارة المنتجات') }}
                </h2>
                <p class="text-muted small mb-0">إدارة شاملة للمنتجات والمخزون والأسعار</p>
            </div>
            <div class="d-flex gap-2">
                @if (auth()->user()->hasRole('admin'))
                    {{-- <button class="btn btn-outline-secondary" onclick="exportProducts()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <button class="btn btn-outline-info" onclick="importProducts()">
                        <i class="fas fa-upload"></i> استيراد
                    </button> --}}
                    <a href="{{ route('admin.products.import') }}" class="btn btn-outline-success">
                        <i class="fas fa-upload"></i> استيراد Excel
                    </a>
                    <a href="{{ route('admin.products.bulk.create') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus-circle"></i> إضافة متعددة
                    </a>
                    <a href="{{ user_route('products.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة منتج
                    </a>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي المنتجات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_products'] ?? 0 }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-boxes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    المنتجات النشطة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['active_products'] ?? 0 }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    مخزون منخفض
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['low_stock'] ?? 0 }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    قيمة المخزون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ number_format($stats['inventory_value'] ?? 0, 2) }} ج.م</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter"></i> البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form action="{{ user_route('products.index') }}" method="GET" id="filterForm">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" id="search"
                                    placeholder="اسم المنتج، الكود، أو الوصف..." value="{{ request('search') }}">
                                <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="category_id" class="form-label">التصنيف</label>
                            <select name="category_id" class="form-select" id="category_id">
                                <option value="">جميع التصنيفات</option>
                                @foreach ($categories as $category)
                                    <option value="{{ $category->id }}"
                                        {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select name="status" class="form-select" id="status">
                                <option value="">جميع الحالات</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشط
                                </option>
                                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>غير
                                    نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="stock_status" class="form-label">حالة المخزون</label>
                            <select name="stock_status" class="form-select" id="stock_status">
                                <option value="">جميع المستويات</option>
                                <option value="in_stock"
                                    {{ request('stock_status') == 'in_stock' ? 'selected' : '' }}>
                                    متوفر</option>
                                <option value="low_stock"
                                    {{ request('stock_status') == 'low_stock' ? 'selected' : '' }}>مخزون منخفض</option>
                                <option value="out_of_stock"
                                    {{ request('stock_status') == 'out_of_stock' ? 'selected' : '' }}>نفد المخزون
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="sort_by" class="form-label">ترتيب حسب</label>
                            <select name="sort_by" class="form-select" id="sort_by">
                                <option value="name" {{ request('sort_by') == 'name' ? 'selected' : '' }}>الاسم
                                </option>
                                <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>
                                    تاريخ الإضافة</option>
                                <option value="category" {{ request('sort_by') == 'category' ? 'selected' : '' }}>
                                    التصنيف</option>
                                <option value="stock" {{ request('sort_by') == 'stock' ? 'selected' : '' }}>المخزون
                                </option>
                            </select>
                        </div>
                        <div class="col-md-1 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="{{ user_route('products.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Bulk Actions -->
        @if (auth()->user()->hasRole('admin'))
            <div class="card shadow mb-4" id="bulkActionsCard" style="display: none;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span id="selectedCount">0</span> منتج محدد
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-success" onclick="bulkActivate()">
                                <i class="fas fa-check"></i> تفعيل
                            </button>
                            <button class="btn btn-warning" onclick="bulkDeactivate()">
                                <i class="fas fa-pause"></i> إلغاء تفعيل
                            </button>
                            <button class="btn btn-info" onclick="bulkExport()">
                                <i class="fas fa-download"></i> تصدير المحدد
                            </button>
                            <button class="btn btn-danger" onclick="bulkDelete()">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Products Table -->
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table"></i> قائمة المنتجات
                </h6>
                <div class="d-flex gap-2">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary active" onclick="toggleView('table')"
                            id="tableViewBtn">
                            <i class="fas fa-table"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="toggleView('grid')" id="gridViewBtn">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Table View -->

                <div class="table-responsive" id="tableView">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                @if (auth()->user()->hasRole('admin'))
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                @endif
                                <th width="80">الصورة</th>
                                <th>المنتج</th>
                                <th>التصنيف</th>
                                <th>السعر</th>
                                <th>سعر البيع</th>
                                <th>المخزون</th>
                                <th>حالة المخزون</th>
                                <th>الحالة</th>
                                <th>آخر تحديث</th>
                                <th width="150">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($products as $product)
                                <tr>
                                    @if (auth()->user()->hasRole('admin'))
                                        <td>
                                            <input type="checkbox" class="form-check-input product-checkbox"
                                                value="{{ $product->id }}">
                                        </td>
                                    @endif
                                    <td>
                                        @if ($product->image_path)
                                            <img src="{{ asset('storage/' . $product->image_path) }}"
                                                alt="{{ $product->name }}" class="img-thumbnail rounded"
                                                style="width: 60px; height: 60px; object-fit: cover;">
                                        @else
                                            <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                style="width: 60px; height: 60px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $product->name }}</strong>
                                            @if ($product->sku)
                                                <br><small class="text-muted">كود: {{ $product->sku }}</small>
                                            @endif
                                            @if ($product->description)
                                                <br><small
                                                    class="text-muted">{{ Str::limit($product->description, 50) }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $product->category->name }}</span>
                                    </td>
                                    <td>
                                        @if ($product->price)
                                            <strong>{{ number_format($product->price, 2) }} ج.م</strong>
                                        @else
                                            <span class="text-muted">غير محدد</span>
                                        @endif
                                    </td>
                                    <td>
                                        @php
                                            $branchInventory = $product->branchInventories->first();
                                            $storeInventory = $product->storeInventories->first();
                                            $salePrice =
                                                $branchInventory?->sale_price_1 ??
                                                ($storeInventory?->sale_price_1 ?? null);
                                        @endphp
                                        @if ($salePrice)
                                            <strong class="text-success">{{ number_format($salePrice, 2) }}
                                                ج.م</strong>
                                            @if ($branchInventory && ($branchInventory->sale_price_2 || $branchInventory->sale_price_3))
                                                <br>
                                                <small class="text-muted">
                                                    @if ($branchInventory->sale_price_2)
                                                        سعر 2: {{ number_format($branchInventory->sale_price_2, 2) }}
                                                    @endif
                                                    @if ($branchInventory->sale_price_3)
                                                        <br>سعر 3:
                                                        {{ number_format($branchInventory->sale_price_3, 2) }}
                                                    @endif
                                                </small>
                                            @endif
                                        @else
                                            <span class="text-muted">غير محدد</span>
                                        @endif
                                    </td>
                                    <td>
                                        @php
                                            $totalStock =
                                                $product->branchInventories->sum('quantity') +
                                                $product->storeInventories->sum('quantity');
                                        @endphp
                                        <span class="fw-bold">{{ number_format($totalStock, 2) }}</span>
                                        <br>
                                        <small class="text-muted">
                                            فروع: {{ number_format($product->branchInventories->sum('quantity'), 2) }}
                                            |
                                            مخازن: {{ number_format($product->storeInventories->sum('quantity'), 2) }}
                                        </small>
                                    </td>
                                    <td>
                                        @php
                                            $minStock = $product->minimum_stock ?? 10;
                                        @endphp
                                        @if ($totalStock <= 0)
                                            <span class="badge bg-danger">نفد المخزون</span>
                                        @elseif($totalStock <= $minStock)
                                            <span class="badge bg-warning">مخزون منخفض</span>
                                        @else
                                            <span class="badge bg-success">متوفر</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if ($product->is_active)
                                            <span class="badge bg-success">نشط</span>
                                        @else
                                            <span class="badge bg-danger">غير نشط</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ $product->updated_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ user_route('products.show', $product) }}"
                                                class="btn btn-outline-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if (auth()->user()->hasRole('admin'))
                                                <a href="{{ user_route('products.edit', $product) }}"
                                                    class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-outline-success"
                                                    onclick="quickTransfer({{ $product->id }})" title="نقل سريع">
                                                    <i class="fas fa-exchange-alt"></i>
                                                </button>
                                                <button class="btn btn-outline-warning"
                                                    onclick="toggleStatus({{ $product->id }})" title="تغيير الحالة">
                                                    <i
                                                        class="fas fa-toggle-{{ $product->is_active ? 'on' : 'off' }}"></i>
                                                </button>
                                                <button class="btn btn-outline-danger delete-product"
                                                    data-product-id="{{ $product->id }}" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="{{ auth()->user()->hasRole('admin') ? '11' : '10' }}"
                                        class="text-center py-4">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد منتجات تطابق معايير البحث</p>
                                        @if (auth()->user()->hasRole('admin'))
                                            <a href="{{ user_route('products.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> إضافة منتج جديد
                                            </a>
                                        @endif
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Grid View -->
                <div class="row" id="gridView" style="display: none;">
                    @forelse($products as $product)
                        <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 shadow-sm">
                                @if (auth()->user()->hasRole('admin'))
                                    <div class="position-absolute top-0 start-0 p-2">
                                        <input type="checkbox" class="form-check-input product-checkbox"
                                            value="{{ $product->id }}">
                                    </div>
                                @endif

                                <div class="card-img-top d-flex align-items-center justify-content-center bg-light"
                                    style="height: 200px;">
                                    @if ($product->image_path)
                                        <img src="{{ asset('storage/' . $product->image_path) }}"
                                            alt="{{ $product->name }}" class="img-fluid rounded"
                                            style="max-height: 180px; max-width: 100%; object-fit: cover;">
                                    @else
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    @endif
                                </div>

                                <div class="card-body">
                                    <h6 class="card-title">{{ $product->name }}</h6>
                                    @if ($product->sku)
                                        <p class="card-text"><small class="text-muted">كود:
                                                {{ $product->sku }}</small></p>
                                    @endif
                                    <p class="card-text">
                                        <span class="badge bg-secondary">{{ $product->category->name }}</span>
                                    </p>
                                    @if ($product->price)
                                        <p class="card-text"><strong>{{ number_format($product->price, 2) }}
                                                ج.م</strong></p>
                                    @endif

                                    @php
                                        $branchInventory = $product->branchInventories->first();
                                        $storeInventory = $product->storeInventories->first();
                                        $salePrice =
                                            $branchInventory?->sale_price_1 ?? ($storeInventory?->sale_price_1 ?? null);
                                    @endphp
                                    @if ($salePrice)
                                        <p class="card-text">
                                            <strong class="text-success">سعر البيع: {{ number_format($salePrice, 2) }}
                                                ج.م</strong>
                                        </p>
                                    @endif

                                    @php
                                        $totalStock =
                                            $product->branchInventories->sum('quantity') +
                                            $product->storeInventories->sum('quantity');
                                        $minStock = $product->minimum_stock ?? 10;
                                    @endphp

                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <small class="text-muted">المخزون: {{ number_format($totalStock, 2) }}</small>
                                        @if ($totalStock <= 0)
                                            <span class="badge bg-danger">نفد</span>
                                        @elseif($totalStock <= $minStock)
                                            <span class="badge bg-warning">منخفض</span>
                                        @else
                                            <span class="badge bg-success">متوفر</span>
                                        @endif
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        @if ($product->is_active)
                                            <span class="badge bg-success">نشط</span>
                                        @else
                                            <span class="badge bg-danger">غير نشط</span>
                                        @endif
                                        <small class="text-muted">{{ $product->updated_at->diffForHumans() }}</small>
                                    </div>
                                </div>

                                <div class="card-footer bg-transparent">
                                    <div class="btn-group w-100">
                                        <a href="{{ user_route('products.show', $product) }}"
                                            class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if (auth()->user()->hasRole('admin'))
                                            <a href="{{ user_route('products.edit', $product) }}"
                                                class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-success btn-sm"
                                                onclick="quickTransfer({{ $product->id }})">
                                                <i class="fas fa-exchange-alt"></i>
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-12 text-center py-5">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد منتجات تطابق معايير البحث</p>
                            @if (auth()->user()->hasRole('admin'))
                                <a href="{{ user_route('products.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة منتج جديد
                                </a>
                            @endif
                        </div>
                    @endforelse
                </div>

                <div class="mt-4">
                    {{ $products->links() }}
                </div>
            </div>
        </div>
    </div>
    </div>
    </div>
</x-app-layout>

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit form on filter change
            const filterForm = document.getElementById('filterForm');
            const filterSelects = filterForm.querySelectorAll('select');

            filterSelects.forEach(select => {
                select.addEventListener('change', function() {
                    filterForm.submit();
                });
            });

            // Handle select all checkbox
            const selectAllCheckbox = document.getElementById('selectAll');
            const productCheckboxes = document.querySelectorAll('.product-checkbox');

            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    productCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateBulkActions();
                });
            }

            // Handle individual checkboxes
            productCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateBulkActions();

                    // Update select all checkbox
                    if (selectAllCheckbox) {
                        const checkedCount = document.querySelectorAll('.product-checkbox:checked')
                            .length;
                        selectAllCheckbox.checked = checkedCount === productCheckboxes.length;
                        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount <
                            productCheckboxes.length;
                    }
                });
            });

            // Handle product deletion
            const deleteButtons = document.querySelectorAll('.delete-product');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');

                    Swal.fire({
                        title: 'تأكيد الحذف',
                        text: 'هل أنت متأكد من حذف هذا المنتج؟',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'نعم، احذف',
                        cancelButtonText: 'إلغاء'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            deleteProduct(productId);
                        }
                    });
                });
            });
        });

        // Update bulk actions visibility
        function updateBulkActions() {
            const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
            const bulkActionsCard = document.getElementById('bulkActionsCard');
            const selectedCount = document.getElementById('selectedCount');

            if (bulkActionsCard && selectedCount) {
                if (checkedBoxes.length > 0) {
                    bulkActionsCard.style.display = 'block';
                    selectedCount.textContent = checkedBoxes.length;
                } else {
                    bulkActionsCard.style.display = 'none';
                }
            }
        }

        // Toggle view between table and grid
        function toggleView(view) {
            const tableView = document.getElementById('tableView');
            const gridView = document.getElementById('gridView');
            const tableBtn = document.getElementById('tableViewBtn');
            const gridBtn = document.getElementById('gridViewBtn');

            if (view === 'table') {
                tableView.style.display = 'block';
                gridView.style.display = 'none';
                tableBtn.classList.add('active');
                gridBtn.classList.remove('active');
                localStorage.setItem('productsView', 'table');
            } else {
                tableView.style.display = 'none';
                gridView.style.display = 'block';
                tableBtn.classList.remove('active');
                gridBtn.classList.add('active');
                localStorage.setItem('productsView', 'grid');
            }
        }

        // Load saved view preference
        const savedView = localStorage.getItem('productsView') || 'table';
        toggleView(savedView);

        // Clear search
        function clearSearch() {
            document.getElementById('search').value = '';
            document.getElementById('filterForm').submit();
        }

        // Quick transfer function
        function quickTransfer(productId) {
            window.location.href = `{{ route('admin.transfers.direct.create') }}?product_id=${productId}`;
        }

        // Toggle product status
        function toggleStatus(productId) {
            fetch(`{{ url('admin/products') }}/${productId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        Swal.fire('خطأ', data.message || 'حدث خطأ أثناء تغيير حالة المنتج', 'error');
                    }
                })
                .catch(error => {
                    Swal.fire('خطأ', 'حدث خطأ أثناء تغيير حالة المنتج', 'error');
                });
        }

        // Delete product
        function deleteProduct(productId) {
            fetch(`{{ url('admin/products') }}/${productId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('تم الحذف', 'تم حذف المنتج بنجاح', 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ', data.message || 'حدث خطأ أثناء حذف المنتج', 'error');
                    }
                })
                .catch(error => {
                    Swal.fire('خطأ', 'حدث خطأ أثناء حذف المنتج', 'error');
                });
        }

        // Bulk operations
        function bulkActivate() {
            bulkOperation('activate', 'تفعيل المنتجات المحددة');
        }

        function bulkDeactivate() {
            bulkOperation('deactivate', 'إلغاء تفعيل المنتجات المحددة');
        }

        function bulkDelete() {
            const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');

            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف ${checkedBoxes.length} منتج؟`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    bulkOperation('delete', 'حذف المنتجات المحددة');
                }
            });
        }

        function bulkOperation(operation, message) {
            const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
            const productIds = Array.from(checkedBoxes).map(cb => cb.value);

            if (productIds.length === 0) {
                Swal.fire('تنبيه', 'يرجى تحديد منتج واحد على الأقل', 'warning');
                return;
            }

            fetch(`{{ url('admin/products/bulk') }}/${operation}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        product_ids: productIds
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('نجح', `تم ${message} بنجاح`, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ', data.message || `حدث خطأ أثناء ${message}`, 'error');
                    }
                })
                .catch(error => {
                    Swal.fire('خطأ', `حدث خطأ أثناء ${message}`, 'error');
                });
        }

        function bulkExport() {
            const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
            const productIds = Array.from(checkedBoxes).map(cb => cb.value);

            if (productIds.length === 0) {
                Swal.fire('تنبيه', 'يرجى تحديد منتج واحد على الأقل', 'warning');
                return;
            }

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ url('admin/products/export') }}';

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = document.querySelector('meta[name="csrf-token"]').content;
            form.appendChild(csrfToken);

            productIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'product_ids[]';
                input.value = id;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function exportProducts() {
            window.location.href = '{{ url('admin/products/export') }}';
        }

        function importProducts() {
            // Implement import functionality
            Swal.fire('قريباً', 'ستتوفر هذه الميزة قريباً', 'info');
        }
    </script>
@endpush

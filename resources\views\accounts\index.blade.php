<x-app-layout>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">الحسابات</h5>
                        <a href="{{ route('accounts.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة حساب
                        </a>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('accounts.index') }}" method="GET" class="mb-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <select name="type" class="form-select">
                                        <option value="">جميع الأنواع</option>
                                        <option value="branch" {{ request('type') == 'branch' ? 'selected' : '' }}>فرع</option>
                                        <option value="supplier" {{ request('type') == 'supplier' ? 'selected' : '' }}>مورد</option>
                                        <option value="customer" {{ request('type') == 'customer' ? 'selected' : '' }}>عميل</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <select name="is_active" class="form-select">
                                        <option value="">جميع الحالات</option>
                                        <option value="1" {{ request('is_active') == '1' ? 'selected' : '' }}>نشط</option>
                                        <option value="0" {{ request('is_active') == '0' ? 'selected' : '' }}>غير نشط</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> تصفية
                                    </button>
                                </div>
                            </div>
                        </form>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>الرمز</th>
                                        <th>النوع</th>
                                        <th>الرصيد الحالي</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($accounts as $account)
                                        <tr>
                                            <td>{{ $account->name }}</td>
                                            <td>{{ $account->code }}</td>
                                            <td>{{ $account->type == 'branch' ? 'فرع' : ($account->type == 'supplier' ? 'مورد' : 'عميل') }}</td>
                                            <td>{{ number_format($account->current_balance, 2) }}</td>
                                            <td>{{ $account->is_active ? 'نشط' : 'غير نشط' }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ route('accounts.show', $account) }}" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('accounts.edit', $account) }}" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('accounts.destroy', $account) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4">
                            {{ $accounts->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

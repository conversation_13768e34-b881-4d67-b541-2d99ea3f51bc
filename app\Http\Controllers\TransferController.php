<?php

namespace App\Http\Controllers;

use App\Models\InventoryTransfer;
use App\Models\Branch;
use App\Models\Store;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Helpers\AlertHelper;

class TransferController extends Controller
{
    /**
     * Show pending transfers that need approval
     */
    public function pending(Request $request)
    {
        $transfers = InventoryTransfer::with([
            'product',
            'sourceLocation',
            'destinationLocation',
            'user'
        ])
            ->where('status', 'pending')
            ->when($request->search, function ($query, $search) {
                $query->whereHas('product', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('sku', 'like', "%{$search}%");
                });
            })
            ->when($request->source_type, function ($query, $type) {
                $query->where('source_type', $type);
            })
            ->when($request->destination_type, function ($query, $type) {
                $query->where('destination_type', $type);
            })
            ->latest()
            ->paginate(20);

        // Get summary statistics
        $stats = [
            'total_pending' => InventoryTransfer::where('status', 'pending')->count(),
            'branch_to_branch' => InventoryTransfer::where('status', 'pending')
                ->where('source_type', 'branch')
                ->where('destination_type', 'branch')
                ->count(),
            'branch_to_store' => InventoryTransfer::where('status', 'pending')
                ->where('source_type', 'branch')
                ->where('destination_type', 'store')
                ->count(),
            'store_to_branch' => InventoryTransfer::where('status', 'pending')
                ->where('source_type', 'store')
                ->where('destination_type', 'branch')
                ->count(),
        ];

        return view('admin.transfers.pending', compact('transfers', 'stats'));
    }

    /**
     * Show direct transfer history
     */
    public function history(Request $request)
    {
        $user = Auth::user();

        // Build query for direct transfers only
        $query = InventoryTransfer::with([
            'items.product',
            'requestedBy',
            'approvedBy',
            'receivedBy'
        ])
            // Filter for direct transfers (completed immediately without approval workflow)
            ->where(function ($q) {
                $q->where('status', 'completed')
                    ->where('requested_by', '=', DB::raw('approved_by'))
                    ->where('requested_by', '=', DB::raw('received_by'));
            });

        // Load source and destination relationships
        $query->with(['sourceBranch', 'sourceStore', 'destinationBranch', 'destinationStore']);

        // Filter by user permissions for sellers
        if (!$user->hasRole('admin')) {
            $query->where(function ($q) use ($user) {
                $q->where(function ($subQ) use ($user) {
                    // Transfers from user's branch
                    $subQ->where('source_type', 'branch')
                        ->where('source_id', $user->branch_id);
                })->orWhere(function ($subQ) use ($user) {
                    // Transfers to user's branch
                    $subQ->where('destination_type', 'branch')
                        ->where('destination_id', $user->branch_id);
                });
            });
        }

        // Apply filters
        $query->when($request->search, function ($q, $search) {
            $q->whereHas('items.product', function ($productQuery) use ($search) {
                $productQuery->where('name', 'like', "%{$search}%")
                    ->orWhere('sku', 'like', "%{$search}%");
            });
        })
            ->when($request->status, function ($q, $status) {
                $q->where('status', $status);
            })
            ->when($request->date_from, function ($q, $date) {
                $q->whereDate('created_at', '>=', $date);
            })
            ->when($request->date_to, function ($q, $date) {
                $q->whereDate('created_at', '<=', $date);
            });

        $transfers = $query->latest()->paginate(20);

        // Get summary statistics for direct transfers only
        $statsQuery = InventoryTransfer::query()
            // Filter for direct transfers (completed immediately without approval workflow)
            ->where(function ($q) {
                $q->where('status', 'completed')
                    ->where('requested_by', '=', DB::raw('approved_by'))
                    ->where('requested_by', '=', DB::raw('received_by'));
            });

        // Apply same user filters for stats
        if (!$user->hasRole('admin')) {
            $statsQuery->where(function ($q) use ($user) {
                $q->where(function ($subQ) use ($user) {
                    $subQ->where('source_type', 'branch')
                        ->where('source_id', $user->branch_id);
                })->orWhere(function ($subQ) use ($user) {
                    $subQ->where('destination_type', 'branch')
                        ->where('destination_id', $user->branch_id);
                });
            });
        }

        // Apply date filters to stats
        $statsQuery->when($request->date_from, function ($q, $date) {
            $q->whereDate('created_at', '>=', $date);
        })
            ->when($request->date_to, function ($q, $date) {
                $q->whereDate('created_at', '<=', $date);
            });

        $stats = [
            'total_completed' => (clone $statsQuery)->count(), // All direct transfers are completed
            'total_cancelled' => 0, // Direct transfers cannot be cancelled (they're executed immediately)
            'total_quantity' => (clone $statsQuery)
                ->join('inventory_transfer_items', 'inventory_transfers.id', '=', 'inventory_transfer_items.inventory_transfer_id')
                ->sum('inventory_transfer_items.received_quantity'),
        ];

        return view('transfers.history', compact('transfers', 'stats'));
    }

    /**
     * Approve a pending transfer
     */
    public function approve(InventoryTransfer $transfer, Request $request)
    {
        if ($transfer->status !== 'pending') {
            AlertHelper::error('لا يمكن الموافقة على هذا النقل');
            return back();
        }

        DB::beginTransaction();
        try {
            // Get source and destination inventories
            $sourceInventory = $this->getInventory($transfer->source_type, $transfer->source_id, $transfer->product_id);
            $destinationInventory = $this->getInventory($transfer->destination_type, $transfer->destination_id, $transfer->product_id);

            // Check if source has enough quantity
            if (!$sourceInventory || $sourceInventory->quantity < $transfer->quantity) {
                throw new \Exception('الكمية المطلوبة غير متوفرة في المصدر');
            }

            // Remove from source
            $sourceInventory->quantity -= $transfer->quantity;
            $sourceInventory->save();

            // Add to destination
            if ($destinationInventory) {
                $destinationInventory->quantity += $transfer->quantity;
                $destinationInventory->save();
            } else {
                // Create new inventory record for destination
                $this->createInventoryRecord($transfer->destination_type, $transfer->destination_id, $transfer->product_id, $transfer->quantity);
            }

            // Update transfer status
            $transfer->update([
                'status' => 'completed',
                'approved_by' => Auth::id(),
                'approved_at' => now(),
                'notes' => $request->notes
            ]);

            DB::commit();
            AlertHelper::success('تم الموافقة على عملية النقل بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            AlertHelper::error('حدث خطأ: ' . $e->getMessage());
        }

        return back();
    }

    /**
     * Reject a pending transfer
     */
    public function reject(InventoryTransfer $transfer, Request $request)
    {
        if ($transfer->status !== 'pending') {
            AlertHelper::error('لا يمكن رفض هذا النقل');
            return back();
        }

        $transfer->update([
            'status' => 'cancelled',
            'approved_by' => Auth::id(),
            'approved_at' => now(),
            'notes' => $request->notes ?? 'تم الرفض من قبل الإدارة'
        ]);

        AlertHelper::success('تم رفض عملية النقل');
        return back();
    }

    /**
     * Get inventory record for a location and product
     */
    private function getInventory($locationType, $locationId, $productId)
    {
        if ($locationType === 'branch') {
            return \App\Models\BranchInventory::where('branch_id', $locationId)
                ->where('product_id', $productId)
                ->first();
        } else {
            return \App\Models\StoreInventory::where('store_id', $locationId)
                ->where('product_id', $productId)
                ->first();
        }
    }

    /**
     * Create new inventory record
     */
    private function createInventoryRecord($locationType, $locationId, $productId, $quantity)
    {
        if ($locationType === 'branch') {
            \App\Models\BranchInventory::create([
                'branch_id' => $locationId,
                'product_id' => $productId,
                'quantity' => $quantity,
                'cost_price' => 0, // Will be updated later
            ]);
        } else {
            \App\Models\StoreInventory::create([
                'store_id' => $locationId,
                'product_id' => $productId,
                'quantity' => $quantity,
                'minimum_stock' => 0,
            ]);
        }
    }
}

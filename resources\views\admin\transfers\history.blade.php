<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-history text-info"></i> {{ __('تاريخ النقل') }}
                </h2>
                <p class="text-muted small mb-0">سجل جميع عمليات النقل المكتملة والملغاة</p>
            </div>
            <div>
                <a href="{{ route('admin.transfers.pending') }}" class="btn btn-warning">
                    <i class="fas fa-clock"></i> العمليات المعلقة
                </a>
                <a href="{{ route('admin.inventory-transfers.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إنشاء عملية نقل
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    العمليات المكتملة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_completed'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    العمليات الملغاة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_cancelled'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إجمالي الكمية المنقولة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_quantity'], 2) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-boxes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter"></i> البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.transfers.history') }}">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="اسم المنتج أو الكود">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="{{ request('date_from') }}">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Transfer History Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-table"></i> سجل عمليات النقل
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>رقم العملية</th>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>طلب بواسطة</th>
                                <th>تاريخ الطلب</th>
                                <th>تمت الموافقة بواسطة</th>
                                <th>تاريخ الموافقة</th>
                                <th>الحالة</th>
                                <th>الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($transfers as $transfer)
                                <tr>
                                    <td class="fw-bold">#{{ $transfer->id }}</td>
                                    <td>{{ $transfer->product->name }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-info">{{ number_format($transfer->quantity, 2) }}</span>
                                    </td>
                                    <td>
                                        @if($transfer->source_type == 'branch')
                                            <span class="badge bg-primary">فرع</span>
                                        @else
                                            <span class="badge bg-success">مخزن</span>
                                        @endif
                                        {{ $transfer->sourceLocation->name }}
                                    </td>
                                    <td>
                                        @if($transfer->destination_type == 'branch')
                                            <span class="badge bg-primary">فرع</span>
                                        @else
                                            <span class="badge bg-success">مخزن</span>
                                        @endif
                                        {{ $transfer->destinationLocation->name }}
                                    </td>
                                    <td>{{ $transfer->user->name }}</td>
                                    <td>{{ $transfer->created_at->format('Y-m-d H:i') }}</td>
                                    <td>
                                        @if($transfer->approved_by)
                                            {{ \App\Models\User::find($transfer->approved_by)->name ?? 'غير معروف' }}
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        @if($transfer->approved_at)
                                            {{ $transfer->approved_at->format('Y-m-d H:i') }}
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        @if($transfer->status == 'completed')
                                            <span class="badge bg-success">مكتمل</span>
                                        @elseif($transfer->status == 'cancelled')
                                            <span class="badge bg-danger">ملغي</span>
                                        @else
                                            <span class="badge bg-warning">معلق</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($transfer->notes)
                                            <span class="text-truncate" style="max-width: 150px;" title="{{ $transfer->notes }}">
                                                {{ $transfer->notes }}
                                            </span>
                                        @else
                                            -
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد عمليات نقل في السجل</p>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($transfers->hasPages())
                    <div class="mt-4">
                        {{ $transfers->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
        </div>

        <!-- Export Options -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card border-left-secondary shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-secondary">
                            <i class="fas fa-download"></i> تصدير البيانات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="text-muted">تصدير سجل عمليات النقل للفترة المحددة</p>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-outline-success" onclick="exportData('excel')">
                                        <i class="fas fa-file-excel"></i> Excel
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="exportData('pdf')">
                                        <i class="fas fa-file-pdf"></i> PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function exportData(format) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', format);
            window.open(`{{ route('admin.transfers.history') }}?${params.toString()}`, '_blank');
        }
    </script>
    @endpush
</x-app-layout>

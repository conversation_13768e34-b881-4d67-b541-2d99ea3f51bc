<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_item_distributions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_item_id')->constrained()->onDelete('cascade');
            $table->string('location_type'); // 'branch' or 'store'
            $table->unsignedBigInteger('location_id'); // branch_id or store_id
            $table->decimal('quantity', 10, 2);
            $table->decimal('cost_price', 10, 2);
            $table->decimal('sale_price_1', 10, 2);
            $table->decimal('sale_price_2', 10, 2)->nullable();
            $table->decimal('sale_price_3', 10, 2)->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['location_type', 'location_id']);
            $table->index('purchase_item_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_item_distributions');
    }
};

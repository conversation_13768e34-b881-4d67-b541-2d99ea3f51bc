<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BranchScoped;

class Sale extends Model
{
    use HasFactory, BranchScoped;

    protected $fillable = [
        'invoice_number',
        'branch_id',
        'store_id',
        'customer_id',
        'supplier_id',
        'user_id',
        'total_amount',
        'paid_amount',
        'discount_amount',
        'status'
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->invoice_number)) {
                $today = date('Y-m-d');
                $dailyCount = static::whereDate('created_at', $today)->count() + 1;
                $model->invoice_number = 'SAL-' . date('Ymd') . '-' . str_pad($dailyCount, 3, '0', STR_PAD_LEFT);
            }
        });
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function items()
    {
        return $this->hasMany(SaleItem::class);
    }

    public function payments()
    {
        return $this->hasMany(SalePayment::class);
    }

    public function returns()
    {
        return $this->hasMany(SaleReturn::class);
    }

    public function products()
    {
        return $this->belongsToMany(Product::class, 'sale_product')
            ->withPivot('quantity', 'price')
            ->withTimestamps();
    }

    public function getItemsCountAttribute()
    {
        return $this->items->sum('quantity');
    }

    public function getRemainingAmountAttribute()
    {
        return $this->total_amount - $this->paid_amount;
    }

    public function getTotalPaymentsAttribute()
    {
        // The paid_amount field already includes all payments (initial + subsequent)
        // Additional payments are added to paid_amount in makePayment() method
        return $this->paid_amount;
    }

    public function getActualRemainingAmountAttribute()
    {
        return $this->total_amount - $this->discount_amount - $this->getTotalPaymentsAttribute();
    }

    public function canReceivePayment(): bool
    {
        return $this->getActualRemainingAmountAttribute() > 0;
    }

    public function getPaymentStatusAttribute(): string
    {
        $remaining = $this->getActualRemainingAmountAttribute();
        $paid = $this->getTotalPaymentsAttribute();

        if ($remaining <= 0) {
            return 'paid';
        } elseif ($paid > 0) {
            return 'partial';
        } else {
            return 'unpaid';
        }
    }

    public function getPaymentStatusColorAttribute(): string
    {
        return match ($this->getPaymentStatusAttribute()) {
            'paid' => 'success',
            'partial' => 'warning',
            'unpaid' => 'danger',
            default => 'secondary'
        };
    }

    public function getPaymentStatusLabelAttribute(): string
    {
        return match ($this->getPaymentStatusAttribute()) {
            'paid' => 'مدفوع بالكامل',
            'partial' => 'مدفوع جزئياً',
            'unpaid' => 'غير مدفوع',
            default => 'غير محدد'
        };
    }

    public function makePayment(float $amount, string $paymentMethod = 'cash', ?string $referenceNumber = null, ?string $notes = null): SalePayment
    {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Payment amount must be greater than zero');
        }

        if ($amount > $this->getActualRemainingAmountAttribute()) {
            throw new \InvalidArgumentException('Payment amount cannot exceed remaining amount');
        }

        $payment = $this->payments()->create([
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'reference_number' => $referenceNumber,
            'notes' => $notes,
            'payment_date' => now(),
            'user_id' => auth()->id() ?? 1,
        ]);

        // Update paid amount
        $this->update([
            'paid_amount' => $this->paid_amount + $amount
        ]);

        // Create account transaction if customer exists
        if ($this->customer && $this->customer->account) {
            $this->customer->account->deposit(
                $amount,
                "دفعة مقابل عملية بيع رقم {$this->invoice_number}",
                $payment
            );
        }

        return $payment;
    }

    public function getTotalReturnsAttribute()
    {
        return $this->returns->where('status', 'completed')->sum('total_amount');
    }

    public function canBeReturned(): bool
    {
        return $this->status === 'completed' && $this->items->count() > 0;
    }

    public function hasReturns(): bool
    {
        return $this->returns->where('status', 'completed')->count() > 0;
    }

    public function getReturnableItemsAttribute()
    {
        return $this->items->filter(function ($item) {
            $returnedQuantity = $this->returns()
                ->where('status', 'completed')
                ->with('items')
                ->get()
                ->flatMap->items
                ->where('sale_item_id', $item->id)
                ->sum('quantity_returned');

            return $item->quantity > $returnedQuantity;
        });
    }
}

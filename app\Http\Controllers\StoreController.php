<?php

namespace App\Http\Controllers;

use App\Models\Store;
use App\Models\Branch;
use App\Models\User;
use App\Models\Product;
use App\Models\StoreInventory;
use App\Models\InventoryTransfer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Helpers\AlertHelper;

class StoreController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Store::with(['branch', 'manager', 'users', 'storeInventories']);

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%")
                    ->orWhere('address', 'like', "%{$search}%")
                    ->orWhereHas('branch', function ($branchQuery) use ($search) {
                        $branchQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Filter by branch
        if ($request->has('branch_id') && $request->branch_id) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $stores = $query->latest()->paginate(10);
        $branches = Branch::where('is_active', true)->get();

        return view('stores.index', compact('stores', 'branches'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $managers = User::whereHas('role', function ($query) {
            $query->whereIn('name', ['manager', 'admin']);
        })->get();

        return view('stores.create', compact('managers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:stores,code',
            'address' => 'nullable|string|max:500',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'manager_id' => 'nullable|exists:users,id',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        try {
            $store = Store::create($validated);

            AlertHelper::success('تم إنشاء المخزن بنجاح');
            return redirect()->route('admin.stores.index');
        } catch (\Exception $e) {
            DB::rollBack();
            AlertHelper::error('حدث خطأ أثناء إنشاء المخزن: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Store $store)
    {
        $store->load(['branch', 'manager', 'users', 'storeInventories.product']);

        // Get store statistics
        $stats = [
            'total_users' => $store->users()->count(),
            'total_products' => $store->storeInventories()->count(),
            'low_stock_count' => $store->storeInventories()->lowStock()->count(),
            'out_of_stock_count' => $store->storeInventories()->where('quantity', '<=', 0)->count(),
            'total_inventory_value' => $store->storeInventories()
                ->join('products', 'products.id', '=', 'store_inventory.product_id')
                ->sum(DB::raw('store_inventory.quantity * COALESCE(products.price, 0)')),
        ];

        return view('stores.show', compact('store', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Store $store)
    {
        $managers = User::whereHas('role', function ($query) {
            $query->whereIn('name', ['manager', 'admin']);
        })->get();

        return view('stores.edit', compact('store', 'managers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Store $store)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:stores,code,' . $store->id,
            'address' => 'nullable|string|max:500',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'manager_id' => 'nullable|exists:users,id',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        try {
            $store->update($validated);

            AlertHelper::success('تم تحديث المخزن بنجاح');
            return redirect()->route('admin.stores.index');
        } catch (\Exception $e) {
            AlertHelper::error('حدث خطأ أثناء تحديث المخزن: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Store $store)
    {
        try {
            // Check if store has any users assigned
            if ($store->users()->count() > 0) {
                AlertHelper::error('لا يمكن حذف المخزن لأنه يحتوي على موظفين');
                return back();
            }

            // Check if store has inventory
            if ($store->inventory()->count() > 0) {
                AlertHelper::error('لا يمكن حذف المخزن لأنه يحتوي على مخزون');
                return back();
            }

            $store->delete();

            AlertHelper::success('تم حذف المخزن بنجاح');
            return redirect()->route('admin.stores.index');
        } catch (\Exception $e) {
            AlertHelper::error('حدث خطأ أثناء حذف المخزن: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Toggle store status
     */
    public function toggleStatus(Store $store)
    {
        try {
            $store->update(['is_active' => !$store->is_active]);

            $status = $store->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';
            AlertHelper::success($status . ' المخزن بنجاح');

            return back();
        } catch (\Exception $e) {
            AlertHelper::error('حدث خطأ أثناء تغيير حالة المخزن: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Show store inventory management
     */
    public function inventory(Store $store, Request $request)
    {
        $query = StoreInventory::where('store_id', $store->id)
            ->with(['product.category']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Filter by stock status
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low':
                    $query->lowStock();
                    break;
                case 'over':
                    $query->overStock();
                    break;
                case 'out':
                    $query->where('quantity', '<=', 0);
                    break;
            }
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->whereHas('product', function ($q) use ($request) {
                $q->where('category_id', $request->category_id);
            });
        }

        $inventory = $query->orderBy('quantity', 'asc')->paginate(20);

        // Get categories for filter
        $categories = Product::whereHas('storeInventories', function ($q) use ($store) {
            $q->where('store_id', $store->id);
        })->with('category')->get()->pluck('category')->unique('id');

        // Get inventory statistics
        $stats = [
            'total_products' => $store->storeInventories()->count(),
            'low_stock_count' => $store->storeInventories()->lowStock()->count(),
            'out_of_stock_count' => $store->storeInventories()->where('quantity', '<=', 0)->count(),
            'total_value' => $store->storeInventories()
                ->join('products', 'products.id', '=', 'store_inventory.product_id')
                ->sum(DB::raw('store_inventory.quantity * COALESCE(products.price, 0)')),
        ];

        return view('stores.inventory', compact('store', 'inventory', 'categories', 'stats'));
    }

    /**
     * Add product to store inventory
     */
    public function addProduct(Store $store)
    {
        $products = Product::where('is_active', true)
            ->whereNotIn('id', function ($query) use ($store) {
                $query->select('product_id')
                    ->from('store_inventory')
                    ->where('store_id', $store->id);
            })
            ->with('category')
            ->get();

        return view('stores.add-product', compact('store', 'products'));
    }

    /**
     * Store product in inventory
     */
    public function storeProduct(Store $store, Request $request)
    {
        $validated = $request->validate([
            'products' => 'required|array|min:1',
            'products.*.product_id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|numeric|min:0',
            'products.*.minimum_stock' => 'required|numeric|min:0',
            'products.*.maximum_stock' => 'nullable|numeric|min:0',
        ]);

        DB::beginTransaction();
        try {
            foreach ($validated['products'] as $productData) {
                // Check if product already exists in store inventory
                $existingInventory = StoreInventory::where('store_id', $store->id)
                    ->where('product_id', $productData['product_id'])
                    ->first();

                if ($existingInventory) {
                    continue; // Skip if already exists
                }

                StoreInventory::create([
                    'store_id' => $store->id,
                    'product_id' => $productData['product_id'],
                    'quantity' => $productData['quantity'],
                    'minimum_stock' => $productData['minimum_stock'],
                    'maximum_stock' => $productData['maximum_stock'],
                ]);
            }

            DB::commit();
            AlertHelper::success('تم إضافة المنتجات للمخزن بنجاح');
            return redirect()->route('admin.stores.inventory', $store);
        } catch (\Exception $e) {
            DB::rollback();
            AlertHelper::error('حدث خطأ أثناء إضافة المنتجات: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Update inventory item
     */
    public function updateInventory(Store $store, StoreInventory $inventory, Request $request)
    {
        $validated = $request->validate([
            'quantity' => 'required|numeric|min:0',
            'minimum_stock' => 'required|numeric|min:0',
            'maximum_stock' => 'nullable|numeric|min:0',
        ]);

        try {
            $inventory->update($validated);
            AlertHelper::success('تم تحديث المخزون بنجاح');
            return back();
        } catch (\Exception $e) {
            AlertHelper::error('حدث خطأ أثناء تحديث المخزون: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Remove product from inventory
     */
    public function removeProduct(Store $store, StoreInventory $inventory)
    {
        try {
            if ($inventory->quantity > 0) {
                AlertHelper::error('لا يمكن حذف منتج يحتوي على كمية في المخزون');
                return back();
            }

            $inventory->delete();
            AlertHelper::success('تم حذف المنتج من المخزن بنجاح');
            return back();
        } catch (\Exception $e) {
            AlertHelper::error('حدث خطأ أثناء حذف المنتج: ' . $e->getMessage());
            return back();
        }
    }
}

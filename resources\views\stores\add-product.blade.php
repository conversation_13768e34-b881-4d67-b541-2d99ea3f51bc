<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">إضافة منتجات للمخزن: {{ $store->name }}</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('admin.stores.index') }}">المخازن</a></li>
                                <li class="breadcrumb-item"><a
                                        href="{{ route('admin.stores.show', $store) }}">{{ $store->name }}</a></li>
                                <li class="breadcrumb-item"><a
                                        href="{{ route('admin.stores.inventory', $store) }}">المخزون</a></li>
                                <li class="breadcrumb-item active">إضافة منتجات</li>
                            </ol>
                        </nav>
                    </div>
                    <a href="{{ route('admin.stores.inventory', $store) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للمخزون
                    </a>
                </div>
            </div>
        </div>

        @if ($products->count() > 0)
            <!-- Add Products Form -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-plus text-primary"></i> اختيار المنتجات للإضافة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('admin.stores.store-product', $store) }}" method="POST"
                                id="addProductsForm">
                                @csrf

                                <!-- Search and Filter -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" id="productSearch"
                                            placeholder="البحث عن منتج...">
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" id="categoryFilter">
                                            <option value="">جميع الفئات</option>
                                            @foreach ($products->pluck('category')->unique('id')->filter() as $category)
                                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <!-- Products Grid -->
                                <div class="row" id="productsGrid">
                                    @foreach ($products as $product)
                                        <div class="col-lg-4 col-md-6 mb-3 product-item"
                                            data-name="{{ strtolower($product->name) }}"
                                            data-category="{{ $product->category_id ?? '' }}">
                                            <div class="card h-100">
                                                <div class="card-body">
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input product-checkbox" type="checkbox"
                                                            id="product{{ $product->id }}"
                                                            data-product-id="{{ $product->id }}">
                                                        <label class="form-check-label fw-bold"
                                                            for="product{{ $product->id }}">
                                                            {{ $product->name }}
                                                        </label>
                                                    </div>

                                                    <div class="d-flex align-items-center mb-2">
                                                        @if ($product->image)
                                                            <img src="{{ asset('storage/' . $product->image) }}"
                                                                alt="{{ $product->name }}" class="rounded me-2"
                                                                style="width: 50px; height: 50px; object-fit: cover;">
                                                        @else
                                                            <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center"
                                                                style="width: 50px; height: 50px;">
                                                                <i class="fas fa-box text-muted"></i>
                                                            </div>
                                                        @endif
                                                        <div>
                                                            @if ($product->category)
                                                                <span
                                                                    class="badge bg-info mb-1">{{ $product->category->name }}</span>
                                                            @endif
                                                            <div><small class="text-muted">{{ $product->sku }}</small>
                                                            </div>
                                                            <div><small
                                                                    class="text-muted">{{ number_format($product->purchase_price, 2) }}
                                                                    ر.س</small></div>
                                                        </div>
                                                    </div>

                                                    <!-- Product Details Form (Hidden by default) -->
                                                    <div class="product-details" id="details{{ $product->id }}"
                                                        style="display: none;">
                                                        <hr>
                                                        <div class="row g-2">
                                                            <div class="col-12">
                                                                <label class="form-label">الكمية الأولية</label>
                                                                <input type="number"
                                                                    class="form-control form-control-sm"
                                                                    name="products[{{ $product->id }}][quantity]"
                                                                    step="0.01" min="0" value="0">
                                                                <input type="hidden"
                                                                    name="products[{{ $product->id }}][product_id]"
                                                                    value="{{ $product->id }}">
                                                            </div>
                                                            <div class="col-6">
                                                                <label class="form-label">الحد الأدنى</label>
                                                                <input type="number"
                                                                    class="form-control form-control-sm"
                                                                    name="products[{{ $product->id }}][minimum_stock]"
                                                                    step="0.01" min="0" value="10">
                                                            </div>
                                                            <div class="col-6">
                                                                <label class="form-label">الحد الأقصى</label>
                                                                <input type="number"
                                                                    class="form-control form-control-sm"
                                                                    name="products[{{ $product->id }}][maximum_stock]"
                                                                    step="0.01" min="0" value="">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <!-- Submit Button -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span id="selectedCount" class="text-muted">لم يتم اختيار أي منتج</span>
                                            </div>
                                            <div>
                                                <button type="submit" class="btn btn-primary" id="submitBtn"
                                                    disabled>
                                                    <i class="fas fa-save"></i> إضافة المنتجات المحددة
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <!-- No Products Available -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">جميع المنتجات موجودة في المخزن</h5>
                            <p class="text-muted">لا توجد منتجات جديدة لإضافتها إلى هذا المخزن</p>
                            <a href="{{ route('admin.stores.inventory', $store) }}" class="btn btn-primary">
                                <i class="fas fa-warehouse"></i> عرض المخزون الحالي
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const checkboxes = document.querySelectorAll('.product-checkbox');
                const selectedCount = document.getElementById('selectedCount');
                const submitBtn = document.getElementById('submitBtn');
                const productSearch = document.getElementById('productSearch');
                const categoryFilter = document.getElementById('categoryFilter');
                const productItems = document.querySelectorAll('.product-item');

                // Handle checkbox changes
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const productId = this.dataset.productId;
                        const details = document.getElementById('details' + productId);

                        if (this.checked) {
                            details.style.display = 'block';
                            // Set required attributes
                            details.querySelectorAll('input[type="number"]').forEach(input => {
                                if (input.name.includes('quantity') || input.name.includes(
                                        'minimum_stock')) {
                                    input.required = true;
                                }
                            });
                        } else {
                            details.style.display = 'none';
                            // Remove required attributes
                            details.querySelectorAll('input[type="number"]').forEach(input => {
                                input.required = false;
                            });
                        }

                        updateSelectedCount();
                    });
                });

                // Update selected count and submit button state
                function updateSelectedCount() {
                    const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;

                    if (checkedCount === 0) {
                        selectedCount.textContent = 'لم يتم اختيار أي منتج';
                        submitBtn.disabled = true;
                    } else if (checkedCount === 1) {
                        selectedCount.textContent = 'تم اختيار منتج واحد';
                        submitBtn.disabled = false;
                    } else {
                        selectedCount.textContent = `تم اختيار ${checkedCount} منتج`;
                        submitBtn.disabled = false;
                    }
                }

                // Search functionality
                productSearch.addEventListener('input', function() {
                    filterProducts();
                });

                // Category filter
                categoryFilter.addEventListener('change', function() {
                    filterProducts();
                });

                function filterProducts() {
                    const searchTerm = productSearch.value.toLowerCase();
                    const selectedCategory = categoryFilter.value;

                    productItems.forEach(item => {
                        const productName = item.dataset.name;
                        const productCategory = item.dataset.category;

                        const matchesSearch = productName.includes(searchTerm);
                        const matchesCategory = !selectedCategory || productCategory === selectedCategory;

                        if (matchesSearch && matchesCategory) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                }
            });
        </script>
    @endpush
</x-app-layout>

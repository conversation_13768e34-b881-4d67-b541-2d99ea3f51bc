<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Color;

class ProductsTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    public function array(): array
    {
        // Return sample data to help users understand the format
        return [
            [
                'منتج تجريبي 1',
                'إلكترونيات',
                'وصف المنتج التجريبي الأول',
                100.50,
                120.00,
                'نشط'
            ],
            [
                'منتج تجريبي 2',
                'ملابس',
                'وصف المنتج التجريبي الثاني',
                50.00,
                75.00,
                'نشط'
            ],
            [
                'منتج تجريبي 3',
                'طعام',
                '',
                25.00,
                35.00,
                'غير نشط'
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'name',           // اسم المنتج
            'category',       // الفئة
            'description',    // الوصف
            'price',          // سعر التكلفة
            'selling_price',  // سعر البيع
            'is_active'       // الحالة
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4'],
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 25, // name
            'B' => 20, // category
            'C' => 35, // description
            'D' => 15, // price
            'E' => 15, // selling_price
            'F' => 12, // is_active
        ];
    }
}

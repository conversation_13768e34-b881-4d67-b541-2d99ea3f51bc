<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">التقارير المالية</h2>
                    <div class="text-muted">
                        <i class="fas fa-calendar-alt"></i> {{ now()->format('Y-m-d') }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" action="{{ route('admin.financial-reports') }}">
                            <div class="row align-items-end">
                                <div class="col-md-4">
                                    <label for="branch_id" class="form-label">تصفية حسب الفرع</label>
                                    <select name="branch_id" id="branch_id" class="form-select">
                                        <option value="">جميع الفروع</option>
                                        @foreach ($branches as $branchOption)
                                            <option value="{{ $branchOption->id }}"
                                                {{ request('branch_id') == $branchOption->id ? 'selected' : '' }}>
                                                {{ $branchOption->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> تصفية
                                    </button>
                                </div>
                                <div class="col-md-6 text-end">
                                    @if ($branch)
                                        <span class="badge bg-primary fs-6">
                                            <i class="fas fa-store"></i> {{ $branch->name }}
                                        </span>
                                    @else
                                        <span class="badge bg-secondary fs-6">
                                            <i class="fas fa-globe"></i> جميع الفروع
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Overview -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المبيعات</h6>
                                <h3 class="mb-0">{{ format_currency($financialSummary['total_sales'] ?? 0) }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المشتريات</h6>
                                <h3 class="mb-0">{{ format_currency($financialSummary['total_purchases'] ?? 0) }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-shopping-cart fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مستحقات العملاء</h6>
                                <h3 class="mb-0">{{ format_currency($financialSummary['customers_owe'] ?? 0) }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مستحقات الموردين</h6>
                                <h3 class="mb-0">{{ format_currency($financialSummary['suppliers_owe'] ?? 0) }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-truck fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profit Analysis -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie"></i> تحليل الأرباح
                        </h5>
                    </div>
                    <div class="card-body">
                        @php
                            $totalSales = $financialSummary['total_sales'] ?? 0;
                            $totalPurchases = $financialSummary['total_purchases'] ?? 0;
                            $grossProfit = $totalSales - $totalPurchases;
                            $profitMargin = $totalSales > 0 ? ($grossProfit / $totalSales) * 100 : 0;
                        @endphp

                        <div class="row text-center">
                            <div class="col-4">
                                <h6 class="text-muted mb-1">إجمالي الإيرادات</h6>
                                <h4 class="text-success">{{ format_currency($totalSales) }}</h4>
                            </div>
                            <div class="col-4">
                                <h6 class="text-muted mb-1">إجمالي التكاليف</h6>
                                <h4 class="text-danger">{{ format_currency($totalPurchases) }}</h4>
                            </div>
                            <div class="col-4">
                                <h6 class="text-muted mb-1">صافي الربح</h6>
                                <h4 class="{{ $grossProfit >= 0 ? 'text-success' : 'text-danger' }}">
                                    {{ format_currency($grossProfit) }}
                                </h4>
                            </div>
                        </div>

                        <hr>

                        <div class="text-center">
                            <h6 class="text-muted mb-1">هامش الربح</h6>
                            <h3 class="{{ $profitMargin >= 0 ? 'text-success' : 'text-danger' }}">
                                {{ number_format($profitMargin, 1) }}%
                            </h3>
                            <div class="progress mt-2">
                                <div class="progress-bar {{ $profitMargin >= 0 ? 'bg-success' : 'bg-danger' }}"
                                    style="width: {{ abs($profitMargin) }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-balance-scale"></i> الميزانية المالية
                        </h5>
                    </div>
                    <div class="card-body">
                        @php
                            $customersOwe = $financialSummary['customers_owe'] ?? 0;
                            $suppliersOwe = $financialSummary['suppliers_owe'] ?? 0;
                            $netBalance = $customersOwe - $suppliersOwe;
                        @endphp

                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="text-muted mb-1">لنا عند العملاء</h6>
                                    <h4 class="text-success">{{ format_currency($customersOwe) }}</h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted mb-1">علينا للموردين</h6>
                                <h4 class="text-danger">{{ format_currency($suppliersOwe) }}</h4>
                            </div>
                        </div>

                        <hr>

                        <div class="text-center">
                            <h6 class="text-muted mb-1">صافي الرصيد</h6>
                            <h3 class="{{ $netBalance >= 0 ? 'text-success' : 'text-danger' }}">
                                {{ format_currency($netBalance) }}
                            </h3>
                            <small class="text-muted">
                                {{ $netBalance >= 0 ? 'رصيد إيجابي' : 'رصيد سلبي' }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Financial Summary -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users"></i> ملخص العملاء المالي
                        </h5>
                        <span class="badge bg-primary">{{ $customerSummary['customers_count'] ?? 0 }} عميل</span>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <h6 class="text-muted mb-1">إجمالي المبيعات</h6>
                                <h5 class="text-primary">{{ format_currency($customerSummary['total_sales'] ?? 0) }}
                                </h5>
                            </div>
                            <div class="col-4">
                                <h6 class="text-muted mb-1">المبلغ المدفوع</h6>
                                <h5 class="text-success">{{ format_currency($customerSummary['total_paid'] ?? 0) }}
                                </h5>
                            </div>
                            <div class="col-4">
                                <h6 class="text-muted mb-1">المبلغ المستحق</h6>
                                <h5 class="text-warning">{{ format_currency($customerSummary['total_owed'] ?? 0) }}
                                </h5>
                            </div>
                        </div>

                        @if (isset($customerSummary['customers']) && count($customerSummary['customers']) > 0)
                            <hr>
                            <h6 class="mb-2">أكبر المدينين:</h6>
                            <div class="list-group list-group-flush">
                                @foreach (array_slice($customerSummary['customers'], 0, 3) as $customer)
                                    <div
                                        class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span>{{ $customer['name'] }}</span>
                                        <span
                                            class="badge bg-warning">{{ format_currency($customer['amount_owed']) }}</span>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-truck"></i> ملخص الموردين المالي
                        </h5>
                        <span class="badge bg-primary">{{ $supplierSummary['suppliers_count'] ?? 0 }} مورد</span>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <h6 class="text-muted mb-1">إجمالي المشتريات</h6>
                                <h5 class="text-primary">
                                    {{ format_currency($supplierSummary['total_purchases'] ?? 0) }}</h5>
                            </div>
                            <div class="col-4">
                                <h6 class="text-muted mb-1">المبلغ المدفوع</h6>
                                <h5 class="text-success">{{ format_currency($supplierSummary['total_paid'] ?? 0) }}
                                </h5>
                            </div>
                            <div class="col-4">
                                <h6 class="text-muted mb-1">المبلغ المستحق</h6>
                                <h5 class="text-danger">{{ format_currency($supplierSummary['total_owed'] ?? 0) }}
                                </h5>
                            </div>
                        </div>

                        @if (isset($supplierSummary['suppliers']) && count($supplierSummary['suppliers']) > 0)
                            <hr>
                            <h6 class="mb-2">أكبر المستحقات:</h6>
                            <div class="list-group list-group-flush">
                                @foreach (array_slice($supplierSummary['suppliers'], 0, 3) as $supplier)
                                    <div
                                        class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span>{{ $supplier['name'] }}</span>
                                        <span
                                            class="badge bg-danger">{{ format_currency($supplier['amount_owed']) }}</span>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="mb-3">تصدير التقارير</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-success">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-danger">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                            <button type="button" class="btn btn-outline-primary">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

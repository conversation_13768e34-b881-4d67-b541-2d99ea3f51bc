<?php

namespace App\Http\Controllers;

use App\Models\ExpenseCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ExpenseCategoryController extends Controller
{
    public function index(Request $request)
    {
        $query = ExpenseCategory::withCount('expenses')
            ->withSum('expenses', 'amount')
            ->with('parent')
            ->when($request->filled('search'), function ($query) use ($request) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('is_active', $request->status === 'active');
            })
            ->when($request->filled('type'), function ($query) use ($request) {
                if ($request->type === 'fixed') {
                    $query->where('is_fixed', true);
                } elseif ($request->type === 'variable') {
                    $query->where('is_fixed', false);
                }
            })
            ->when($request->filled('parent'), function ($query) use ($request) {
                if ($request->parent === 'root') {
                    $query->whereNull('parent_id');
                } else {
                    $query->where('parent_id', $request->parent);
                }
            })
            ->when($request->filled('sort'), function ($query) use ($request) {
                switch ($request->sort) {
                    case 'name_asc':
                        $query->orderBy('name');
                        break;
                    case 'name_desc':
                        $query->orderByDesc('name');
                        break;
                    case 'expenses_count_asc':
                        $query->orderBy('expenses_count');
                        break;
                    case 'expenses_count_desc':
                        $query->orderByDesc('expenses_count');
                        break;
                    case 'total_expenses_asc':
                        $query->orderBy('expenses_sum_amount');
                        break;
                    case 'total_expenses_desc':
                        $query->orderByDesc('expenses_sum_amount');
                        break;
                    case 'budget_usage_asc':
                        $query->orderBy(DB::raw('(expenses_sum_amount / COALESCE(monthly_budget, 0))'));
                        break;
                    case 'budget_usage_desc':
                        $query->orderByDesc(DB::raw('(expenses_sum_amount / COALESCE(monthly_budget, 0))'));
                        break;
                    default:
                        $query->latest();
                }
            }, function ($query) {
                $query->latest();
            });

        $categories = $query->paginate(10);
        $parentCategories = ExpenseCategory::whereNull('parent_id')->get();

        if ($request->wantsJson()) {
            return response()->json($categories);
        }

        return view('expense-categories.index', compact('categories', 'parentCategories'));
    }

    public function create()
    {
        $parentCategories = ExpenseCategory::whereNull('parent_id')->get();
        return view('expense-categories.create', compact('parentCategories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:expense_categories',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'parent_id' => 'nullable|exists:expense_categories,id',
            'monthly_budget' => 'nullable|numeric|min:0',
            'yearly_budget' => 'nullable|numeric|min:0',
            'color' => 'nullable|string|size:7',
            'icon' => 'nullable|image|max:1024',
            'is_fixed' => 'boolean',
            'requires_approval' => 'boolean',
            'approval_roles' => 'nullable|array',
        ]);

        if ($request->hasFile('icon')) {
            $path = $request->file('icon')->store('category-icons', 'public');
            $validated['icon'] = $path;
        }

        ExpenseCategory::create($validated);

        return redirect()->route('expense-categories.index')
            ->with('success', 'تم إضافة تصنيف المصروفات بنجاح');
    }

    public function show(ExpenseCategory $expenseCategory)
    {
        $expenseCategory->load(['children', 'parent']);

        $expenses = $expenseCategory->expenses()
            ->with(['branch', 'category'])
            ->latest()
            ->paginate(10);

        $monthlyExpenses = $expenseCategory->expenses()
            ->whereMonth('expense_date', now()->month)
            ->whereYear('expense_date', now()->year)
            ->sum('amount');

        $yearlyExpenses = $expenseCategory->expenses()
            ->whereYear('expense_date', now()->year)
            ->sum('amount');

        return view('expense-categories.show', compact('expenseCategory', 'expenses', 'monthlyExpenses', 'yearlyExpenses'));
    }

    public function edit(ExpenseCategory $expenseCategory)
    {
        $parentCategories = ExpenseCategory::whereNull('parent_id')
            ->where('id', '!=', $expenseCategory->id)
            ->get();
        return view('expense-categories.edit', compact('expenseCategory', 'parentCategories'));
    }

    public function update(Request $request, ExpenseCategory $expenseCategory)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:expense_categories,name,' . $expenseCategory->id,
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'parent_id' => 'nullable|exists:expense_categories,id',
            'monthly_budget' => 'nullable|numeric|min:0',
            'yearly_budget' => 'nullable|numeric|min:0',
            'color' => 'nullable|string|size:7',
            'icon' => 'nullable|image|max:1024',
            'is_fixed' => 'boolean',
            'requires_approval' => 'boolean',
            'approval_roles' => 'nullable|array',
        ]);

        if ($request->hasFile('icon')) {
            if ($expenseCategory->icon) {
                Storage::disk('public')->delete($expenseCategory->icon);
            }
            $path = $request->file('icon')->store('category-icons', 'public');
            $validated['icon'] = $path;
        }

        $expenseCategory->update($validated);

        return redirect()->route('expense-categories.index')
            ->with('success', 'تم تحديث تصنيف المصروفات بنجاح');
    }

    public function destroy(ExpenseCategory $expenseCategory)
    {
        if ($expenseCategory->expenses()->exists()) {
            return back()->with('error', 'لا يمكن حذف هذا التصنيف لوجود مصروفات مرتبطة به');
        }

        if ($expenseCategory->children()->exists()) {
            return back()->with('error', 'لا يمكن حذف هذا التصنيف لوجود تصنيفات فرعية مرتبطة به');
        }

        if ($expenseCategory->icon) {
            Storage::disk('public')->delete($expenseCategory->icon);
        }

        $expenseCategory->delete();

        return redirect()->route('expense-categories.index')
            ->with('success', 'تم حذف تصنيف المصروفات بنجاح');
    }

    public function export(Request $request)
    {
        $categories = ExpenseCategory::withCount('expenses')
            ->withSum('expenses', 'amount')
            ->with('parent')
            ->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="expense-categories.csv"',
        ];

        $callback = function () use ($categories) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['الاسم', 'الوصف', 'التصنيف الأب', 'الميزانية الشهرية', 'الميزانية السنوية', 'عدد المصروفات', 'إجمالي المصروفات', 'الحالة']);

            foreach ($categories as $category) {
                fputcsv($file, [
                    $category->name,
                    $category->description,
                    $category->parent ? $category->parent->name : '-',
                    $category->monthly_budget,
                    $category->yearly_budget,
                    $category->expenses_count,
                    $category->expenses_sum_amount,
                    $category->is_active ? 'نشط' : 'غير نشط'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'categories' => 'required|array',
            'categories.*' => 'exists:expense_categories,id'
        ]);

        $categories = ExpenseCategory::whereIn('id', $request->categories);

        switch ($request->action) {
            case 'activate':
                $categories->update(['is_active' => true]);
                $message = 'تم تفعيل التصنيفات المحددة بنجاح';
                break;
            case 'deactivate':
                $categories->update(['is_active' => false]);
                $message = 'تم إيقاف التصنيفات المحددة بنجاح';
                break;
            case 'delete':
                $categories->each(function ($category) {
                    if (!$category->expenses()->exists() && !$category->children()->exists()) {
                        if ($category->icon) {
                            Storage::disk('public')->delete($category->icon);
                        }
                        $category->delete();
                    }
                });
                $message = 'تم حذف التصنيفات المحددة بنجاح';
                break;
        }

        return redirect()->route('expense-categories.index')
            ->with('success', $message);
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\InventoryTransfer;
use App\Models\Store;
use App\Models\Branch;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InventoryTransferReportController extends Controller
{
    /**
     * Display inventory transfer analytics dashboard.
     */
    public function index(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        $status = $request->get('status');
        $fromLocationId = $request->get('from_location_id');
        $toLocationId = $request->get('to_location_id');

        // Base query for transfers in date range
        $transfersQuery = InventoryTransfer::with(['fromStore', 'fromBranch', 'toStore', 'toBranch', 'user'])
            ->whereBetween('created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59']);

        if ($status) {
            $transfersQuery->where('status', $status);
        }

        if ($fromLocationId) {
            $transfersQuery->where(function ($query) use ($fromLocationId) {
                $query->where('from_store_id', $fromLocationId)
                      ->orWhere('from_branch_id', $fromLocationId);
            });
        }

        if ($toLocationId) {
            $transfersQuery->where(function ($query) use ($toLocationId) {
                $query->where('to_store_id', $toLocationId)
                      ->orWhere('to_branch_id', $toLocationId);
            });
        }

        // Statistics
        $totalTransfers = $transfersQuery->count();
        $pendingTransfers = (clone $transfersQuery)->where('status', 'pending')->count();
        $approvedTransfers = (clone $transfersQuery)->where('status', 'approved')->count();
        $shippedTransfers = (clone $transfersQuery)->where('status', 'shipped')->count();
        $receivedTransfers = (clone $transfersQuery)->where('status', 'received')->count();
        $cancelledTransfers = (clone $transfersQuery)->where('status', 'cancelled')->count();

        // Recent transfers
        $recentTransfers = (clone $transfersQuery)->orderByDesc('created_at')->limit(10)->get();

        // Transfer trends by status (last 6 months)
        $statusTrends = InventoryTransfer::select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                'status',
                DB::raw('COUNT(*) as transfer_count')
            )
            ->where('created_at', '>=', now()->subMonths(6))
            ->groupBy('month', 'status')
            ->orderBy('month')
            ->get()
            ->groupBy('month');

        // Most transferred products
        $topProducts = DB::table('inventory_transfer_items')
            ->join('inventory_transfers', 'inventory_transfer_items.inventory_transfer_id', '=', 'inventory_transfers.id')
            ->join('products', 'inventory_transfer_items.product_id', '=', 'products.id')
            ->whereBetween('inventory_transfers.created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59'])
            ->select('products.name', 'products.id', DB::raw('SUM(inventory_transfer_items.quantity) as total_quantity'))
            ->groupBy('products.id', 'products.name')
            ->orderByDesc('total_quantity')
            ->limit(10)
            ->get();

        // Get all locations for filters
        $stores = Store::orderBy('name')->get();
        $branches = Branch::orderBy('name')->get();

        return view('inventory-transfer-reports.index', compact(
            'totalTransfers',
            'pendingTransfers',
            'approvedTransfers',
            'shippedTransfers',
            'receivedTransfers',
            'cancelledTransfers',
            'recentTransfers',
            'statusTrends',
            'topProducts',
            'stores',
            'branches',
            'dateFrom',
            'dateTo',
            'status',
            'fromLocationId',
            'toLocationId'
        ));
    }

    /**
     * Display detailed transfer history report.
     */
    public function transferHistory(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        $status = $request->get('status');
        $fromLocationId = $request->get('from_location_id');
        $toLocationId = $request->get('to_location_id');

        $query = InventoryTransfer::with(['fromStore', 'fromBranch', 'toStore', 'toBranch', 'user', 'items.product'])
            ->whereBetween('created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59']);

        if ($status) {
            $query->where('status', $status);
        }

        if ($fromLocationId) {
            $query->where(function ($q) use ($fromLocationId) {
                $q->where('from_store_id', $fromLocationId)
                  ->orWhere('from_branch_id', $fromLocationId);
            });
        }

        if ($toLocationId) {
            $query->where(function ($q) use ($toLocationId) {
                $q->where('to_store_id', $toLocationId)
                  ->orWhere('to_branch_id', $toLocationId);
            });
        }

        $transfers = $query->orderByDesc('created_at')->paginate(20);
        $stores = Store::orderBy('name')->get();
        $branches = Branch::orderBy('name')->get();

        return view('inventory-transfer-reports.transfer-history', compact(
            'transfers',
            'stores',
            'branches',
            'dateFrom',
            'dateTo',
            'status',
            'fromLocationId',
            'toLocationId'
        ));
    }

    /**
     * Display product movement tracking report.
     */
    public function productMovements(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        $productId = $request->get('product_id');

        $query = DB::table('inventory_transfer_items')
            ->join('inventory_transfers', 'inventory_transfer_items.inventory_transfer_id', '=', 'inventory_transfers.id')
            ->join('products', 'inventory_transfer_items.product_id', '=', 'products.id')
            ->leftJoin('stores as from_stores', 'inventory_transfers.from_store_id', '=', 'from_stores.id')
            ->leftJoin('branches as from_branches', 'inventory_transfers.from_branch_id', '=', 'from_branches.id')
            ->leftJoin('stores as to_stores', 'inventory_transfers.to_store_id', '=', 'to_stores.id')
            ->leftJoin('branches as to_branches', 'inventory_transfers.to_branch_id', '=', 'to_branches.id')
            ->whereBetween('inventory_transfers.created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59'])
            ->select(
                'products.name as product_name',
                'products.id as product_id',
                'inventory_transfer_items.quantity',
                'inventory_transfers.status',
                'inventory_transfers.created_at',
                'from_stores.name as from_store_name',
                'from_branches.name as from_branch_name',
                'to_stores.name as to_store_name',
                'to_branches.name as to_branch_name',
                'inventory_transfers.id as transfer_id'
            );

        if ($productId) {
            $query->where('products.id', $productId);
        }

        $movements = $query->orderByDesc('inventory_transfers.created_at')->paginate(20);

        // Product summary
        $productSummary = DB::table('inventory_transfer_items')
            ->join('inventory_transfers', 'inventory_transfer_items.inventory_transfer_id', '=', 'inventory_transfers.id')
            ->join('products', 'inventory_transfer_items.product_id', '=', 'products.id')
            ->whereBetween('inventory_transfers.created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59'])
            ->when($productId, function ($query) use ($productId) {
                return $query->where('products.id', $productId);
            })
            ->select(
                'products.name',
                'products.id',
                DB::raw('SUM(inventory_transfer_items.quantity) as total_moved'),
                DB::raw('COUNT(DISTINCT inventory_transfers.id) as transfer_count')
            )
            ->groupBy('products.id', 'products.name')
            ->orderByDesc('total_moved')
            ->get();

        $products = Product::orderBy('name')->get();

        return view('inventory-transfer-reports.product-movements', compact(
            'movements',
            'productSummary',
            'products',
            'dateFrom',
            'dateTo',
            'productId'
        ));
    }

    /**
     * Display location performance report.
     */
    public function locationPerformance(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // Store performance
        $storePerformance = DB::table('inventory_transfers')
            ->leftJoin('stores as from_stores', 'inventory_transfers.from_store_id', '=', 'from_stores.id')
            ->leftJoin('stores as to_stores', 'inventory_transfers.to_store_id', '=', 'to_stores.id')
            ->whereBetween('created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59'])
            ->select(
                DB::raw('COALESCE(from_stores.name, to_stores.name) as store_name'),
                DB::raw('COALESCE(from_stores.id, to_stores.id) as store_id'),
                DB::raw('SUM(CASE WHEN from_store_id IS NOT NULL THEN 1 ELSE 0 END) as outgoing_transfers'),
                DB::raw('SUM(CASE WHEN to_store_id IS NOT NULL THEN 1 ELSE 0 END) as incoming_transfers'),
                DB::raw('COUNT(*) as total_transfers')
            )
            ->whereNotNull(DB::raw('COALESCE(from_stores.id, to_stores.id)'))
            ->groupBy('store_name', 'store_id')
            ->orderByDesc('total_transfers')
            ->get();

        // Branch performance
        $branchPerformance = DB::table('inventory_transfers')
            ->leftJoin('branches as from_branches', 'inventory_transfers.from_branch_id', '=', 'from_branches.id')
            ->leftJoin('branches as to_branches', 'inventory_transfers.to_branch_id', '=', 'to_branches.id')
            ->whereBetween('created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59'])
            ->select(
                DB::raw('COALESCE(from_branches.name, to_branches.name) as branch_name'),
                DB::raw('COALESCE(from_branches.id, to_branches.id) as branch_id'),
                DB::raw('SUM(CASE WHEN from_branch_id IS NOT NULL THEN 1 ELSE 0 END) as outgoing_transfers'),
                DB::raw('SUM(CASE WHEN to_branch_id IS NOT NULL THEN 1 ELSE 0 END) as incoming_transfers'),
                DB::raw('COUNT(*) as total_transfers')
            )
            ->whereNotNull(DB::raw('COALESCE(from_branches.id, to_branches.id)'))
            ->groupBy('branch_name', 'branch_id')
            ->orderByDesc('total_transfers')
            ->get();

        return view('inventory-transfer-reports.location-performance', compact(
            'storePerformance',
            'branchPerformance',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * Export transfer data to CSV.
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'transfers');
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $filename = "inventory_transfer_report_{$type}_" . now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($type, $dateFrom, $dateTo) {
            $file = fopen('php://output', 'w');

            if ($type === 'transfers') {
                fputcsv($file, ['رقم النقل', 'من موقع', 'إلى موقع', 'الحالة', 'تاريخ الإنشاء', 'المستخدم']);

                InventoryTransfer::with(['fromStore', 'fromBranch', 'toStore', 'toBranch', 'user'])
                    ->whereBetween('created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59'])
                    ->chunk(100, function ($transfers) use ($file) {
                        foreach ($transfers as $transfer) {
                            $fromLocation = $transfer->fromStore ? $transfer->fromStore->name : $transfer->fromBranch->name;
                            $toLocation = $transfer->toStore ? $transfer->toStore->name : $transfer->toBranch->name;

                            fputcsv($file, [
                                $transfer->id,
                                $fromLocation,
                                $toLocation,
                                $transfer->status,
                                $transfer->created_at->format('Y-m-d H:i'),
                                $transfer->user->name ?? 'غير محدد'
                            ]);
                        }
                    });
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}

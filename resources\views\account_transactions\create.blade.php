<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">إضافة معاملة مالية جديدة</h2>
                    <a href="{{ route('account-transactions.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-plus text-primary"></i> بيانات المعاملة الجديدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('account-transactions.store') }}" method="POST">
                            @csrf

                            <div class="row">
                                <!-- Account Selection -->
                                <div class="col-md-6 mb-3">
                                    <label for="account_id" class="form-label">الحساب <span
                                            class="text-danger">*</span></label>
                                    <select name="account_id" id="account_id"
                                        class="form-select @error('account_id') is-invalid @enderror" required>
                                        <option value="">اختر الحساب</option>
                                        @foreach ($accounts as $acc)
                                            <option value="{{ $acc->id }}"
                                                {{ old('account_id', $selectedAccount?->id) == $acc->id ? 'selected' : '' }}>
                                                {{ $acc->name }} ({{ $acc->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('account_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Transaction Type -->
                                <div class="col-md-6 mb-3">
                                    <label for="type" class="form-label">نوع المعاملة <span
                                            class="text-danger">*</span></label>
                                    <select name="type" id="type"
                                        class="form-select @error('type') is-invalid @enderror" required>
                                        <option value="">اختر نوع المعاملة</option>
                                        <option value="debit" {{ old('type') == 'debit' ? 'selected' : '' }}>مدين (خصم)
                                        </option>
                                        <option value="credit" {{ old('type') == 'credit' ? 'selected' : '' }}>دائن
                                            (إضافة)</option>
                                        <option value="transfer" {{ old('type') == 'transfer' ? 'selected' : '' }}>تحويل
                                        </option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <!-- Amount -->
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">المبلغ <span
                                            class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" min="0" name="amount"
                                            id="amount" class="form-control @error('amount') is-invalid @enderror"
                                            value="{{ old('amount') }}" required>
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                    @error('amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Reference Number -->
                                <div class="col-md-6 mb-3">
                                    <label for="reference_number" class="form-label">رقم المرجع</label>
                                    <input type="text" name="reference_number" id="reference_number"
                                        class="form-control @error('reference_number') is-invalid @enderror"
                                        value="{{ old('reference_number') }}">
                                    @error('reference_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">رقم مرجعي للمعاملة (اختياري)</div>
                                </div>
                            </div>

                            <!-- Transfer To Account (shown only for transfer type) -->
                            <div class="row" id="transfer-section" style="display: none;">
                                <div class="col-md-12 mb-3">
                                    <label for="to_account_id" class="form-label">تحويل إلى الحساب</label>
                                    <select name="to_account_id" id="to_account_id"
                                        class="form-select @error('to_account_id') is-invalid @enderror">
                                        <option value="">اختر الحساب المحول إليه</option>
                                        @foreach ($accounts as $acc)
                                            <option value="{{ $acc->id }}"
                                                {{ old('to_account_id') == $acc->id ? 'selected' : '' }}>
                                                {{ $acc->name }} ({{ $acc->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('to_account_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <textarea name="description" id="description" rows="3"
                                    class="form-control @error('description') is-invalid @enderror">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Submit Buttons -->
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('account-transactions.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ المعاملة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Transfer Section -->
    <script>
        document.getElementById('type').addEventListener('change', function() {
            const transferSection = document.getElementById('transfer-section');
            if (this.value === 'transfer') {
                transferSection.style.display = 'block';
                document.getElementById('to_account_id').required = true;
            } else {
                transferSection.style.display = 'none';
                document.getElementById('to_account_id').required = false;
                document.getElementById('to_account_id').value = '';
            }
        });
    </script>
</x-app-layout>

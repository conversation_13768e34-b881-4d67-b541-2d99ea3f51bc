<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stores', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['branch_id']);

            // Make branch_id nullable to allow independent stores
            $table->foreignId('branch_id')->nullable()->change();

            // Re-add the foreign key constraint as nullable
            $table->foreign('branch_id')->references('id')->on('branches')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stores', function (Blueprint $table) {
            // Drop the nullable foreign key
            $table->dropForeign(['branch_id']);

            // Make branch_id required again
            $table->foreignId('branch_id')->change();

            // Re-add the original foreign key constraint
            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();
        });
    }
};

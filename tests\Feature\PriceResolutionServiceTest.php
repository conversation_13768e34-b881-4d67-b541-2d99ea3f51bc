<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Product;
use App\Models\Branch;
use App\Models\BranchInventory;
use App\Models\User;
use App\Services\PriceResolutionService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PriceResolutionServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $priceResolutionService;
    protected $product;
    protected $branch;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->priceResolutionService = app(PriceResolutionService::class);
        
        // Create test data
        $this->branch = Branch::factory()->create();
        $this->product = Product::factory()->create([
            'price' => 100.00,
            'selling_price' => 120.00,
            'cost_price' => 80.00
        ]);
        $this->user = User::factory()->create(['branch_id' => $this->branch->id]);
    }

    public function test_gets_branch_inventory_price_when_available()
    {
        // Create branch inventory with specific price
        BranchInventory::create([
            'branch_id' => $this->branch->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'cost_price' => 85.00,
            'sale_price_1' => 130.00
        ]);

        $priceInfo = $this->priceResolutionService->getSellingPrice($this->product, $this->branch);

        $this->assertEquals(130.00, $priceInfo['price']);
        $this->assertEquals('branch_inventory', $priceInfo['source']);
        $this->assertEquals(85.00, $priceInfo['cost_price']);
    }

    public function test_falls_back_to_product_selling_price()
    {
        $priceInfo = $this->priceResolutionService->getSellingPrice($this->product, $this->branch);

        $this->assertEquals(120.00, $priceInfo['price']);
        $this->assertEquals('product_selling_price', $priceInfo['source']);
        $this->assertEquals(80.00, $priceInfo['cost_price']);
    }

    public function test_falls_back_to_product_price()
    {
        $this->product->update(['selling_price' => null]);
        
        $priceInfo = $this->priceResolutionService->getSellingPrice($this->product, $this->branch);

        $this->assertEquals(100.00, $priceInfo['price']);
        $this->assertEquals('product_price', $priceInfo['source']);
    }

    public function test_calculates_cost_plus_price_when_no_prices_available()
    {
        $this->product->update(['price' => null, 'selling_price' => null]);
        
        $priceInfo = $this->priceResolutionService->getSellingPrice($this->product, $this->branch);

        $expectedPrice = 80.00 * 1.30; // 30% markup
        $this->assertEquals($expectedPrice, $priceInfo['price']);
        $this->assertEquals('cost_plus_calculated', $priceInfo['source']);
    }

    public function test_validates_price_above_cost()
    {
        $validation = $this->priceResolutionService->validateSellingPrice(
            90.00, // Above cost price of 80
            $this->product,
            $this->branch,
            $this->user
        );

        $this->assertTrue($validation['valid']);
        $this->assertEquals('السعر مقبول', $validation['message']);
    }

    public function test_rejects_price_below_cost_for_non_admin()
    {
        $validation = $this->priceResolutionService->validateSellingPrice(
            70.00, // Below cost price of 80
            $this->product,
            $this->branch,
            $this->user
        );

        $this->assertFalse($validation['valid']);
        $this->assertEquals('لا يمكن البيع بسعر أقل من سعر التكلفة', $validation['message']);
    }

    public function test_allows_price_below_cost_for_admin()
    {
        $admin = User::factory()->create(['branch_id' => $this->branch->id]);
        $admin->assignRole('admin');

        $validation = $this->priceResolutionService->validateSellingPrice(
            70.00, // Below cost price of 80
            $this->product,
            $this->branch,
            $admin
        );

        $this->assertTrue($validation['valid']);
        $this->assertContains('تحذير: البيع بسعر أقل من سعر التكلفة', $validation['warnings']);
    }

    public function test_warns_about_low_profit_margin()
    {
        $validation = $this->priceResolutionService->validateSellingPrice(
            82.00, // Very low margin (2.5%)
            $this->product,
            $this->branch,
            $this->user
        );

        $this->assertTrue($validation['valid']);
        $this->assertContains('تحذير: هامش الربح منخفض جداً', $validation['warnings']);
    }

    public function test_warns_about_significant_price_deviation()
    {
        $validation = $this->priceResolutionService->validateSellingPrice(
            200.00, // 67% higher than selling price of 120
            $this->product,
            $this->branch,
            $this->user
        );

        $this->assertTrue($validation['valid']);
        $this->assertContains('تحذير: السعر يختلف بشكل كبير عن السعر المعتاد', $validation['warnings']);
    }

    public function test_updates_branch_price()
    {
        $result = $this->priceResolutionService->updateBranchPrice(
            $this->product,
            $this->branch,
            150.00,
            1,
            $this->user
        );

        $this->assertTrue($result);

        $branchInventory = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();

        $this->assertEquals(150.00, $branchInventory->sale_price_1);
    }

    public function test_calculates_sale_total_with_validation()
    {
        $items = [
            [
                'product_id' => $this->product->id,
                'quantity' => 2,
                'price' => 120.00
            ]
        ];

        $result = $this->priceResolutionService->calculateSaleTotal(
            $items,
            $this->branch,
            $this->user
        );

        $this->assertEquals(240.00, $result['total']);
        $this->assertCount(1, $result['items']);
        $this->assertEquals(240.00, $result['items'][0]['subtotal']);
    }

    public function test_rejects_invalid_price_in_sale_calculation()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('لا يمكن البيع بسعر أقل من سعر التكلفة');

        $items = [
            [
                'product_id' => $this->product->id,
                'quantity' => 1,
                'price' => 70.00 // Below cost price
            ]
        ];

        $this->priceResolutionService->calculateSaleTotal(
            $items,
            $this->branch,
            $this->user
        );
    }

    public function test_gets_price_suggestions()
    {
        $suggestions = $this->priceResolutionService->getPriceSuggestions(
            $this->product,
            $this->branch
        );

        $this->assertArrayHasKey('standard', $suggestions);
        $this->assertEquals(120.00, $suggestions['standard']['price']);
        
        // Should have margin suggestions
        $this->assertArrayHasKey('margin_20', $suggestions);
        $this->assertArrayHasKey('margin_30', $suggestions);
        $this->assertArrayHasKey('margin_50', $suggestions);
    }

    public function test_bulk_update_branch_prices()
    {
        $product2 = Product::factory()->create(['cost_price' => 50.00]);
        
        $updates = [
            [
                'product_id' => $this->product->id,
                'price' => 140.00,
                'price_level' => 1
            ],
            [
                'product_id' => $product2->id,
                'price' => 70.00,
                'price_level' => 1
            ]
        ];

        $result = $this->priceResolutionService->bulkUpdateBranchPrices(
            $updates,
            $this->branch,
            $this->user
        );

        $this->assertEquals(2, $result['success']);
        $this->assertEmpty($result['failed']);

        // Verify prices were updated
        $inventory1 = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();
        $this->assertEquals(140.00, $inventory1->sale_price_1);

        $inventory2 = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $product2->id)
            ->first();
        $this->assertEquals(70.00, $inventory2->sale_price_1);
    }
}

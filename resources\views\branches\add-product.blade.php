<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-plus text-primary"></i> {{ __('إضافة منتجات للفرع') }}
                </h2>
                <p class="text-muted small mb-0">إضافة منتجات جديدة لمخزون فرع: {{ $branch->name }}</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('branches.sales-inventory', $branch) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للمخزون
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid px-4">


        <!-- Display Validation Errors -->
        @if ($errors->any())
            <div class="alert alert-danger mb-4">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>حدثت الأخطاء التالية:</h6>
                <ul class="mb-0">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Display Success Messages -->
        @if (session('success'))
            <div class="alert alert-success mb-4">
                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            </div>
        @endif

        <!-- Breadcrumb -->
        <div class="row align-items-center mb-4">
            <div class="col-md-8">
                <nav aria-label="breadcrumb" class="d-flex justify-content-start">
                    <ol class="breadcrumb mb-0 bg-light px-3 py-2 rounded">
                        <li class="breadcrumb-item">
                            <a href="{{ user_route('dashboard') }}" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ user_route('branches.index') }}" class="text-decoration-none">
                                <i class="fas fa-building me-1"></i>الفروع
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ user_route('branches.show', $branch) }}" class="text-decoration-none">
                                <i class="fas fa-building me-1"></i>{{ Str::limit($branch->name, 20) }}
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ user_route('branches.sales-inventory', $branch) }}"
                                class="text-decoration-none">
                                <i class="fas fa-boxes me-1"></i>المخزون
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-plus me-1"></i>إضافة منتجات
                        </li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-info fs-6">
                    <i class="fas fa-building me-1"></i>{{ $branch->name }}
                </span>
            </div>
        </div>

        @if ($products->count() > 0)
            <!-- Add Products Form -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow border-0">
                        <div class="card-header bg-gradient-primary text-white py-3">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-plus fa-lg me-3"></i>
                                    <h5 class="mb-0 fw-bold">اختيار المنتجات للإضافة</h5>
                                </div>
                                <div class="text-white">
                                    <span id="selectedCount" class="badge bg-warning text-dark fs-6">0 منتج محدد</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-4">
                            <form action="{{ user_route('branches.store-product', $branch) }}" method="POST"
                                id="addProductsForm">
                                @csrf

                                <!-- Search and Filter Controls -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-search text-muted"></i>
                                            </span>
                                            <input type="text" class="form-control" id="productSearch"
                                                placeholder="البحث في المنتجات...">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <select class="form-select" id="categoryFilter">
                                            <option value="">جميع الفئات</option>
                                            @foreach ($categories as $category)
                                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-outline-secondary w-100"
                                            onclick="clearFilters()">
                                            <i class="fas fa-times"></i> مسح
                                        </button>
                                    </div>
                                </div>

                                <!-- Products Grid -->
                                <div class="row" id="productsGrid">
                                    @foreach ($products as $product)
                                        <div class="col-lg-4 col-md-6 mb-4 product-item"
                                            data-name="{{ strtolower($product->name) }}"
                                            data-category="{{ $product->category_id ?? '' }}">
                                            <div class="card h-100 border-0 shadow-sm">
                                                <div class="card-body p-3">
                                                    <!-- Product Header -->
                                                    <div class="d-flex align-items-start mb-3">
                                                        <div class="form-check me-3">
                                                            <input class="form-check-input product-checkbox"
                                                                type="checkbox" id="product{{ $product->id }}"
                                                                data-product-id="{{ $product->id }}"
                                                                onchange="toggleProductFields(this)">
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <label class="form-check-label fw-bold text-primary"
                                                                for="product{{ $product->id }}">
                                                                {{ $product->name }}
                                                            </label>
                                                            <p class="text-muted small mb-1">
                                                                <i
                                                                    class="fas fa-tag me-1"></i>{{ $product->category->name ?? 'بدون فئة' }}
                                                            </p>
                                                            @if ($product->sku)
                                                                <p class="text-muted small mb-0">
                                                                    <i
                                                                        class="fas fa-barcode me-1"></i>{{ $product->sku }}
                                                                </p>
                                                            @endif
                                                        </div>
                                                    </div>

                                                    <!-- Product Fields -->
                                                    <div class="product-fields" id="fields{{ $product->id }}"
                                                        style="display: none;">
                                                        <hr>
                                                        <div class="row g-2">
                                                            <!-- Quantity -->
                                                            <div class="col-6">
                                                                <label class="form-label small fw-bold">الكمية <span
                                                                        class="text-danger">*</span></label>
                                                                <input type="number" step="0.01" min="0"
                                                                    class="form-control form-control-sm"
                                                                    name="products[{{ $product->id }}][quantity]"
                                                                    placeholder="0" disabled>
                                                                <input type="hidden"
                                                                    name="products[{{ $product->id }}][product_id]"
                                                                    value="{{ $product->id }}" disabled>
                                                            </div>
                                                            <!-- Cost Price -->
                                                            <div class="col-6">
                                                                <label class="form-label small fw-bold">سعر التكلفة
                                                                    <span class="text-danger">*</span></label>
                                                                <input type="number" step="0.01" min="0"
                                                                    class="form-control form-control-sm"
                                                                    name="products[{{ $product->id }}][cost_price]"
                                                                    value="{{ $product->price ?? '' }}"
                                                                    placeholder="0.00" disabled>
                                                            </div>
                                                            <!-- Sale Price 1 -->
                                                            <div class="col-6">
                                                                <label class="form-label small fw-bold">سعر البيع
                                                                    1</label>
                                                                <input type="number" step="0.01" min="0"
                                                                    class="form-control form-control-sm"
                                                                    name="products[{{ $product->id }}][sale_price_1]"
                                                                    value="{{ $product->selling_price ?? '' }}"
                                                                    placeholder="0.00" disabled>
                                                            </div>
                                                            <!-- Sale Price 2 -->
                                                            <div class="col-6">
                                                                <label class="form-label small fw-bold">سعر البيع
                                                                    2</label>
                                                                <input type="number" step="0.01" min="0"
                                                                    class="form-control form-control-sm"
                                                                    name="products[{{ $product->id }}][sale_price_2]"
                                                                    placeholder="0.00" disabled>
                                                            </div>
                                                            <!-- Sale Price 3 -->
                                                            <div class="col-12">
                                                                <label class="form-label small fw-bold">سعر البيع
                                                                    3</label>
                                                                <input type="number" step="0.01" min="0"
                                                                    class="form-control form-control-sm"
                                                                    name="products[{{ $product->id }}][sale_price_3]"
                                                                    placeholder="0.00" disabled>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <!-- Submit Section -->
                                <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                                    <div class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        اختر المنتجات وحدد الكميات والأسعار المطلوبة
                                    </div>
                                    <div class="d-flex gap-2">
                                        <a href="{{ user_route('branches.sales-inventory', $branch) }}"
                                            class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-success" id="submitBtn" disabled>
                                            <i class="fas fa-save me-2"></i>إضافة المنتجات المحددة
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <!-- No Products Available -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow border-0">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-box-open fa-4x text-muted mb-4"></i>
                            <h4 class="text-muted mb-3">جميع المنتجات موجودة في المخزون</h4>
                            <p class="text-muted mb-4">
                                جميع المنتجات النشطة موجودة بالفعل في مخزون هذا الفرع.
                                <br>يمكنك إنشاء منتجات جديدة أو إدارة المخزون الحالي.
                            </p>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="{{ user_route('products.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>إنشاء منتج جديد
                                </a>
                                <a href="{{ user_route('branches.sales-inventory', $branch) }}"
                                    class="btn btn-outline-primary">
                                    <i class="fas fa-warehouse me-2"></i>عرض المخزون الحالي
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</x-app-layout>

@push('styles')
    <style>
        .bg-gradient-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .product-item {
            transition: transform 0.2s ease-in-out;
        }

        .product-item:hover {
            transform: translateY(-2px);
        }

        .form-check-input:checked~.card {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .product-fields {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
@endpush

<!-- Inline Script for Global Functions -->
<script>
    // Global functions - defined immediately
    window.toggleProductFields = function(checkbox) {
        console.log('toggleProductFields called!', checkbox);

        const productId = checkbox.getAttribute('data-product-id');
        console.log('Product ID:', productId);

        const fields = document.getElementById('fields' + productId);
        console.log('Fields element:', fields);

        if (!fields) {
            console.error('Fields element not found for ID: fields' + productId);
            return;
        }

        if (checkbox.checked) {
            console.log('Showing fields...');
            fields.style.display = 'block';
            // Enable all inputs and set required attributes for quantity and cost price
            fields.querySelectorAll('input').forEach(function(input) {
                input.disabled = false;
                if (input.name && (input.name.includes('[quantity]') || input.name.includes(
                        '[cost_price]'))) {
                    input.required = true;
                }
            });
        } else {
            console.log('Hiding fields...');
            fields.style.display = 'none';
            // Disable all inputs, remove required and clear values
            fields.querySelectorAll('input').forEach(function(input) {
                input.disabled = true;
                input.required = false;
                if (input.type === 'number') {
                    input.value = '';
                }
            });
        }

        window.updateSelectedCount();
    };

    window.updateSelectedCount = function() {
        const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;
        const selectedCount = document.getElementById('selectedCount');
        const submitBtn = document.getElementById('submitBtn');

        if (!selectedCount || !submitBtn) {
            console.log('selectedCount or submitBtn not found');
            return;
        }

        if (checkedCount === 0) {
            selectedCount.textContent = '0 منتج محدد';
            selectedCount.className = 'badge bg-warning text-dark fs-6';
            submitBtn.disabled = true;
        } else {
            selectedCount.textContent = checkedCount + ' منتج محدد';
            selectedCount.className = 'badge bg-success text-white fs-6';
            submitBtn.disabled = false;
        }
    };
</script>

@push('scripts')
    <script>
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            // Set up checkbox event listeners (backup)
            document.querySelectorAll('.product-checkbox').forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    toggleProductFields(this);
                });
            });

            // Get elements for search and filter
            const productSearch = document.getElementById('productSearch');
            const categoryFilter = document.getElementById('categoryFilter');
            const productItems = document.querySelectorAll('.product-item');

            // Search functionality
            if (productSearch) {
                productSearch.addEventListener('input', function() {
                    filterProducts();
                });
            }

            // Category filter
            if (categoryFilter) {
                categoryFilter.addEventListener('change', function() {
                    filterProducts();
                });
            }

            // Filter products based on search and category
            function filterProducts() {
                const searchTerm = productSearch ? productSearch.value.toLowerCase() : '';
                const selectedCategory = categoryFilter ? categoryFilter.value : '';

                productItems.forEach(item => {
                    const productName = item.dataset.name || '';
                    const productCategory = item.dataset.category || '';

                    const matchesSearch = !searchTerm || productName.includes(searchTerm);
                    const matchesCategory = !selectedCategory || productCategory === selectedCategory;

                    if (matchesSearch && matchesCategory) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            }

            // Clear filters
            window.clearFilters = function() {
                if (productSearch) productSearch.value = '';
                if (categoryFilter) categoryFilter.value = '';
                filterProducts();
            };

            // Form validation and cleanup
            const form = document.getElementById('addProductsForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');

                    if (checkedBoxes.length === 0) {
                        e.preventDefault();
                        alert('يرجى اختيار منتج واحد على الأقل');
                        return false;
                    }

                    // Validate checked products have required fields
                    let hasValidationErrors = false;
                    let errorMessages = [];

                    checkedBoxes.forEach(function(checkbox) {
                        const productId = checkbox.getAttribute('data-product-id');
                        const productLabel = checkbox.closest('.card').querySelector(
                            '.form-check-label');
                        const productName = productLabel ? productLabel.textContent.trim() :
                            `المنتج رقم ${productId}`;

                        const quantityInput = form.querySelector(
                            `input[name="products[${productId}][quantity]"]:not([disabled])`);
                        const costPriceInput = form.querySelector(
                            `input[name="products[${productId}][cost_price]"]:not([disabled])`);

                        // Check if inputs exist and have valid values
                        if (!quantityInput || !quantityInput.value || parseFloat(quantityInput
                                .value) <= 0) {
                            hasValidationErrors = true;
                            errorMessages.push(`يرجى إدخال كمية صحيحة لـ ${productName}`);
                        }

                        if (!costPriceInput || !costPriceInput.value || parseFloat(costPriceInput
                                .value) <= 0) {
                            hasValidationErrors = true;
                            errorMessages.push(`يرجى إدخال سعر تكلفة صحيح لـ ${productName}`);
                        }
                    });

                    if (hasValidationErrors) {
                        e.preventDefault();
                        alert('حدثت الأخطاء التالية:\n\n' + errorMessages.join('\n'));
                        return false;
                    }

                    // Show loading state
                    const submitBtn = document.getElementById('submitBtn');
                    if (submitBtn) {
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
                        submitBtn.disabled = true;
                    }

                    // Allow normal form submission to proceed
                    return true;
                });
            }

            // Auto-calculate sale prices based on cost price (simplified)
            document.addEventListener('input', function(e) {
                if (e.target.name && e.target.name.includes('[cost_price]')) {
                    const costPrice = parseFloat(e.target.value) || 0;
                    const productIdMatch = e.target.name.match(/\[(\d+)\]/);

                    if (productIdMatch) {
                        const productId = productIdMatch[1];
                        const salePrice1Input = document.querySelector(
                            `input[name="products[${productId}][sale_price_1]"]`);

                        if (salePrice1Input && costPrice > 0 && !salePrice1Input.value) {
                            // Suggest 30% markup
                            const suggestedPrice = (costPrice * 1.3).toFixed(2);
                            salePrice1Input.placeholder = `مقترح: ${suggestedPrice}`;
                        }
                    }
                }
            });
        });
    </script>
@endpush

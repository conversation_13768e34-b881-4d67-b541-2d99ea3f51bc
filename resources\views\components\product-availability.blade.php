@props(['product', 'showTransferButton' => false, 'compact' => false])

<div class="product-availability {{ $compact ? 'compact' : '' }}">
    <!-- Main availability status -->
    <div class="availability-status mb-2">
        <span class="badge bg-{{ $product->availability_class }} {{ $compact ? 'small' : '' }}">
            <i
                class="fas {{ $product->availability_status === 'local' ? 'fa-check' : ($product->availability_status === 'transferable' ? 'fa-exchange-alt' : 'fa-times') }}"></i>
            {{ $product->availability_text }}
        </span>
    </div>

    @if (!$compact)
        <!-- Detailed availability breakdown -->
        <div class="availability-details">
            <!-- Local availability -->
            @if ($product->local_quantity > 0)
                <div class="location-availability local mb-1">
                    <small class="text-success">
                        <i class="fas fa-map-marker-alt"></i>
                        <strong>محلياً:</strong> {{ $product->local_quantity }} قطعة
                    </small>
                </div>
            @endif

            <!-- Available locations -->
            @if ($product->all_available_locations && $product->all_available_locations->count() > 0)
                <div class="available-locations mb-1">
                    <small class="text-warning">
                        <i class="fas fa-map-marker-alt"></i>
                        <strong>متوفر في:</strong>
                    </small>
                    <div class="ms-3">
                        @foreach ($product->all_available_locations as $location)
                            <div class="small text-muted d-flex justify-content-between align-items-center">
                                <span>
                                    @if ($location['location_type'] === 'branch')
                                        <i class="fas fa-building text-primary"></i>
                                    @else
                                        <i class="fas fa-warehouse text-info"></i>
                                    @endif
                                    {{ $location['location_display'] }}
                                </span>
                                <span class="badge bg-light text-dark">{{ $location['quantity'] }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Total quantity -->
            @if ($product->total_quantity > 0)
                <div class="total-availability">
                    <small class="text-primary">
                        <i class="fas fa-calculator"></i>
                        <strong>الإجمالي:</strong> {{ $product->total_quantity }} قطعة
                    </small>
                </div>
            @endif
        </div>

        <!-- Transfer button -->
        @if ($showTransferButton && $product->availability_status === 'transferable' && $product->local_quantity == 0)
            <div class="transfer-action mt-2">
                <button class="btn btn-sm btn-outline-warning" onclick="requestTransfer({{ $product->id }}, 1)"
                    title="طلب نقل من موقع آخر">
                    <i class="fas fa-exchange-alt"></i>
                    طلب نقل
                </button>
            </div>
        @endif
    @else
        <!-- Compact view - just show quantities -->
        <div class="availability-compact">
            @if ($product->local_quantity > 0)
                <small class="text-success">محلياً: {{ $product->local_quantity }}</small>
            @endif
            @if ($product->availability_status === 'transferable')
                <small class="text-warning">| إجمالي: {{ $product->total_quantity }}</small>
            @endif
        </div>
    @endif
</div>

@if (!$compact)
    <style>
        .product-availability {
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 0.75rem;
            background-color: #f8f9fa;
        }

        .product-availability.compact {
            border: none;
            padding: 0.25rem;
            background-color: transparent;
        }

        .location-availability.local {
            border-left: 3px solid #198754;
            padding-left: 0.5rem;
        }

        .other-branches-availability {
            border-left: 3px solid #ffc107;
            padding-left: 0.5rem;
        }

        .stores-availability {
            border-left: 3px solid #0dcaf0;
            padding-left: 0.5rem;
        }

        .total-availability {
            border-top: 1px solid #dee2e6;
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }
    </style>
@endif

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('expense_categories', function (Blueprint $table) {
            // $table->foreignId('parent_id')->nullable()->constrained('expense_categories')->onDelete('set null');
            // $table->decimal('monthly_budget', 10, 2)->nullable();
            // $table->decimal('yearly_budget', 10, 2)->nullable();
            // $table->string('color', 7)->nullable();
            // $table->string('icon')->nullable();
            // $table->boolean('is_fixed')->default(false);
            // $table->boolean('requires_approval')->default(false);
            // $table->json('approval_roles')->nullable();
            // $table->timestamps();
        });
    }

    public function down()
    {
        Schema::table('expense_categories', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
            $table->dropColumn([
                'parent_id',
                'monthly_budget',
                'yearly_budget',
                'color',
                'icon',
                'is_fixed',
                'requires_approval',
                'approval_roles'
            ]);
        });
    }
};

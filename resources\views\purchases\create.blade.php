<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-plus-circle text-primary me-2"></i>
                    إنشاء عملية شراء جديدة
                </h1>
                <p class="mb-0 text-muted">إنشاء فاتورة شراء جديدة - سيتم توزيع المنتجات في الخطوة التالية</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('purchases.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المشتريات
                </a>
            </div>
        </div>

        <!-- Workflow Stepper -->
        {{-- <div class="card shadow mb-4">
            <div class="card-body">
                <div class="stepper">
                    <div class="step active">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h6>إنشاء الفاتورة</h6>
                            <small>إدخال بيانات المورد والمنتجات</small>
                        </div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h6>توزيع المنتجات</h6>
                            <small>تحديد الفروع والمخازن</small>
                        </div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h6>اكتمال العملية</h6>
                            <small>إضافة للمخزون</small>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}

        <form method="POST" action="{{ user_route('purchases.store') }}" id="purchaseForm">
            @csrf

            <!-- Basic Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="supplier_id" class="form-label fw-bold">
                                <i class="fas fa-truck text-info me-2"></i>المورد
                            </label>
                            <select class="form-select @error('supplier_id') is-invalid @enderror" id="supplier_id"
                                name="supplier_id" required>
                                <option value="">-- اختر المورد --</option>
                                @foreach ($suppliers as $supplier)
                                    <option value="{{ $supplier->id }}"
                                        data-account-id="{{ $supplier->account->id ?? '' }}"
                                        data-phone="{{ $supplier->phone ?? '' }}"
                                        data-email="{{ $supplier->email ?? '' }}"
                                        data-contact-person="{{ $supplier->contact_person ?? '' }}"
                                        data-balance="{{ $supplier->getRemainingBalance() }}"
                                        {{ old('supplier_id') == $supplier->id ? 'selected' : '' }}>
                                        {{ $supplier->name }}
                                        @if ($supplier->getRemainingBalance() != 0)
                                            ({{ number_format($supplier->getRemainingBalance(), 2) }} ج.م)
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                            @error('supplier_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>ملاحظة:</strong> سيتم تحديد مواقع توزيع المنتجات (الفروع والمخازن) في الخطوة
                                التالية بعد إنشاء الفاتورة.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="notes" class="form-label fw-bold">
                                <i class="fas fa-sticky-note text-warning me-2"></i>ملاحظات
                            </label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3"
                                placeholder="أدخل أي ملاحظات إضافية...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-boxes me-2"></i>المنتجات
                    </h6>
                    <button type="button" class="btn btn-primary btn-sm" id="addProduct">
                        <i class="fas fa-plus me-2"></i>إضافة منتج
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="productsTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="35%">المنتج</th>
                                    <th width="15%">الكمية</th>
                                    <th width="20%">سعر الشراء</th>
                                    <th width="20%">الإجمالي</th>
                                    <th width="10%">إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- Products will be added here -->
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="3" class="text-end">المجموع الكلي:</th>
                                    <th id="totalAmount">0.00 ج.م</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <div id="emptyProductsMessage" class="text-center py-5 text-muted">
                        <i class="fas fa-box-open fa-3x mb-3"></i>
                        <h5>لا توجد منتجات</h5>
                        <p>انقر على "إضافة منتج" لبدء إضافة المنتجات</p>
                    </div>
                </div>
            </div>

            <!-- Financial Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calculator me-2"></i>الملخص المالي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="total_amount" class="form-label fw-bold">
                                <i class="fas fa-coins text-info me-2"></i>المبلغ الإجمالي
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01" class="form-control bg-light" id="total_amount"
                                    name="total_amount" readonly>
                                <span class="input-group-text">{{ currency_symbol() }}</span>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="discount_amount" class="form-label fw-bold">
                                <i class="fas fa-percentage text-danger me-2"></i>الخصم
                            </label>
                            <div class="row">
                                <div class="col-4">
                                    <select class="form-select" id="discount_type" name="discount_type">
                                        <option value="amount"
                                            {{ old('discount_type', 'amount') == 'amount' ? 'selected' : '' }}>مبلغ
                                        </option>
                                        <option value="percentage"
                                            {{ old('discount_type') == 'percentage' ? 'selected' : '' }}>نسبة</option>
                                    </select>
                                </div>
                                <div class="col-8">
                                    <div class="input-group">
                                        <input type="number" step="0.01"
                                            class="form-control @error('discount_amount') is-invalid @enderror"
                                            id="discount_amount" name="discount_amount"
                                            value="{{ old('discount_amount', 0) }}" min="0">
                                        <span class="input-group-text"
                                            id="discount_unit">{{ currency_symbol() }}</span>
                                    </div>
                                </div>
                            </div>
                            @error('discount_amount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="paid_amount" class="form-label fw-bold">
                                <i class="fas fa-money-bill text-success me-2"></i>المبلغ المدفوع
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01"
                                    class="form-control @error('paid_amount') is-invalid @enderror" id="paid_amount"
                                    name="paid_amount" value="{{ old('paid_amount', 0) }}">
                                <span class="input-group-text">{{ currency_symbol() }}</span>
                            </div>
                            @error('paid_amount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="remaining_amount" class="form-label fw-bold">
                                <i class="fas fa-clock text-warning me-2"></i>المبلغ المتبقي
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01" class="form-control bg-light"
                                    id="remaining_amount" name="remaining_amount" readonly>
                                <span class="input-group-text">{{ currency_symbol() }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                بعد إنشاء الفاتورة سيتم توجيهك لتوزيع المنتجات
                            </small>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{{ user_route('purchases.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-2"></i>إنشاء الفاتورة والمتابعة للتوزيع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>



    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                let productIndex = 0;
                const productsTableBody = document.getElementById('productsTableBody');
                const productsTable = document.getElementById('productsTable');
                const emptyMessage = document.getElementById('emptyProductsMessage');
                const products = @json($products);

                // Toggle empty message and table visibility
                function toggleEmptyMessage() {
                    const hasProducts = productsTableBody.children.length > 0;
                    emptyMessage.style.display = hasProducts ? 'none' : 'block';
                    if (hasProducts) {
                        productsTable.classList.add('show');
                    } else {
                        productsTable.classList.remove('show');
                    }
                }

                // Add Product Row
                document.getElementById('addProduct').addEventListener('click', function() {
                    const rowHtml = `
                        <tr class="product-row" data-index="${productIndex}">
                            <td>
                                <div class="product-search-container position-relative">
                                    <input type="text" class="form-control product-search" placeholder="ابحث عن المنتج..." autocomplete="off">
                                    <input type="hidden" name="products[${productIndex}][product_id]" class="product-id-input" required>
                                    <div class="product-dropdown position-absolute w-100 bg-white border rounded shadow-sm" style="display: none; max-height: 200px; overflow-y: auto; z-index: 1000;">
                                        ${products.map(product => `
                                                                                                            <div class="product-option p-2 border-bottom cursor-pointer"
                                                                                                                 data-id="${product.id}"
                                                                                                                 data-price="${product.price || 0}"
                                                                                                                 data-name="${product.name}"
                                                                                                                 data-sku="${product.sku || ''}">
                                                                                                                <div class="fw-bold">${product.name}</div>
                                                                                                                ${product.sku ? `<small class="text-muted">كود: ${product.sku}</small>` : ''}
                                                                                                            </div>
                                                                                                        `).join('')}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <input type="number" name="products[${productIndex}][quantity]" class="form-control quantity-input" min="1" step="1" required>
                            </td>
                            <td>
                                <input type="number" name="products[${productIndex}][purchase_price]" class="form-control purchase-price-input" min="0" step="0.01" required>
                            </td>
                            <td>
                                <input type="number" class="form-control total-price-input bg-light" readonly>
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger btn-sm remove-product">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;

                    productsTableBody.insertAdjacentHTML('beforeend', rowHtml);
                    const newRow = productsTableBody.lastElementChild;
                    initializeRowEvents(newRow);
                    productIndex++;
                    toggleEmptyMessage();
                });

                // Initialize Events for a Row
                function initializeRowEvents(row) {
                    const productSearch = row.querySelector('.product-search');
                    const productIdInput = row.querySelector('.product-id-input');
                    const productDropdown = row.querySelector('.product-dropdown');
                    const quantityInput = row.querySelector('.quantity-input');
                    const purchasePriceInput = row.querySelector('.purchase-price-input');
                    const totalPriceInput = row.querySelector('.total-price-input');
                    const removeButton = row.querySelector('.remove-product');

                    // Handle product search
                    productSearch.addEventListener('input', function() {
                        const searchTerm = this.value.toLowerCase();
                        const options = productDropdown.querySelectorAll('.product-option');
                        let hasVisibleOptions = false;

                        options.forEach(option => {
                            const name = option.dataset.name.toLowerCase();
                            const sku = option.dataset.sku.toLowerCase();

                            if (name.includes(searchTerm) || sku.includes(searchTerm)) {
                                option.style.display = 'block';
                                hasVisibleOptions = true;
                            } else {
                                option.style.display = 'none';
                            }
                        });

                        productDropdown.style.display = searchTerm && hasVisibleOptions ? 'block' : 'none';
                    });

                    // Handle product selection
                    productDropdown.addEventListener('click', function(e) {
                        const option = e.target.closest('.product-option');
                        if (option) {
                            productSearch.value = option.dataset.name;
                            productIdInput.value = option.dataset.id;
                            purchasePriceInput.value = option.dataset.price;
                            productDropdown.style.display = 'none';
                            calculateTotal();
                        }
                    });

                    // Show dropdown on focus
                    productSearch.addEventListener('focus', function() {
                        if (this.value) {
                            productDropdown.style.display = 'block';
                        }
                    });

                    // Hide dropdown when clicking outside
                    document.addEventListener('click', function(e) {
                        if (!row.contains(e.target)) {
                            productDropdown.style.display = 'none';
                        }
                    });

                    // Calculate Total Price
                    function calculateTotal() {
                        const quantity = parseFloat(quantityInput.value) || 0;
                        const price = parseFloat(purchasePriceInput.value) || 0;
                        const total = quantity * price;
                        totalPriceInput.value = total.toFixed(2);
                        updatePurchaseTotals();
                    }

                    quantityInput.addEventListener('input', calculateTotal);
                    purchasePriceInput.addEventListener('input', calculateTotal);

                    // Remove Row
                    removeButton.addEventListener('click', function() {
                        row.remove();
                        updatePurchaseTotals();
                        toggleEmptyMessage();
                    });
                }

                // Update Purchase Totals
                function updatePurchaseTotals() {
                    let total = 0;
                    document.querySelectorAll('.total-price-input').forEach(input => {
                        total += parseFloat(input.value) || 0;
                    });

                    // Update table footer total
                    document.getElementById('totalAmount').textContent = total.toFixed(2) + ' ج.م';

                    // Calculate discount
                    const discountType = document.getElementById('discount_type').value;
                    const discountValue = parseFloat(document.getElementById('discount_amount').value) || 0;
                    let discountAmount = 0;

                    if (discountType === 'percentage') {
                        discountAmount = (total * discountValue) / 100;
                    } else {
                        discountAmount = discountValue;
                    }

                    // Update financial summary
                    const paid = parseFloat(document.getElementById('paid_amount').value) || 0;
                    const remaining = total - discountAmount - paid;

                    document.getElementById('total_amount').value = total.toFixed(2);
                    document.getElementById('remaining_amount').value = remaining.toFixed(2);
                }

                // Handle discount type change
                document.getElementById('discount_type').addEventListener('change', function() {
                    const discountUnit = document.getElementById('discount_unit');
                    const discountAmount = document.getElementById('discount_amount');

                    if (this.value === 'percentage') {
                        discountUnit.textContent = '%';
                        discountAmount.max = '100';
                        discountAmount.placeholder = 'نسبة الخصم';
                    } else {
                        discountUnit.textContent = 'ج.م';
                        discountAmount.removeAttribute('max');
                        discountAmount.placeholder = 'مبلغ الخصم';
                    }
                    updatePurchaseTotals();
                });

                // Update Remaining Amount when discount or paid amount changes
                document.getElementById('discount_amount').addEventListener('input', updatePurchaseTotals);
                document.getElementById('paid_amount').addEventListener('input', updatePurchaseTotals);
                document.getElementById('discount_type').addEventListener('change', updatePurchaseTotals);

                // Initialize discount type
                document.getElementById('discount_type').dispatchEvent(new Event('change'));

                // Initialize empty message
                toggleEmptyMessage();
            });
        </script>
    @endpush

    @push('styles')
        <style>
            .rtl {
                direction: rtl;
                text-align: right;
            }

            .stepper {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .step {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
                position: relative;
            }

            .step-number {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: #e9ecef;
                color: #6c757d;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                margin-bottom: 10px;
            }

            .step.active .step-number {
                background: #007bff;
                color: white;
            }

            .step-line {
                width: 100px;
                height: 2px;
                background: #e9ecef;
                margin: 0 20px;
                margin-top: -30px;
            }

            .step-content h6 {
                margin: 0;
                font-size: 14px;
            }

            .step-content small {
                color: #6c757d;
                font-size: 12px;
            }

            #productsTable {
                display: none;
            }

            #productsTable.show {
                display: table !important;
            }

            .product-row td {
                vertical-align: middle;
                padding: 0.5rem;
            }

            .product-row .form-control,
            .product-row .form-select {
                border: 1px solid #e3e6f0;
            }

            .product-row .form-control:focus,
            .product-row .form-select:focus {
                border-color: #5a5c69;
                box-shadow: 0 0 0 0.2rem rgba(90, 92, 105, 0.25);
            }

            /* Custom Product Search Dropdown */
            .product-search-container {
                position: relative;
            }

            .product-dropdown {
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
                z-index: 1000;
                max-height: 200px;
                overflow-y: auto;
            }

            .product-option {
                padding: 0.5rem 0.75rem;
                border-bottom: 1px solid #f8f9fa;
                cursor: pointer;
                transition: background-color 0.15s ease-in-out;
            }

            .product-option:hover {
                background-color: #f8f9fa;
            }

            .product-option:last-child {
                border-bottom: none;
            }

            .cursor-pointer {
                cursor: pointer;
            }



            .rtl .form-select {
                text-align: right;
            }

            .rtl .invalid-feedback {
                text-align: right;
            }

            .rtl .btn i {
                margin-left: 0.5rem;
                margin-right: 0;
            }

            .rtl .table th,
            .rtl .table td {
                text-align: right;
            }

            .rtl .btn-group {
                flex-direction: row-reverse;
            }

            .rtl .btn-group .btn {
                margin-right: 0;
                margin-left: 0.25rem;
            }

            .rtl .form-label {
                text-align: right;
            }

            .rtl .card-title {
                text-align: right;
            }

            .rtl .text-start {
                text-align: right !important;
            }

            .rtl .text-end {
                text-align: left !important;
            }

            .rtl .justify-content-start {
                justify-content: flex-end !important;
            }

            .rtl .justify-content-end {
                justify-content: flex-start !important;
            }
        </style>
    @endpush

    @push('scripts')
        <!-- Select2 CSS -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css"
            rel="stylesheet" />

        <!-- Select2 JS -->
        <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

        <script>
            $(document).ready(function() {
                // Initialize Select2 for supplier dropdown
                $('#supplier_id').select2({
                    theme: 'bootstrap-5',
                    placeholder: 'ابحث عن المورد...',
                    allowClear: false,
                    dir: 'rtl',
                    language: {
                        noResults: function() {
                            return 'لا توجد نتائج';
                        },
                        searching: function() {
                            return 'جاري البحث...';
                        },
                        inputTooShort: function() {
                            return 'يرجى إدخال حرف واحد على الأقل';
                        }
                    },
                    templateResult: function(option) {
                        if (!option.id) {
                            return option.text;
                        }

                        var $option = $(option.element);
                        var phone = $option.data('phone');
                        var email = $option.data('email');
                        var contactPerson = $option.data('contact-person');
                        var balance = $option.data('balance');

                        var $result = $('<div></div>');
                        $result.text(option.text);

                        var $details = $('<div class="small text-muted mt-1"></div>');

                        if (contactPerson) {
                            $details.append('<div>👤 ' + contactPerson + '</div>');
                        }

                        if (phone) {
                            $details.append('<div>📞 ' + phone + '</div>');
                        }

                        if (email) {
                            $details.append('<div>📧 ' + email + '</div>');
                        }

                        if ($details.children().length > 0) {
                            $result.append($details);
                        }

                        return $result;
                    }
                });
            });
        </script>

        <style>
            /* Fix Select2 RTL close button positioning */
            .select2-container--bootstrap-5[dir="rtl"] .select2-selection--single .select2-selection__clear {
                right: auto !important;
                left: 7px !important;
            }

            .select2-container--bootstrap-5[dir="rtl"] .select2-selection--single .select2-selection__rendered {
                padding-left: 25px !important;
                padding-right: 12px !important;
            }

            .select2-container--bootstrap-5[dir="rtl"] .select2-selection--single .select2-selection__arrow {
                right: auto !important;
                left: 1px !important;
            }

            /* Ensure proper spacing for Arabic text */
            .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
                line-height: 1.5;
                text-align: right;
            }
        </style>
    @endpush
</x-app-layout>

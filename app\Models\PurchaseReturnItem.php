<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseReturnItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_return_id',
        'purchase_item_id',
        'product_id',
        'quantity_returned',
        'original_quantity',
        'cost_price',
        'total_cost',
        'condition',
        'item_notes',
        'inventory_adjusted'
    ];

    protected $casts = [
        'quantity_returned' => 'decimal:2',
        'original_quantity' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'inventory_adjusted' => 'boolean'
    ];

    // Relationships
    public function purchaseReturn()
    {
        return $this->belongsTo(PurchaseReturn::class);
    }

    public function purchaseItem()
    {
        return $this->belongsTo(PurchaseItem::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    // Business Logic Methods
    public function getReturnPercentageAttribute(): float
    {
        if ($this->original_quantity <= 0) {
            return 0;
        }

        return ($this->quantity_returned / $this->original_quantity) * 100;
    }

    public function canReturnMoreQuantity(): bool
    {
        return $this->quantity_returned < $this->original_quantity;
    }

    public function getRemainingQuantityAttribute(): float
    {
        return $this->original_quantity - $this->quantity_returned;
    }

    // Scopes
    public function scopeGoodCondition($query)
    {
        return $query->where('condition', 'good');
    }

    public function scopeDamaged($query)
    {
        return $query->where('condition', 'damaged');
    }

    public function scopeExpired($query)
    {
        return $query->where('condition', 'expired');
    }

    public function scopeDefective($query)
    {
        return $query->where('condition', 'defective');
    }

    public function scopeInventoryAdjusted($query)
    {
        return $query->where('inventory_adjusted', true);
    }

    public function scopeInventoryNotAdjusted($query)
    {
        return $query->where('inventory_adjusted', false);
    }
}

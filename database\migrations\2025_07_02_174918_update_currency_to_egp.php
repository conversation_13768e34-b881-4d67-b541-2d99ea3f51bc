<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing settings to use EGP as default currency
        DB::table('settings')->update(['company_currency' => 'EGP']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert to SAR as default currency
        DB::table('settings')->update(['company_currency' => 'SAR']);
    }
};

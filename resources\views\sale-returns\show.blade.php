<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-undo text-warning me-2"></i>
                    تفاصيل مرتجع المبيعات
                </h1>
                <p class="mb-0 text-muted">{{ $saleReturn->return_number }}</p>
            </div>
            <div class="d-flex gap-2">
                @if($saleReturn->canBeEdited())
                    <a href="{{ user_route('sale-returns.edit', $saleReturn) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                @endif
                @if(auth()->user()->hasRole('admin') && $saleReturn->canBeApproved())
                    <form method="POST" action="{{ user_route('sale-returns.approve', $saleReturn) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-info" 
                                onclick="return confirm('هل أنت متأكد من الموافقة على هذا المرتجع؟')">
                            <i class="fas fa-check me-2"></i>موافقة
                        </button>
                    </form>
                @endif
                @if(auth()->user()->hasRole('admin') && $saleReturn->canBeCompleted())
                    <form method="POST" action="{{ user_route('sale-returns.complete', $saleReturn) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success" 
                                onclick="return confirm('هل أنت متأكد من إكمال هذا المرتجع؟ سيتم تعديل المخزون.')">
                            <i class="fas fa-check-circle me-2"></i>إكمال
                        </button>
                    </form>
                @endif
                <a href="{{ user_route('sale-returns.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المرتجعات
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Return Details -->
            <div class="col-lg-8">
                <!-- Return Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle me-2"></i>معلومات المرتجع
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">رقم المرتجع</label>
                                <div class="fw-bold text-primary">{{ $saleReturn->return_number }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">رقم الفاتورة الأصلية</label>
                                <div>
                                    <a href="{{ user_route('sales.show', $saleReturn->sale) }}" 
                                       class="text-decoration-none fw-bold text-info">
                                        {{ $saleReturn->sale->invoice_number }}
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">العميل</label>
                                <div class="fw-bold">{{ $saleReturn->customer?->name ?? 'عميل نقدي' }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">تاريخ المرتجع</label>
                                <div class="fw-bold">{{ $saleReturn->return_date->format('d/m/Y') }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">نوع المرتجع</label>
                                <div>
                                    <span class="badge bg-info fs-6">{{ $saleReturn->return_type_label }}</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">الحالة</label>
                                <div>
                                    <span class="badge bg-{{ $saleReturn->status_color }} fs-6">{{ $saleReturn->status_label }}</span>
                                </div>
                            </div>
                            @if($saleReturn->reason)
                            <div class="col-md-12 mb-3">
                                <label class="form-label fw-bold text-muted">سبب المرتجع</label>
                                <div class="fw-bold">{{ $saleReturn->reason }}</div>
                            </div>
                            @endif
                            @if($saleReturn->notes)
                            <div class="col-md-12 mb-3">
                                <label class="form-label fw-bold text-muted">ملاحظات</label>
                                <div class="p-3 bg-light rounded">{{ $saleReturn->notes }}</div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Return Items -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-boxes me-2"></i>المنتجات المرتجعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية الأصلية</th>
                                        <th>الكمية المرتجعة</th>
                                        <th>السعر</th>
                                        <th>المجموع</th>
                                        <th>الحالة</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($saleReturn->items as $item)
                                    <tr>
                                        <td>
                                            <div class="fw-bold">{{ $item->product->name }}</div>
                                            <small class="text-muted">كود: {{ $item->product->code }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $item->original_quantity }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">{{ $item->quantity_returned }}</span>
                                        </td>
                                        <td>{{ number_format($item->sale_price, 2) }} ج.م</td>
                                        <td class="fw-bold text-success">{{ number_format($item->total_amount, 2) }} ج.م</td>
                                        <td>
                                            <span class="badge bg-{{ $item->condition_color }}">
                                                {{ $item->condition_label }}
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $item->item_notes ?? '-' }}</small>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="4" class="text-end">إجمالي المرتجع:</th>
                                        <th class="text-success">{{ number_format($saleReturn->total_amount, 2) }} ج.م</th>
                                        <th colspan="2"></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Summary Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-calculator me-2"></i>ملخص المرتجع
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <h5 class="text-muted">عدد المنتجات</h5>
                                <h3 class="text-primary">{{ $saleReturn->items->count() }}</h3>
                            </div>
                            <div class="col-12 mb-3">
                                <h5 class="text-muted">إجمالي الكمية</h5>
                                <h3 class="text-warning">{{ $saleReturn->items->sum('quantity_returned') }}</h3>
                            </div>
                            <div class="col-12 mb-3">
                                <h5 class="text-muted">إجمالي المبلغ</h5>
                                <h3 class="text-success">{{ number_format($saleReturn->total_amount, 2) }} ج.م</h3>
                            </div>
                            @if($saleReturn->refund_amount > 0)
                            <div class="col-12 mb-3">
                                <h5 class="text-muted">مبلغ الاسترداد</h5>
                                <h3 class="text-info">{{ number_format($saleReturn->refund_amount, 2) }} ج.م</h3>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- User Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>معلومات المستخدم
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">تم الإنشاء بواسطة</label>
                            <div class="fw-bold">{{ $saleReturn->user->name }}</div>
                            <small class="text-muted">{{ $saleReturn->created_at->format('d/m/Y h:i A') }}</small>
                        </div>
                        @if($saleReturn->approved_by)
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">تمت الموافقة بواسطة</label>
                            <div class="fw-bold">{{ $saleReturn->approvedBy->name }}</div>
                            <small class="text-muted">{{ $saleReturn->approved_at->format('d/m/Y h:i A') }}</small>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Location Information -->
                @if($saleReturn->branch || $saleReturn->store)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-map-marker-alt me-2"></i>معلومات الموقع
                        </h6>
                    </div>
                    <div class="card-body">
                        @if($saleReturn->branch)
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">الفرع</label>
                            <div class="fw-bold">{{ $saleReturn->branch->name }}</div>
                        </div>
                        @endif
                        @if($saleReturn->store)
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">المخزن</label>
                            <div class="fw-bold">{{ $saleReturn->store->name }}</div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>

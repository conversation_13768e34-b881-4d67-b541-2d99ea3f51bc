<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('purchases', function (Blueprint $table) {
            // $table->decimal('total_amount', 10, 2)->default(0);
            // $table->decimal('discount_amount', 10, 2)->default(0);
            // $table->decimal('paid_amount', 10, 2)->default(0);
            // $table->decimal('remaining_amount', 10, 2)->default(0);
            // $table->foreignId('supplier_account_id')->nullable()->constrained('accounts')->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::table('purchases', function (Blueprint $table) {
            $table->dropColumn([
                'total_amount',
                'discount_amount',
                'paid_amount',
                'remaining_amount',
                'supplier_account_id'
            ]);
        });
    }
};

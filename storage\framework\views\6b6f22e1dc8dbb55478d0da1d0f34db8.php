<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">لوحة تحكم المدير</h2>
                    <div class="text-muted">
                        <i class="fas fa-calendar-alt"></i> <?php echo e(now()->format('Y-m-d')); ?>

                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        

        <!-- Main Statistics Cards -->
        <div class="row mb-4">
            <!-- Branches -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">الفروع</h6>
                                <h3 class="mb-0"><?php echo e($stats['total_branches']); ?></h3>
                                <small>فرع</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-store fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stores -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">المخازن</h6>
                                <h3 class="mb-0"><?php echo e($stats['total_stores']); ?></h3>
                                <small>مخزن</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-warehouse fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-secondary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">المستخدمين</h6>
                                <h3 class="mb-0"><?php echo e($stats['total_users']); ?></h3>
                                <small>مستخدم</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today Sales -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مبيعات اليوم</h6>
                                <h3 class="mb-0"><?php echo e(format_currency($stats['total_sales_today'], null, 0)); ?></h3>
                                <small>جنيه</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-shopping-cart fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Cards -->
        <div class="row mb-4">
            <!-- Today Purchases -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مشتريات اليوم</h6>
                                <h3 class="mb-0"><?php echo e(format_currency($stats['total_purchases_today'], null, 0)); ?></h3>
                                <small>جنيه</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-shopping-bag fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Suppliers Owe -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مستحقات الموردين</h6>
                                <h3 class="mb-0">
                                    <?php echo e(format_currency($financialSummary['suppliers_owe'] ?? 0, null, 0)); ?></h3>
                                <small>جنيه</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customers Owe -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-dark text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">ديون العملاء</h6>
                                <h3 class="mb-0">
                                    <?php echo e(format_currency($financialSummary['customers_owe'] ?? 0, null, 0)); ?></h3>
                                <small>جنيه</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-user-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today Direct Transfers -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white" style="background-color: #6f42c1;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">تحويلات اليوم</h6>
                                <h3 class="mb-0"><?php echo e($branchStats['today_direct_transfers'] ?? 0); ?></h3>
                                <small>تحويل مباشر</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-exchange-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Summary -->
        <div class="row mb-4">
            <div class="col-lg-12 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-wallet"></i> الملخص المالي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center p-3 border-end">
                                    <h6 class="text-muted mb-1">إجمالي المبيعات</h6>
                                    <h4 class="text-success mb-0">
                                        <?php echo e(format_currency($financialSummary['total_sales'] ?? 0)); ?></h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3">
                                    <h6 class="text-muted mb-1">إجمالي المشتريات</h6>
                                    <h4 class="text-danger mb-0">
                                        <?php echo e(format_currency($financialSummary['total_purchases'] ?? 0)); ?></h4>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center p-3 border-end">
                                    <h6 class="text-muted mb-1">ديون العملاء</h6>
                                    <h4 class="text-warning mb-0">
                                        <?php echo e(format_currency($financialSummary['customers_owe'] ?? 0)); ?></h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3">
                                    <h6 class="text-muted mb-1">مستحقات الموردين</h6>
                                    <h4 class="text-info mb-0">
                                        <?php echo e(format_currency($financialSummary['suppliers_owe'] ?? 0)); ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>



        <!-- Recent Activities -->
        <div class="row">
            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart"></i> أحدث المبيعات
                        </h5>
                        <a href="<?php echo e(route('admin.sales.index')); ?>" class="btn btn-sm btn-outline-primary">عرض
                            الكل</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>الفرع</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $recentSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>
                                                <a href="<?php echo e(route('admin.sales.show', $sale)); ?>"
                                                    class="text-decoration-none">
                                                    #<?php echo e($sale->id); ?>

                                                </a>
                                            </td>
                                            <td><?php echo e($sale->customer->name ?? 'عميل نقدي'); ?></td>
                                            <td><?php echo e($sale->branch->name ?? '-'); ?></td>
                                            <td><?php echo e(format_currency($sale->total_amount)); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="4" class="text-center text-muted py-3">لا توجد مبيعات
                                                حديثة
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-truck"></i> أحدث المشتريات
                        </h5>
                        <a href="<?php echo e(route('admin.purchases.index')); ?>" class="btn btn-sm btn-outline-primary">عرض
                            الكل</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>المورد</th>
                                        <th>الفرع</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $recentPurchases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purchase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>
                                                <a href="<?php echo e(route('admin.purchases.show', $purchase)); ?>"
                                                    class="text-decoration-none">
                                                    #<?php echo e($purchase->id); ?>

                                                </a>
                                            </td>
                                            <td><?php echo e($purchase->supplier->name ?? '-'); ?></td>
                                            <td><?php echo e($purchase->branch->name ?? '-'); ?></td>
                                            <td><?php echo e(format_currency($purchase->total_amount)); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="4" class="text-center text-muted py-3">لا توجد مشتريات
                                                حديثة</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Branches -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy"></i> الفروع
                        </h5>
                        <div>
                            <span class="badge bg-primary me-2">هذا الشهر</span>
                            
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم الفرع</th>
                                        <th>مبيعات الشهر</th>
                                        <th>عدد المبيعات</th>
                                        
                                        <th>الموظفين</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $topBranches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo e($branch['name']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="text-success fw-bold">
                                                    <?php echo e(format_currency($branch['monthly_sales'])); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo e($branch['sales_count']); ?> مبيعة</span>
                                            </td>
                                            
                                            <td>
                                                <span class="badge bg-primary"><?php echo e($branch['users_count']); ?>

                                                    موظف</span>
                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('admin.branches.show', $branch['id'])); ?>"
                                                    class="btn btn-sm btn-outline-primary">
                                                    عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="6" class="text-center text-muted py-3">
                                                <i class="fas fa-building fa-2x mb-2 text-muted"></i><br>
                                                لا توجد بيانات فروع متاحة
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outstanding Balances -->
        <div class="row">
            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle text-warning"></i> العملاء المدينون
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم العميل</th>
                                        <th>المبلغ المستحق</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $topCustomersOwing; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($customer['name']); ?></td>
                                            <td class="text-warning fw-bold">
                                                <?php echo e(format_currency($customer['owed_amount'])); ?></td>
                                            <td>
                                                <a href="<?php echo e(route('admin.customers.show', $customer['id'])); ?>"
                                                    class="btn btn-sm btn-outline-primary">
                                                    عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="3" class="text-center text-muted py-3">لا توجد مستحقات
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-circle text-danger"></i> مستحقات الموردين
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم المورد</th>
                                        <th>المبلغ المستحق</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $topSuppliersWeOwe; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($supplier['name']); ?></td>
                                            <td class="text-danger fw-bold">
                                                <?php echo e(format_currency($supplier['owed_amount'])); ?></td>
                                            <td>
                                                <a href="<?php echo e(user_route('suppliers.show', $supplier['id'])); ?>"
                                                    class="btn btn-sm btn-outline-primary">
                                                    عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="3" class="text-center text-muted py-3">لا توجد مستحقات
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>
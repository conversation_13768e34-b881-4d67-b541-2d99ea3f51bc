<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-money-bill-wave text-success me-2"></i>
                    دفعات العملاء
                </h1>
                <p class="mb-0 text-muted">إدارة ومتابعة دفعات العملاء</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('customer-payments.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة دفعة جديدة
                </a>
                <a href="{{ user_route('sales.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المبيعات
                </a>
            </div>
        </div>

        <!-- Filters Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter me-2"></i>البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ user_route('customer-payments.index') }}">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="customer_id" class="form-label">العميل</label>
                            <select class="form-select" id="customer_id" name="customer_id">
                                <option value="">جميع العملاء</option>
                                @foreach ($customers as $customer)
                                    <option value="{{ $customer->id }}"
                                        {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                        {{ $customer->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="payment_method" name="payment_method">
                                <option value="">جميع الطرق</option>
                                <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>نقدي
                                </option>
                                <option value="card" {{ request('payment_method') == 'card' ? 'selected' : '' }}>بطاقة
                                </option>
                                <option value="bank_transfer"
                                    {{ request('payment_method') == 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي
                                </option>
                                <option value="check" {{ request('payment_method') == 'check' ? 'selected' : '' }}>شيك
                                </option>
                                <option value="other" {{ request('payment_method') == 'other' ? 'selected' : '' }}>
                                    أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                value="{{ request('date_from') }}">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="{{ user_route('customer-payments.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Payments Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>قائمة الدفعات
                </h6>
            </div>
            <div class="card-body">
                @if ($payments->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>رقم المرجع</th>
                                    <th>تاريخ الدفع</th>
                                    <th>المستخدم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($payments as $payment)
                                    <tr>
                                        <td>
                                            <a href="{{ user_route('sales.show', $payment->sale) }}"
                                                class="text-decoration-none fw-bold text-primary">
                                                {{ $payment->sale->invoice_number }}
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-info text-white me-2">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        {{ $payment->sale->customer?->name ?? 'عميل نقدي' }}
                                                    </div>
                                                    @if ($payment->sale->customer?->phone)
                                                        <small
                                                            class="text-muted">{{ $payment->sale->customer->phone }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">
                                                {{ number_format($payment->amount, 2) }} ج.م
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                {{ $payment->payment_method_label }}
                                            </span>
                                        </td>
                                        <td>
                                            {{ $payment->reference_number ?? '-' }}
                                        </td>
                                        <td>
                                            <div>{{ $payment->payment_date->format('d/m/Y') }}</div>
                                            <small
                                                class="text-muted">{{ $payment->payment_date->format('h:i A') }}</small>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $payment->user->name }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ user_route('customer-payments.show', $payment) }}"
                                                    class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if (auth()->user()->hasRole('admin'))
                                                    <form method="POST"
                                                        action="{{ user_route('customer-payments.destroy', $payment) }}"
                                                        class="d-inline"
                                                        onsubmit="return confirm('هل أنت متأكد من حذف هذه الدفعة؟')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                            title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="2" class="text-end">إجمالي الدفعات:</th>
                                    <th class="text-success">
                                        {{ number_format($payments->sum('amount'), 2) }} ج.م
                                    </th>
                                    <th colspan="5"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $payments->withQueryString()->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد دفعات</h5>
                        <p class="text-muted">لم يتم العثور على أي دفعات تطابق معايير البحث</p>
                        <a href="{{ user_route('customer-payments.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة دفعة جديدة
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>

<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">إدارة الفروع</h2>
                        <p class="text-muted mb-0">إدارة وتتبع جميع فروع الشركة</p>
                    </div>
                    {{-- <div>
                        <a href="{{ route('admin.branches.analytics-dashboard') }}" class="btn btn-info me-2">
                            <i class="fas fa-chart-line"></i> تحليلات الفروع
                        </a>
                        <a href="{{ route('admin.branches.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة فرع جديد
                        </a>
                    </div> --}}
                </div>
            </div>
        </div>

        <!-- Branch Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي الفروع</h6>
                                <h3 class="mb-0">{{ $branches->count() }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">الفروع النشطة</h6>
                                <h3 class="mb-0">{{ $branches->where('is_active', true)->count() }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المخازن</h6>
                                <h3 class="mb-0">
                                    {{ $branches->sum(function ($branch) {return $branch->stores->count();}) }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-warehouse fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي الموظفين</h6>
                                <h3 class="mb-0">
                                    {{ $branches->sum(function ($branch) {return $branch->users->count();}) }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" action="{{ route('admin.branches.index') }}">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                        value="{{ request('search') }}" placeholder="البحث بالاسم أو الكود...">
                                </div>
                                <div class="col-md-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشط
                                        </option>
                                        <option value="inactive"
                                            {{ request('status') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="sort" class="form-label">ترتيب حسب</label>
                                    <select class="form-select" id="sort" name="sort">
                                        <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>الاسم
                                        </option>
                                        <option value="created_at"
                                            {{ request('sort') == 'created_at' ? 'selected' : '' }}>تاريخ الإنشاء
                                        </option>
                                        <option value="sales" {{ request('sort') == 'sales' ? 'selected' : '' }}>
                                            المبيعات</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branches Grid -->
        <div class="row">
            @forelse($branches as $branch)
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card h-100 {{ !$branch->is_active ? 'border-danger' : '' }}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">{{ $branch->name }}</h6>
                                <small class="text-muted">{{ $branch->code }}</small>
                            </div>
                            <div>
                                <span class="badge bg-{{ $branch->is_active ? 'success' : 'danger' }}">
                                    {{ $branch->is_active ? 'نشط' : 'غير نشط' }}
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Branch Info -->
                            <div class="mb-3">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="border-end">
                                            <h6 class="text-primary mb-0">{{ $branch->stores->count() }}</h6>
                                            <small class="text-muted">مخازن</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="border-end">
                                            <h6 class="text-success mb-0">{{ $branch->users->count() }}</h6>
                                            <small class="text-muted">موظفين</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <h6 class="text-info mb-0">{{ $branch->sales->count() }}</h6>
                                        <small class="text-muted">مبيعات</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Info -->
                            <div class="mb-3">
                                @if ($branch->address)
                                    <div class="d-flex align-items-center mb-1">
                                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                        <small class="text-muted">{{ Str::limit($branch->address, 30) }}</small>
                                    </div>
                                @endif
                                @if ($branch->phone)
                                    <div class="d-flex align-items-center mb-1">
                                        <i class="fas fa-phone text-muted me-2"></i>
                                        <small class="text-muted">{{ $branch->phone }}</small>
                                    </div>
                                @endif
                                @if ($branch->email)
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-envelope text-muted me-2"></i>
                                        <small class="text-muted">{{ $branch->email }}</small>
                                    </div>
                                @endif
                            </div>

                            <!-- Financial Summary -->
                            <div class="mb-3">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="text-center p-2 bg-light rounded">
                                            <small class="text-muted d-block">مبيعات الشهر</small>
                                            <strong
                                                class="text-success">{{ number_format($branch->sales->where('created_at', '>=', now()->startOfMonth())->sum('total_amount'), 2) }}
                                            </strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <a href="{{ route('admin.branches.show', $branch) }}"
                                    class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                <a href="{{ route('admin.branches.edit', $branch) }}"
                                    class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="modal"
                                    data-bs-target="#analyticsModal{{ $branch->id }}">
                                    <i class="fas fa-chart-bar"></i> إحصائيات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد فروع</h5>
                            <p class="text-muted">ابدأ بإضافة فرع جديد لإدارة أعمالك</p>
                            {{-- <a href="{{ route('admin.branches.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة فرع جديد
                            </a> --}}
                        </div>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Analytics Modals -->
        @foreach ($branches as $branch)
            <div class="modal fade" id="analyticsModal{{ $branch->id }}" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إحصائيات فرع {{ $branch->name }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-primary text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4>{{ number_format($branch->sales->sum('total_amount'), 2) }} </h4>
                                            <p class="mb-0">إجمالي المبيعات</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-success text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4>{{ number_format($branch->purchases->sum('total_amount'), 2) }}
                                            </h4>
                                            <p class="mb-0">إجمالي المشتريات</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-info text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4>{{ $branch->sales->count() }}</h4>
                                            <p class="mb-0">عدد المبيعات</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-warning text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4>{{ $branch->purchases->count() }}</h4>
                                            <p class="mb-0">عدد المشتريات</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-secondary text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4>{{ $branch->expenses->sum('amount') ?? 0 }}</h4>
                                            <p class="mb-0">إجمالي المصروفات</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-dark text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4>{{ number_format($branch->sales->sum('total_amount') - $branch->purchases->sum('total_amount') - ($branch->expenses->sum('amount') ?? 0), 2) }}
                                            </h4>
                                            <p class="mb-0">صافي الربح</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <a href="{{ route('admin.branches.show', $branch) }}" class="btn btn-primary">
                                عرض التفاصيل الكاملة
                            </a>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</x-app-layout>

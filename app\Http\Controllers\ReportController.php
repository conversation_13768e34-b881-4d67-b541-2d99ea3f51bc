<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Category;
use App\Models\Expense;
use App\Models\Invoice;
use App\Models\Product;
use App\Models\Purchase;
use App\Models\Store;
use App\Models\Supplier;
use App\Models\Customer;
use App\Models\User;
use App\Models\Sale;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request as FacadesRequest;

class ReportController extends Controller
{
    public function sales(Request $request)
    {
        $query = Sale::with(['branch', 'user', 'customer', 'items.product'])
            ->when($request->branch_id, function ($q) use ($request) {
                return $q->where('branch_id', $request->branch_id);
            })
            ->when($request->date_from, function ($q) use ($request) {
                return $q->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($q) use ($request) {
                return $q->whereDate('created_at', '<=', $request->date_to);
            })
            ->when($request->customer_id, function ($q) use ($request) {
                return $q->where('customer_id', $request->customer_id);
            })
            ->when($request->user_id, function ($q) use ($request) {
                return $q->where('user_id', $request->user_id);
            });

        // Calculate summary statistics
        $summary = [
            'total_sales' => $query->sum('total_amount'),
            'total_invoices' => $query->count(),
            'average_invoice' => $query->avg('total_amount'),
            'highest_invoice' => $query->max('total_amount'),
            'total_items_sold' => $query->withSum('items', 'quantity')->get()->sum('items_sum_quantity'),
            'total_discount' => $query->sum('discount_amount'),
            'net_sales' => $query->sum('total_amount') - $query->sum('discount_amount')
        ];

        // Get top selling products
        $topProducts = $query->get()->flatMap(function ($sale) {
            return $sale->items->map(function ($item) {
                return [
                    'name' => $item->product->name,
                    'quantity' => $item->quantity,
                    'total' => $item->total
                ];
            });
        })->groupBy('name')->map(function ($items) {
            return [
                'name' => $items->first()['name'],
                'quantity' => $items->sum('quantity'),
                'total' => $items->sum('total')
            ];
        })->sortByDesc('total')->take(5)->values();

        // Group by period if specified
        if ($request->group_by) {
            $groupBy = $request->group_by;
            $periodExpression = match ($groupBy) {
                'daily' => 'DATE(created_at)',
                'weekly' => 'DATE(DATE_SUB(created_at, INTERVAL WEEKDAY(created_at) DAY))',
                'monthly' => 'DATE_FORMAT(created_at, "%Y-%m-01")',
                default => 'DATE(created_at)'
            };

            $query->select([
                DB::raw("{$periodExpression} as period"),
                DB::raw('COUNT(*) as invoices_count'),
                DB::raw('SUM(total_amount) as total_sales'),
                DB::raw('SUM(discount_amount) as total_discount'),
                DB::raw('SUM(total_amount - discount_amount) as net_sales'),
                DB::raw('AVG(total_amount) as average_invoice')
            ])
                ->groupBy(DB::raw($periodExpression));

            // Apply sorting
            if ($request->sort_by) {
                switch ($request->sort_by) {
                    case 'date_asc':
                        $query->orderBy('period', 'asc');
                        break;
                    case 'date_desc':
                        $query->orderBy('period', 'desc');
                        break;
                    case 'amount_desc':
                        $query->orderBy('total_sales', 'desc');
                        break;
                    case 'amount_asc':
                        $query->orderBy('total_sales', 'asc');
                        break;
                }
            } else {
                $query->orderBy('period', 'desc');
            }
        }

        $sales = $query->paginate(10);
        $branches = Branch::get();
        $customers = Customer::get();
        $users = User::get();

        return view('reports.sales', compact(
            'sales',
            'summary',
            'topProducts',
            'branches',
            'customers',
            'users'
        ));
    }

    public function purchases(Request $request)
    {
        $query = Purchase::with(['branch', 'supplier'])
            ->when($request->filled('supplier'), function ($query) use ($request) {
                $query->where('supplier_id', $request->supplier);
            })
            ->when($request->filled('branch'), function ($query) use ($request) {
                $query->where('branch_id', $request->branch);
            })
            ->when($request->filled('date_range'), function ($query) use ($request) {
                $dates = explode(' - ', $request->date_range);
                $query->whereBetween('purchase_date', $dates);
            })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('status', $request->status);
            });

        $summary = [
            'total_purchases' => $query->sum('total_amount'),
            'total_orders' => $query->count(),
            'average_order' => $query->avg('total_amount'),
            'highest_order' => $query->max('total_amount'),
        ];

        $groupBy = $request->input('group_by', 'daily');
        $sortBy = $request->input('sort_by', 'date_desc');

        switch ($groupBy) {
            case 'daily':
                $purchases = $query->select(
                    DB::raw('DATE(purchase_date) as period'),
                    DB::raw('COUNT(*) as purchases_count'),
                    DB::raw('SUM(total_amount) as total_purchases'),
                    DB::raw('AVG(total_amount) as average_purchase'),
                    DB::raw('MAX(total_amount) as highest_purchase')
                )
                    ->groupBy('period')
                    ->orderBy($sortBy === 'date_asc' ? 'period' : 'total_purchases', $sortBy === 'date_asc' ? 'asc' : 'desc')
                    ->paginate(10);
                break;

            case 'weekly':
                $purchases = $query->select(
                    DB::raw('YEARWEEK(purchase_date) as period'),
                    DB::raw('COUNT(*) as purchases_count'),
                    DB::raw('SUM(total_amount) as total_purchases'),
                    DB::raw('AVG(total_amount) as average_purchase'),
                    DB::raw('MAX(total_amount) as highest_purchase')
                )
                    ->groupBy('period')
                    ->orderBy($sortBy === 'date_asc' ? 'period' : 'total_purchases', $sortBy === 'date_asc' ? 'asc' : 'desc')
                    ->paginate(10);
                break;

            case 'monthly':
                $purchases = $query->select(
                    DB::raw('DATE_FORMAT(purchase_date, "%Y-%m") as period'),
                    DB::raw('COUNT(*) as purchases_count'),
                    DB::raw('SUM(total_amount) as total_purchases'),
                    DB::raw('AVG(total_amount) as average_purchase'),
                    DB::raw('MAX(total_amount) as highest_purchase')
                )
                    ->groupBy('period')
                    ->orderBy($sortBy === 'date_asc' ? 'period' : 'total_purchases', $sortBy === 'date_asc' ? 'asc' : 'desc')
                    ->paginate(10);
                break;
        }

        $branches = Branch::where('is_active', true)->get();
        $suppliers = Supplier::where('is_active', true)->get();

        return view('reports.purchases', compact('purchases', 'summary', 'branches', 'suppliers'));
    }

    public function inventory(Request $request)
    {
        $query = Product::with(['category', 'branchInventories.branch'])
            ->when($request->filled('category'), function ($query) use ($request) {
                $query->where('category_id', $request->category);
            })
            ->when($request->filled('branch'), function ($query) use ($request) {
                $query->whereHas('branchInventories', function ($q) use ($request) {
                    $q->where('branch_id', $request->branch);
                });
            })
            ->when($request->filled('stock_status'), function ($query) use ($request) {
                switch ($request->stock_status) {
                    case 'low':
                        $query->whereHas('branchInventories', function ($q) {
                            $q->whereRaw('quantity <= minimum_stock');
                        });
                        break;
                    case 'out':
                        $query->whereHas('branchInventories', function ($q) {
                            $q->where('quantity', 0);
                        });
                        break;
                    case 'available':
                        $query->whereHas('branchInventories', function ($q) {
                            $q->where('quantity', '>', 0);
                        });
                        break;
                }
            })
            ->when($request->filled('search'), function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%')
                    ->orWhere('sku', 'like', '%' . $request->search . '%');
            });

        // Calculate summary statistics
        $summary = [
            'total_products' => $query->count(),
            'total_value' => $query->get()->sum(function ($product) {
                $branchValue = $product->branchInventories->sum(function ($inventory) use ($product) {
                    return $inventory->quantity * $product->cost_price;
                });
                $storeValue = $product->storeInventories->sum(function ($inventory) use ($product) {
                    return $inventory->quantity * $product->cost_price;
                });
                return $branchValue + $storeValue;
            }),
            'total_sale_value' => $query->get()->sum(function ($product) {
                $branchSaleValue = $product->branchInventories->sum(function ($inventory) use ($product) {
                    return $inventory->quantity * $product->sale_price;
                });
                $storeSaleValue = $product->storeInventories->sum(function ($inventory) use ($product) {
                    return $inventory->quantity * $product->sale_price;
                });
                return $branchSaleValue + $storeSaleValue;
            }),
            'low_stock_count' => $query->get()->filter(function ($product) {
                $branchStock = $product->branchInventories->sum('quantity');
                $storeStock = $product->storeInventories->sum('quantity');
                $totalStock = $branchStock + $storeStock;
                return $totalStock > 0 && $totalStock <= $product->minimum_stock;
            })->count(),
            'out_of_stock_count' => $query->get()->filter(function ($product) {
                $branchStock = $product->branchInventories->sum('quantity');
                $storeStock = $product->storeInventories->sum('quantity');
                return ($branchStock + $storeStock) <= 0;
            })->count(),
            'total_categories' => $query->distinct('category_id')->count('category_id'),
            'total_branches' => $query->get()->flatMap->branchInventories->pluck('branch_id')->unique()->count(),
            'total_stores' => $query->get()->flatMap->storeInventories->pluck('store_id')->unique()->count(),
            'independent_stores' => $query->get()->flatMap->storeInventories->filter(function ($inventory) {
                return $inventory->store && $inventory->store->isIndependent();
            })->pluck('store_id')->unique()->count(),
            'average_stock' => $query->get()->avg(function ($product) {
                $branchStock = $product->branchInventories->sum('quantity');
                $storeStock = $product->storeInventories->sum('quantity');
                return $branchStock + $storeStock;
            }),
            'total_stock' => $query->get()->sum(function ($product) {
                return $product->branchInventories->sum('quantity') + $product->storeInventories->sum('quantity');
            })
        ];

        // Get top categories by stock value
        $topCategories = $query->get()
            ->groupBy('category_id')
            ->map(function ($products) {
                return [
                    'name' => $products->first()->category->name,
                    'total_stock' => $products->sum(function ($product) {
                        return $product->branchInventories->sum('quantity') + $product->storeInventories->sum('quantity');
                    }),
                    'total_value' => $products->sum(function ($product) {
                        $branchValue = $product->branchInventories->sum(function ($inventory) use ($product) {
                            return $inventory->quantity * $product->cost_price;
                        });
                        $storeValue = $product->storeInventories->sum(function ($inventory) use ($product) {
                            return $inventory->quantity * $product->cost_price;
                        });
                        return $branchValue + $storeValue;
                    })
                ];
            })
            ->sortByDesc('total_value')
            ->take(5);

        // Get top branches by stock value
        $topBranches = $query->get()
            ->flatMap->branchInventories
            ->groupBy('branch_id')
            ->map(function ($inventories) {
                $branch = $inventories->first()->branch;
                return [
                    'name' => $branch->name,
                    'type' => 'branch',
                    'total_stock' => $inventories->sum('quantity'),
                    'total_value' => $inventories->sum(function ($inventory) {
                        return $inventory->quantity * $inventory->product->cost_price;
                    })
                ];
            })
            ->sortByDesc('total_value')
            ->take(5);

        // Get top stores by stock value
        $topStores = $query->get()
            ->flatMap->storeInventories
            ->groupBy('store_id')
            ->map(function ($inventories) {
                $store = $inventories->first()->store;
                return [
                    'name' => $store->name,
                    'type' => $store->isIndependent() ? 'independent_store' : 'branch_store',
                    'total_stock' => $inventories->sum('quantity'),
                    'total_value' => $inventories->sum(function ($inventory) {
                        return $inventory->quantity * $inventory->product->cost_price;
                    })
                ];
            })
            ->sortByDesc('total_value')
            ->take(5);

        // Apply sorting
        $sortBy = $request->input('sort_by', 'name');
        $sortDirection = $request->input('sort_direction', 'asc');

        switch ($sortBy) {
            case 'name':
                $query->orderBy('name', $sortDirection);
                break;
            case 'stock':
                $query->orderBy(DB::raw('(SELECT SUM(quantity) FROM branch_inventories WHERE product_id = products.id)'), $sortDirection);
                break;
            case 'value':
                $query->orderBy(DB::raw('(SELECT SUM(quantity * cost_price) FROM branch_inventories WHERE product_id = products.id)'), $sortDirection);
                break;
            case 'category':
                $query->orderBy(DB::raw('(SELECT name FROM categories WHERE id = products.category_id)'), $sortDirection);
                break;
            case 'branch':
                $query->orderBy(DB::raw('(SELECT name FROM branches WHERE id IN (SELECT branch_id FROM branch_inventories WHERE product_id = products.id))'), $sortDirection);
                break;
        }

        // Get paginated results
        $products = $query->paginate(10)->withQueryString();

        $branches = Branch::where('is_active', true)->get();
        $stores = Store::where('is_active', true)->get();
        $categories = Category::all();

        return view('reports.inventory', compact(
            'products',
            'summary',
            'topCategories',
            'topBranches',
            'topStores',
            'branches',
            'stores',
            'categories'
        ));
    }

    public function profit(Request $request)
    {
        $query = Sale::with(['branch', 'items.product'])
            ->when($request->filled('branch'), function ($query) use ($request) {
                $query->where('branch_id', $request->branch);
            })
            ->when($request->filled('date_range'), function ($query) use ($request) {
                $dates = explode(' - ', $request->date_range);
                $query->whereBetween('created_at', $dates);
            });

        // Get expenses for the same period
        $expensesQuery = Expense::query()
            ->when($request->filled('branch'), function ($query) use ($request) {
                $query->where('branch_id', $request->branch);
            })
            ->when($request->filled('date_range'), function ($query) use ($request) {
                $dates = explode(' - ', $request->date_range);
                $query->whereBetween('expense_date', $dates);
            });

        $totalExpenses = $expensesQuery->sum('amount');

        // Calculate summary statistics
        $summary = [
            'total_sales' => $query->sum('total_amount'),
            'total_cost' => $query->get()->sum(function ($sale) {
                return $sale->items->sum(function ($item) {
                    return $item->quantity * $item->product->cost_price;
                });
            }),
            'total_expenses' => $totalExpenses,
            'total_profit' => $query->sum('total_amount') - $query->get()->sum(function ($sale) {
                return $sale->items->sum(function ($item) {
                    return $item->quantity * $item->product->cost_price;
                });
            }) - $totalExpenses,
            'profit_percentage' => $query->sum('total_amount') > 0
                ? (($query->sum('total_amount') - $query->get()->sum(function ($sale) {
                    return $sale->items->sum(function ($item) {
                        return $item->quantity * $item->product->cost_price;
                    });
                }) - $totalExpenses) / $query->sum('total_amount')) * 100
                : 0,
        ];

        $groupBy = $request->input('group_by', 'daily');
        $sortBy = $request->input('sort', 'date_desc');

        switch ($groupBy) {
            case 'daily':
                $profits = $query->select(
                    DB::raw('DATE(created_at) as period'),
                    DB::raw('COUNT(*) as sales_count'),
                    DB::raw('SUM(total_amount) as total_sales'),
                    'branch_id'
                )
                    ->groupBy(DB::raw('DATE(created_at)'), 'branch_id')
                    ->get()
                    ->map(function ($item) {
                        $cost = Sale::whereDate('created_at', $item->period)
                            ->where('branch_id', $item->branch_id)
                            ->get()
                            ->sum(function ($sale) {
                                return $sale->items->sum(function ($item) {
                                    return $item->quantity * $item->product->cost_price;
                                });
                            });

                        $expenses = Expense::whereDate('expense_date', $item->period)
                            ->where('branch_id', $item->branch_id)
                            ->sum('amount');

                        $netProfit = $item->total_sales - $cost - $expenses;
                        $profitPercentage = $item->total_sales > 0 ? ($netProfit / $item->total_sales) * 100 : 0;

                        return (object)[
                            'period' => $item->period,
                            'sales_count' => $item->sales_count,
                            'total_sales' => $item->total_sales,
                            'total_cost' => $cost,
                            'total_expenses' => $expenses,
                            'net_profit' => $netProfit,
                            'profit_percentage' => $profitPercentage
                        ];
                    });
                break;

            case 'weekly':
                $profits = $query->select(
                    DB::raw('YEARWEEK(created_at) as period'),
                    DB::raw('COUNT(*) as sales_count'),
                    DB::raw('SUM(total_amount) as total_sales'),
                    'branch_id'
                )
                    ->groupBy(DB::raw('YEARWEEK(created_at)'), 'branch_id')
                    ->get()
                    ->map(function ($item) {
                        $cost = Sale::whereRaw('YEARWEEK(created_at) = ?', [$item->period])
                            ->where('branch_id', $item->branch_id)
                            ->get()
                            ->sum(function ($sale) {
                                return $sale->items->sum(function ($item) {
                                    return $item->quantity * $item->product->cost_price;
                                });
                            });

                        $expenses = Expense::whereRaw('YEARWEEK(expense_date) = ?', [$item->period])
                            ->where('branch_id', $item->branch_id)
                            ->sum('amount');

                        $netProfit = $item->total_sales - $cost - $expenses;
                        $profitPercentage = $item->total_sales > 0 ? ($netProfit / $item->total_sales) * 100 : 0;

                        return (object)[
                            'period' => $item->period,
                            'sales_count' => $item->sales_count,
                            'total_sales' => $item->total_sales,
                            'total_cost' => $cost,
                            'total_expenses' => $expenses,
                            'net_profit' => $netProfit,
                            'profit_percentage' => $profitPercentage
                        ];
                    });
                break;

            case 'monthly':
                $profits = $query->select(
                    DB::raw('DATE_FORMAT(created_at, "%Y-%m") as period'),
                    DB::raw('COUNT(*) as sales_count'),
                    DB::raw('SUM(total_amount) as total_sales'),
                    'branch_id'
                )
                    ->groupBy(DB::raw('DATE_FORMAT(created_at, "%Y-%m")'), 'branch_id')
                    ->get()
                    ->map(function ($item) {
                        $cost = Sale::whereRaw('DATE_FORMAT(created_at, "%Y-%m") = ?', [$item->period])
                            ->where('branch_id', $item->branch_id)
                            ->get()
                            ->sum(function ($sale) {
                                return $sale->items->sum(function ($item) {
                                    return $item->quantity * $item->product->cost_price;
                                });
                            });

                        $expenses = Expense::whereRaw('DATE_FORMAT(expense_date, "%Y-%m") = ?', [$item->period])
                            ->where('branch_id', $item->branch_id)
                            ->sum('amount');

                        $netProfit = $item->total_sales - $cost - $expenses;
                        $profitPercentage = $item->total_sales > 0 ? ($netProfit / $item->total_sales) * 100 : 0;

                        return (object)[
                            'period' => $item->period,
                            'sales_count' => $item->sales_count,
                            'total_sales' => $item->total_sales,
                            'total_cost' => $cost,
                            'total_expenses' => $expenses,
                            'net_profit' => $netProfit,
                            'profit_percentage' => $profitPercentage
                        ];
                    });
                break;
        }

        // Apply sorting
        $profits = collect($profits);
        if ($sortBy === 'date_asc') {
            $profits = $profits->sortBy('period');
        } elseif ($sortBy === 'date_desc') {
            $profits = $profits->sortByDesc('period');
        } elseif ($sortBy === 'profit_asc') {
            $profits = $profits->sortBy('net_profit');
        } elseif ($sortBy === 'profit_desc') {
            $profits = $profits->sortByDesc('net_profit');
        }

        // Paginate the results
        $profits = new \Illuminate\Pagination\LengthAwarePaginator(
            $profits->forPage(FacadesRequest::get('page', 1), 10),
            $profits->count(),
            10,
            FacadesRequest::get('page', 1),
            ['path' => FacadesRequest::url(), 'query' => FacadesRequest::query()]
        );

        $branches = Branch::where('is_active', true)->get();

        return view('reports.profit', compact('profits', 'summary', 'branches'));
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

class StoreInventory extends Model
{
    use HasFactory;

    protected $table = 'store_inventory';

    protected $fillable = [
        'store_id',
        'product_id',
        'quantity',
        'minimum_stock',
        'maximum_stock',
        'cost_price',
        'sale_price_1',
        'sale_price_2',
        'sale_price_3',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'minimum_stock' => 'decimal:2',
        'maximum_stock' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'sale_price_1' => 'decimal:2',
        'sale_price_2' => 'decimal:2',
        'sale_price_3' => 'decimal:2',
    ];

    // Relationships
    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // Helper methods
    public function isLowStock(): bool
    {
        return $this->quantity <= $this->minimum_stock;
    }

    public function isOverStock(): bool
    {
        return $this->maximum_stock && $this->quantity >= $this->maximum_stock;
    }

    public function getStockStatus(): string
    {
        if ($this->isLowStock()) {
            return 'low';
        } elseif ($this->isOverStock()) {
            return 'over';
        }
        return 'normal';
    }

    public function getStockStatusText(): string
    {
        return match ($this->getStockStatus()) {
            'low' => 'مخزون منخفض',
            'over' => 'مخزون زائد',
            'normal' => 'مخزون طبيعي',
            default => 'غير محدد'
        };
    }

    public function getStockStatusBadgeClass(): string
    {
        return match ($this->getStockStatus()) {
            'low' => 'bg-danger',
            'over' => 'bg-warning',
            'normal' => 'bg-success',
            default => 'bg-secondary'
        };
    }

    public function canTransfer(float $quantity): bool
    {
        return $this->quantity >= $quantity;
    }

    public function addStock(float $quantity): void
    {
        $this->quantity += $quantity;
        $this->save();
    }

    public function removeStock(float $quantity): bool
    {
        if (!$this->canTransfer($quantity)) {
            return false;
        }

        $this->quantity -= $quantity;
        $this->save();
        return true;
    }

    // Scopes
    public function scopeLowStock($query)
    {
        return $query->whereRaw('quantity <= minimum_stock');
    }

    public function scopeOverStock($query)
    {
        return $query->whereRaw('quantity >= maximum_stock AND maximum_stock IS NOT NULL');
    }

    public function scopeForStore($query, int $storeId)
    {
        return $query->where('store_id', $storeId);
    }

    public function scopeForProduct($query, int $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeAccessibleBy($query, User $user)
    {
        if ($user->canAccessAllBranches()) {
            return $query;
        }

        $accessibleStoreIds = $user->getAccessibleStoreIds();
        if (!empty($accessibleStoreIds)) {
            return $query->whereIn('store_id', $accessibleStoreIds);
        }

        // Fallback: no accessible stores
        return $query->whereRaw('1 = 0'); // Return empty result
    }
}

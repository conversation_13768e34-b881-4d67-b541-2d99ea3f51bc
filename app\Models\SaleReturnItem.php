<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleReturnItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'sale_return_id',
        'sale_item_id',
        'product_id',
        'quantity_returned',
        'original_quantity',
        'sale_price',
        'total_amount',
        'condition',
        'item_notes',
        'inventory_adjusted'
    ];

    protected $casts = [
        'quantity_returned' => 'decimal:2',
        'original_quantity' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'inventory_adjusted' => 'boolean'
    ];

    // Relationships
    public function saleReturn()
    {
        return $this->belongsTo(SaleReturn::class);
    }

    public function saleItem()
    {
        return $this->belongsTo(SaleItem::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    // Business Logic Methods
    public function getReturnPercentageAttribute(): float
    {
        if ($this->original_quantity <= 0) {
            return 0;
        }

        return ($this->quantity_returned / $this->original_quantity) * 100;
    }

    public function canReturnMoreQuantity(): bool
    {
        return $this->quantity_returned < $this->original_quantity;
    }

    public function getRemainingQuantityAttribute(): float
    {
        return $this->original_quantity - $this->quantity_returned;
    }

    public function getConditionLabelAttribute(): string
    {
        return match($this->condition) {
            'good' => 'جيد',
            'damaged' => 'تالف',
            'expired' => 'منتهي الصلاحية',
            'defective' => 'معيب',
            default => 'غير محدد'
        };
    }

    public function getConditionColorAttribute(): string
    {
        return match($this->condition) {
            'good' => 'success',
            'damaged' => 'warning',
            'expired' => 'danger',
            'defective' => 'info',
            default => 'secondary'
        };
    }

    // Scopes
    public function scopeGoodCondition($query)
    {
        return $query->where('condition', 'good');
    }

    public function scopeDamaged($query)
    {
        return $query->where('condition', 'damaged');
    }

    public function scopeExpired($query)
    {
        return $query->where('condition', 'expired');
    }

    public function scopeDefective($query)
    {
        return $query->where('condition', 'defective');
    }
}

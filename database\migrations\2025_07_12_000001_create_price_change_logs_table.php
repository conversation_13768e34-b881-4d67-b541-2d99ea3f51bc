<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('price_change_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->cascadeOnDelete();
            $table->foreignId('branch_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            
            // Price change details
            $table->string('price_type'); // 'product_price', 'product_selling_price', 'branch_sale_price_1', etc.
            $table->decimal('old_price', 10, 2)->nullable();
            $table->decimal('new_price', 10, 2);
            $table->decimal('price_difference', 10, 2);
            $table->decimal('percentage_change', 8, 4)->nullable();
            
            // Context information
            $table->string('change_reason')->nullable(); // 'manual_update', 'bulk_update', 'cost_change', etc.
            $table->string('change_source')->default('manual'); // 'manual', 'api', 'import', 'system'
            $table->json('metadata')->nullable(); // Additional context data
            
            // Validation and warnings
            $table->json('validation_warnings')->nullable();
            $table->boolean('below_cost_price')->default(false);
            $table->decimal('profit_margin_before', 8, 4)->nullable();
            $table->decimal('profit_margin_after', 8, 4)->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['product_id', 'created_at']);
            $table->index(['branch_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index('price_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('price_change_logs');
    }
};

<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">تفاصيل فرع {{ $branch->name }}</h2>
                        <p class="text-muted mb-0">{{ $branch->code }} - {{ $branch->is_active ? 'نشط' : 'غير نشط' }}</p>
                    </div>
                    <div>
                        {{-- <a href="{{ route('admin.branches.edit', $branch) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل الفرع
                        </a> --}}
                        <a href="{{ route('admin.branches.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> عودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Overview Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المبيعات</h6>
                                <h3 class="mb-0">{{ number_format($branch->sales->sum('total_amount'), 2) }} ج.م</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المشتريات</h6>
                                <h3 class="mb-0">{{ number_format($branch->purchases->sum('total_amount'), 2) }} ج.م
                                </h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-shopping-cart fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">عدد المخازن</h6>
                                <h3 class="mb-0">{{ $branch->stores->count() }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-warehouse fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">عدد الموظفين</h6>
                                <h3 class="mb-0">{{ $branch->users->count() }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Information and Financial Summary -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> معلومات الفرع
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">الكود:</th>
                                <td>{{ $branch->code }}</td>
                            </tr>
                            <tr>
                                <th>الاسم:</th>
                                <td>{{ $branch->name }}</td>
                            </tr>
                            <tr>
                                <th>العنوان:</th>
                                <td>{{ $branch->address ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>الهاتف:</th>
                                <td>{{ $branch->phone ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>البريد الإلكتروني:</th>
                                <td>{{ $branch->email ?? '-' }}</td>
                            </tr>
                            {{-- <tr>
                                <th>الرصيد الافتتاحي:</th>
                                <td>{{ number_format($branch->opening_balance, 2) }}</td>
                            </tr> --}}
                            <tr>
                                <th>الحالة:</th>
                                <td>
                                    <span class="badge bg-{{ $branch->is_active ? 'success' : 'danger' }}">
                                        {{ $branch->is_active ? 'نشط' : 'غير نشط' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>تاريخ الإنشاء:</th>
                                <td>{{ $branch->created_at }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie"></i> الملخص المالي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="text-muted mb-1">مبيعات اليوم</h6>
                                    <h4 class="text-success mb-3">
                                        {{ number_format($branch->sales->where('created_at', '>=', now()->startOfDay())->sum('total_amount'), 2) }}
                                        ج.م
                                    </h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted mb-1">مشتريات اليوم</h6>
                                <h4 class="text-primary mb-3">
                                    {{ number_format($branch->purchases->where('created_at', '>=', now()->startOfDay())->sum('total_amount'), 2) }}
                                    ج.م
                                </h4>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="text-muted mb-1">مبيعات الشهر</h6>
                                    <h4 class="text-success mb-3">
                                        {{ number_format($branch->sales->where('created_at', '>=', now()->startOfMonth())->sum('total_amount'), 2) }}
                                        ج.م
                                    </h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted mb-1">مشتريات الشهر</h6>
                                <h4 class="text-primary mb-3">
                                    {{ number_format($branch->purchases->where('created_at', '>=', now()->startOfMonth())->sum('total_amount'), 2) }}
                                    ج.م
                                </h4>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <h6 class="text-muted mb-1">صافي الربح (إجمالي)</h6>
                            <h3
                                class="text-{{ $branch->sales->sum('total_amount') - $branch->purchases->sum('total_amount') >= 0 ? 'success' : 'danger' }}">
                                {{ number_format($branch->sales->sum('total_amount') - $branch->purchases->sum('total_amount'), 2) }}
                                ج.م
                            </h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stores and Users -->
        <div class="row mb-4">
            {{-- <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-store"></i> المخازن ({{ $branch->stores->count() }})
                        </h5>
                        <a href="{{ route('admin.stores.create', ['branch_id' => $branch->id]) }}"
                            class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة مخزن
                        </a>
                    </div>
                    <div class="card-body p-0">
                        @if ($branch->stores->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>اسم المخزن</th>
                                            <th>الكود</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($branch->stores as $store)
                                            <tr>
                                                <td>{{ $store->name }}</td>
                                                <td>{{ $store->code }}</td>
                                                <td>
                                                    <span
                                                        class="badge bg-{{ $store->is_active ? 'success' : 'danger' }}">
                                                        {{ $store->is_active ? 'نشط' : 'غير نشط' }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="{{ route('admin.stores.show', $store) }}"
                                                        class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-store fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد مخازن في هذا الفرع</p>
                                <a href="{{ route('admin.stores.create', ['branch_id' => $branch->id]) }}"
                                    class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة مخزن جديد
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div> --}}

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users"></i> الموظفين ({{ $branch->users->count() }})
                        </h5>
                        <a href="{{ route('admin.settings.users.create', ['branch_id' => $branch->id]) }}"
                            class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة موظف
                        </a>
                    </div>
                    <div class="card-body p-0">
                        @if ($branch->users->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الدور</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($branch->users as $user)
                                            <tr>
                                                <td>{{ $user->name }}</td>
                                                <td>{{ $user->email }}</td>
                                                <td>
                                                    <span
                                                        class="badge bg-info">{{ $user->role->name ?? 'غير محدد' }}</span>
                                                </td>
                                                <td>
                                                    <a href="{{ route('admin.settings.users.show', $user) }}"
                                                        class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا يوجد موظفين في هذا الفرع</p>
                                <a href="{{ route('admin.settings.users.create', ['branch_id' => $branch->id]) }}"
                                    class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة موظف جديد
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart"></i> أحدث المبيعات
                        </h5>
                        <a href="{{ route('admin.sales.index', ['branch_id' => $branch->id]) }}"
                            class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="card-body p-0">
                        @if ($branch->sales->take(5)->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($branch->sales->sortByDesc('created_at')->take(5) as $sale)
                                            <tr>
                                                <td>
                                                    <a href="{{ route('admin.sales.show', $sale) }}"
                                                        class="text-decoration-none">
                                                        #{{ $sale->id }}
                                                    </a>
                                                </td>
                                                <td>{{ $sale->customer->name ?? 'عميل نقدي' }}</td>
                                                <td>{{ number_format($sale->total_amount, 2) }} ج.م</td>
                                                <td>{{ $sale->created_at->format('Y-m-d') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد مبيعات حديثة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-truck"></i> أحدث المشتريات
                        </h5>
                        <a href="{{ route('admin.purchases.index', ['branch_id' => $branch->id]) }}"
                            class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="card-body p-0">
                        @if ($branch->purchases->take(5)->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>المورد</th>
                                            <th>المبلغ</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($branch->purchases->sortByDesc('created_at')->take(5) as $purchase)
                                            <tr>
                                                <td>
                                                    <a href="{{ route('admin.purchases.show', $purchase) }}"
                                                        class="text-decoration-none">
                                                        #{{ $purchase->id }}
                                                    </a>
                                                </td>
                                                <td>{{ $purchase->supplier->name ?? '-' }}</td>
                                                <td>{{ number_format($purchase->total_amount, 2) }} ج.م</td>
                                                <td>{{ $purchase->created_at->format('Y-m-d') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-truck fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد مشتريات حديثة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the type ENUM to include 'direct' and 'internal'
        DB::statement("ALTER TABLE inventory_transfers MODIFY COLUMN type ENUM('store_to_branch', 'branch_to_store', 'store_to_store', 'branch_to_branch', 'direct', 'internal')");
        
        // Update the status ENUM to include 'shipped' and 'rejected'
        DB::statement("ALTER TABLE inventory_transfers MODIFY COLUMN status ENUM('pending', 'approved', 'shipped', 'in_transit', 'completed', 'cancelled', 'rejected') DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert to original ENUM values
        DB::statement("ALTER TABLE inventory_transfers MODIFY COLUMN type ENUM('store_to_branch', 'branch_to_store', 'store_to_store', 'branch_to_branch')");
        
        DB::statement("ALTER TABLE inventory_transfers MODIFY COLUMN status ENUM('pending', 'approved', 'in_transit', 'completed', 'cancelled') DEFAULT 'pending'");
    }
};

<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Branch;
use App\Models\BranchInventory;
use App\Models\Category;
use App\Services\PriceResolutionService;
use App\Services\PriceAuditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PriceManagementController extends Controller
{
    protected $priceResolutionService;
    protected $priceAuditService;

    public function __construct(
        PriceResolutionService $priceResolutionService,
        PriceAuditService $priceAuditService
    ) {
        $this->priceResolutionService = $priceResolutionService;
        $this->priceAuditService = $priceAuditService;
    }

    /**
     * Display price management dashboard
     */
    public function index(Request $request)
    {
        $branches = Branch::where('is_active', true)->get();
        $categories = Category::all();
        
        // Get filter parameters
        $branchId = $request->get('branch_id');
        $categoryId = $request->get('category_id');
        $search = $request->get('search');

        // Build query for products with branch inventory
        $query = Product::with(['category', 'branchInventories.branch'])
            ->where('is_active', true);

        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        $products = $query->paginate(20);

        // Enhance products with price information
        foreach ($products as $product) {
            if ($branchId) {
                $priceInfo = $this->priceResolutionService->getSellingPrice($product, $branchId);
                $product->resolved_price_info = $priceInfo;
            }
        }

        return view('admin.price-management.index', compact(
            'products',
            'branches',
            'categories',
            'branchId',
            'categoryId',
            'search'
        ));
    }

    /**
     * Show bulk price update form
     */
    public function bulkUpdate(Request $request)
    {
        $branchId = $request->get('branch_id');
        $categoryId = $request->get('category_id');
        
        if (!$branchId) {
            return redirect()->route('admin.price-management.index')
                ->with('error', 'يجب اختيار فرع لتحديث الأسعار');
        }

        $branch = Branch::findOrFail($branchId);
        $categories = Category::all();

        // Get products for the selected branch and category
        $query = Product::with(['category', 'branchInventories' => function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        }])->where('is_active', true);

        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        $products = $query->get();

        // Enhance with price information
        foreach ($products as $product) {
            $priceInfo = $this->priceResolutionService->getSellingPrice($product, $branchId);
            $product->resolved_price_info = $priceInfo;
            
            $suggestions = $this->priceResolutionService->getPriceSuggestions($product, $branchId);
            $product->price_suggestions = $suggestions;
        }

        return view('admin.price-management.bulk-update', compact(
            'products',
            'branch',
            'categories',
            'categoryId'
        ));
    }

    /**
     * Process bulk price update
     */
    public function processBulkUpdate(Request $request)
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'updates' => 'required|array|min:1',
            'updates.*.product_id' => 'required|exists:products,id',
            'updates.*.price' => 'required|numeric|min:0',
            'updates.*.price_level' => 'required|integer|min:1|max:3',
        ]);

        $user = $request->user();
        $branchId = $request->branch_id;
        $updates = $request->updates;

        try {
            DB::beginTransaction();

            $result = $this->priceResolutionService->bulkUpdateBranchPrices(
                $updates,
                $branchId,
                $user
            );

            DB::commit();

            $message = "تم تحديث {$result['success']} سعر بنجاح";
            if (!empty($result['failed'])) {
                $message .= " - فشل في تحديث " . count($result['failed']) . " أسعار";
            }

            return redirect()->route('admin.price-management.index', ['branch_id' => $branchId])
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'حدث خطأ أثناء تحديث الأسعار: ' . $e->getMessage());
        }
    }

    /**
     * Show price history for a product
     */
    public function priceHistory(Product $product, Request $request)
    {
        $branchId = $request->get('branch_id');
        $branch = $branchId ? Branch::find($branchId) : null;

        $history = $this->priceAuditService->getProductPriceHistory($product, $branch);

        return view('admin.price-management.history', compact('product', 'branch', 'history'));
    }

    /**
     * Get price suggestions for a product
     */
    public function getPriceSuggestions(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $product = Product::findOrFail($request->product_id);
        $suggestions = $this->priceResolutionService->getPriceSuggestions($product, $request->branch_id);

        return response()->json([
            'success' => true,
            'suggestions' => $suggestions
        ]);
    }

    /**
     * Show price analytics dashboard
     */
    public function analytics(Request $request)
    {
        $days = $request->get('days', 30);
        
        $stats = $this->priceAuditService->getPriceChangeStats($days);
        $significantChanges = $this->priceAuditService->getRecentSignificantChanges($days);
        $belowCostChanges = $this->priceAuditService->getChangesBelowCost($days);

        return view('admin.price-management.analytics', compact(
            'stats',
            'significantChanges',
            'belowCostChanges',
            'days'
        ));
    }

    /**
     * Export price data
     */
    public function export(Request $request)
    {
        $branchId = $request->get('branch_id');
        $categoryId = $request->get('category_id');

        // Build query
        $query = Product::with(['category', 'branchInventories.branch'])
            ->where('is_active', true);

        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        if ($branchId) {
            $query->whereHas('branchInventories', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        }

        $products = $query->get();

        // Prepare CSV data
        $csvData = [];
        $csvData[] = ['Product ID', 'Product Name', 'SKU', 'Category', 'Branch', 'Cost Price', 'Sale Price 1', 'Sale Price 2', 'Sale Price 3', 'Quantity'];

        foreach ($products as $product) {
            $inventories = $branchId 
                ? $product->branchInventories->where('branch_id', $branchId)
                : $product->branchInventories;

            foreach ($inventories as $inventory) {
                $csvData[] = [
                    $product->id,
                    $product->name,
                    $product->sku,
                    $product->category->name,
                    $inventory->branch->name,
                    $inventory->cost_price,
                    $inventory->sale_price_1,
                    $inventory->sale_price_2,
                    $inventory->sale_price_3,
                    $inventory->quantity,
                ];
            }
        }

        // Generate CSV
        $filename = 'price_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}

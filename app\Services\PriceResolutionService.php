<?php

namespace App\Services;

use App\Models\Product;
use App\Models\BranchInventory;
use App\Models\User;
use App\Models\Branch;
use Illuminate\Support\Facades\Log;

class PriceResolutionService
{
    /**
     * Default markup percentage for cost-plus pricing
     */
    const DEFAULT_MARKUP_PERCENTAGE = 0.30; // 30%

    /**
     * Minimum profit margin percentage
     */
    const MINIMUM_PROFIT_MARGIN = 0.05; // 5%

    protected $priceAuditService;

    /**
     * Get the selling price for a product in a specific branch
     *
     * @param Product $product
     * @param Branch|int $branch
     * @param int $priceLevel (1, 2, or 3 for different price tiers)
     * @return array ['price' => float, 'source' => string, 'cost_price' => float]
     */
    public function getSellingPrice(Product $product, $branch, int $priceLevel = 1): array
    {
        $branchId = is_object($branch) ? $branch->id : $branch;

        // Get branch inventory for this product
        $branchInventory = BranchInventory::where('branch_id', $branchId)
            ->where('product_id', $product->id)
            ->first();

        $costPrice = $this->getCostPrice($product, $branchInventory);

        // Priority 1: Branch inventory specific price
        if ($branchInventory) {
            $priceField = "sale_price_{$priceLevel}";
            if ($branchInventory->$priceField && $branchInventory->$priceField > 0) {
                return [
                    'price' => (float) $branchInventory->$priceField,
                    'source' => 'branch_inventory',
                    'cost_price' => $costPrice,
                    'profit_margin' => $this->calculateProfitMargin($branchInventory->$priceField, $costPrice)
                ];
            }
        }

        // Priority 2: Product base selling price
        if ($product->selling_price && $product->selling_price > 0) {
            return [
                'price' => (float) $product->selling_price,
                'source' => 'product_selling_price',
                'cost_price' => $costPrice,
                'profit_margin' => $this->calculateProfitMargin($product->selling_price, $costPrice)
            ];
        }

        // Priority 3: Product base price (legacy)
        if ($product->price && $product->price > 0) {
            return [
                'price' => (float) $product->price,
                'source' => 'product_price',
                'cost_price' => $costPrice,
                'profit_margin' => $this->calculateProfitMargin($product->price, $costPrice)
            ];
        }

        // Priority 4: Cost-plus pricing (fallback)
        $calculatedPrice = $this->calculateCostPlusPrice($costPrice);

        return [
            'price' => $calculatedPrice,
            'source' => 'cost_plus_calculated',
            'cost_price' => $costPrice,
            'profit_margin' => self::DEFAULT_MARKUP_PERCENTAGE
        ];
    }

    /**
     * Validate if a proposed selling price is acceptable
     *
     * @param float $proposedPrice
     * @param Product $product
     * @param Branch|int $branch
     * @param User|null $user
     * @return array ['valid' => bool, 'message' => string, 'warnings' => array]
     */
    public function validateSellingPrice(float $proposedPrice, Product $product, $branch, ?User $user = null): array
    {
        $branchId = is_object($branch) ? $branch->id : $branch;
        $priceInfo = $this->getSellingPrice($product, $branchId);
        $costPrice = $priceInfo['cost_price'];

        $warnings = [];

        // Check minimum price (cost price)
        if ($proposedPrice < $costPrice) {
            // Only admins can sell below cost price
            if (!$user || !$user->hasRole('admin')) {
                return [
                    'valid' => false,
                    'message' => 'لا يمكن البيع بسعر أقل من سعر التكلفة',
                    'warnings' => []
                ];
            } else {
                $warnings[] = 'تحذير: البيع بسعر أقل من سعر التكلفة';
            }
        }

        // Check minimum profit margin
        $profitMargin = $this->calculateProfitMargin($proposedPrice, $costPrice);
        if ($profitMargin < self::MINIMUM_PROFIT_MARGIN && $proposedPrice >= $costPrice) {
            $warnings[] = 'تحذير: هامش الربح منخفض جداً';
        }

        // Check significant deviation from standard price
        $standardPrice = $priceInfo['price'];
        $deviationPercentage = abs($proposedPrice - $standardPrice) / $standardPrice;

        if ($deviationPercentage > 0.20) { // 20% deviation
            $warnings[] = 'تحذير: السعر يختلف بشكل كبير عن السعر المعتاد';
        }

        return [
            'valid' => true,
            'message' => 'السعر مقبول',
            'warnings' => $warnings
        ];
    }

    /**
     * Get cost price for a product
     *
     * @param Product $product
     * @param BranchInventory|null $branchInventory
     * @return float
     */
    private function getCostPrice(Product $product, ?BranchInventory $branchInventory = null): float
    {
        // Priority 1: Branch inventory cost price
        if ($branchInventory && $branchInventory->cost_price && $branchInventory->cost_price > 0) {
            return (float) $branchInventory->cost_price;
        }

        // Priority 2: Product cost price
        if ($product->cost_price && $product->cost_price > 0) {
            return (float) $product->cost_price;
        }

        // Fallback: Use selling price with reverse markup
        $sellingPrice = $product->selling_price ?? $product->price ?? 0;
        if ($sellingPrice > 0) {
            return $sellingPrice / (1 + self::DEFAULT_MARKUP_PERCENTAGE);
        }

        return 0;
    }

    /**
     * Calculate cost-plus price
     *
     * @param float $costPrice
     * @param float $markupPercentage
     * @return float
     */
    private function calculateCostPlusPrice(float $costPrice, float $markupPercentage = self::DEFAULT_MARKUP_PERCENTAGE): float
    {
        if ($costPrice <= 0) {
            return 0;
        }

        return round($costPrice * (1 + $markupPercentage), 2);
    }

    /**
     * Calculate profit margin percentage
     *
     * @param float $sellingPrice
     * @param float $costPrice
     * @return float
     */
    private function calculateProfitMargin(float $sellingPrice, float $costPrice): float
    {
        if ($costPrice <= 0) {
            return 0;
        }

        return ($sellingPrice - $costPrice) / $costPrice;
    }

    /**
     * Update branch inventory price
     *
     * @param Product $product
     * @param Branch|int $branch
     * @param float $price
     * @param int $priceLevel
     * @param User|null $user
     * @return bool
     */
    public function updateBranchPrice(Product $product, $branch, float $price, int $priceLevel = 1, ?User $user = null): bool
    {
        $branchId = is_object($branch) ? $branch->id : $branch;

        // Validate the price first
        $validation = $this->validateSellingPrice($price, $product, $branchId, $user);
        if (!$validation['valid']) {
            throw new \Exception($validation['message']);
        }

        // Get or create branch inventory
        $branchInventory = BranchInventory::firstOrCreate(
            [
                'branch_id' => $branchId,
                'product_id' => $product->id
            ],
            [
                'quantity' => 0,
                'cost_price' => $product->cost_price ?? 0
            ]
        );

        $priceField = "sale_price_{$priceLevel}";
        $oldPrice = $branchInventory->$priceField;

        $branchInventory->$priceField = $price;
        $branchInventory->save();

        // Log price change using audit service if available
        try {
            $auditService = app(\App\Services\PriceAuditService::class);
            $branchModel = Branch::find($branchId);

            $auditService->logBranchPriceChange(
                $product,
                $branchModel,
                $priceField,
                $oldPrice,
                $price,
                $user,
                [
                    'reason' => 'manual_update',
                    'source' => 'price_resolution_service',
                    'metadata' => [
                        'price_level' => $priceLevel,
                        'validation_warnings' => $validation['warnings']
                    ]
                ]
            );
        } catch (\Exception $e) {
            // Fallback to basic logging if audit service fails
            Log::info('Branch price updated', [
                'product_id' => $product->id,
                'branch_id' => $branchId,
                'price_level' => $priceLevel,
                'old_price' => $oldPrice,
                'new_price' => $price,
                'user_id' => $user?->id,
                'warnings' => $validation['warnings'],
                'audit_error' => $e->getMessage()
            ]);
        }

        return true;
    }

    /**
     * Get multiple products with their resolved prices for a branch
     *
     * @param array $productIds
     * @param Branch|int $branch
     * @param int $priceLevel
     * @return array
     */
    public function getProductsWithPrices(array $productIds, $branch, int $priceLevel = 1): array
    {
        $branchId = is_object($branch) ? $branch->id : $branch;
        $products = Product::whereIn('id', $productIds)->get();

        $result = [];
        foreach ($products as $product) {
            $priceInfo = $this->getSellingPrice($product, $branchId, $priceLevel);
            $result[] = [
                'product' => $product,
                'price_info' => $priceInfo
            ];
        }

        return $result;
    }

    /**
     * Calculate total for sale items with price validation
     *
     * @param array $items [['product_id' => int, 'quantity' => int, 'price' => float], ...]
     * @param Branch|int $branch
     * @param User|null $user
     * @return array ['total' => float, 'items' => array, 'warnings' => array]
     */
    public function calculateSaleTotal(array $items, $branch, ?User $user = null): array
    {
        $branchId = is_object($branch) ? $branch->id : $branch;
        $total = 0;
        $validatedItems = [];
        $warnings = [];

        foreach ($items as $item) {
            $product = Product::find($item['product_id']);
            if (!$product) {
                throw new \Exception("المنتج غير موجود");
            }

            // Validate the proposed price
            $validation = $this->validateSellingPrice($item['price'], $product, $branchId, $user);
            if (!$validation['valid']) {
                throw new \Exception("سعر المنتج {$product->name} غير صحيح: {$validation['message']}");
            }

            $warnings = array_merge($warnings, $validation['warnings']);

            $subtotal = $item['price'] * $item['quantity'];
            $total += $subtotal;

            $validatedItems[] = [
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'subtotal' => $subtotal,
                'product_name' => $product->name
            ];
        }

        return [
            'total' => $total,
            'items' => $validatedItems,
            'warnings' => array_unique($warnings)
        ];
    }

    /**
     * Get price suggestions for a product
     *
     * @param Product $product
     * @param Branch|int $branch
     * @return array
     */
    public function getPriceSuggestions(Product $product, $branch): array
    {
        $branchId = is_object($branch) ? $branch->id : $branch;
        $priceInfo = $this->getSellingPrice($product, $branchId);
        $costPrice = $priceInfo['cost_price'];

        $suggestions = [];

        // Current/Standard price
        $suggestions['standard'] = [
            'price' => $priceInfo['price'],
            'label' => 'السعر المعتاد',
            'source' => $priceInfo['source']
        ];

        // Cost-plus suggestions with different margins
        if ($costPrice > 0) {
            $margins = [0.20, 0.30, 0.50, 0.75, 1.00]; // 20%, 30%, 50%, 75%, 100%

            foreach ($margins as $margin) {
                $price = $this->calculateCostPlusPrice($costPrice, $margin);
                $suggestions["margin_" . ($margin * 100)] = [
                    'price' => $price,
                    'label' => "هامش ربح " . ($margin * 100) . "%",
                    'source' => 'calculated'
                ];
            }
        }

        return $suggestions;
    }

    /**
     * Bulk update prices for multiple products in a branch
     *
     * @param array $updates [['product_id' => int, 'price' => float, 'price_level' => int], ...]
     * @param Branch|int $branch
     * @param User|null $user
     * @return array ['success' => int, 'failed' => array]
     */
    public function bulkUpdateBranchPrices(array $updates, $branch, ?User $user = null): array
    {
        $branchId = is_object($branch) ? $branch->id : $branch;
        $success = 0;
        $failed = [];

        foreach ($updates as $update) {
            try {
                $product = Product::find($update['product_id']);
                if (!$product) {
                    throw new \Exception("المنتج غير موجود");
                }

                $this->updateBranchPrice(
                    $product,
                    $branchId,
                    $update['price'],
                    $update['price_level'] ?? 1,
                    $user
                );

                $success++;
            } catch (\Exception $e) {
                $failed[] = [
                    'product_id' => $update['product_id'],
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'success' => $success,
            'failed' => $failed
        ];
    }
}

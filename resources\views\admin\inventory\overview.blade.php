<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-warehouse text-primary"></i> {{ __('نظرة عامة على المخزون') }}
                </h2>
                <p class="text-muted small mb-0">عرض شامل لجميع المنتجات عبر الفروع والمخازن</p>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي المنتجات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_products'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-boxes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    إجمالي القيمة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_value'], 2) }} ج.م</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    مخزون منخفض
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['low_stock_count'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    نفد المخزون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['out_of_stock_count'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter"></i> البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.inventory.overview') }}">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="اسم المنتج أو الكود">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="category_id" class="form-label">التصنيف</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">جميع التصنيفات</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" 
                                            {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="stock_status" class="form-label">حالة المخزون</label>
                            <select class="form-select" id="stock_status" name="stock_status">
                                <option value="">جميع الحالات</option>
                                <option value="available" {{ request('stock_status') == 'available' ? 'selected' : '' }}>متوفر</option>
                                <option value="low_stock" {{ request('stock_status') == 'low_stock' ? 'selected' : '' }}>منخفض</option>
                                <option value="out_of_stock" {{ request('stock_status') == 'out_of_stock' ? 'selected' : '' }}>نفد المخزون</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Products Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table"></i> تفاصيل المخزون
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>التصنيف</th>
                                <th>الكود</th>
                                <th>السعر</th>
                                <th>الفروع</th>
                                <th>المخازن</th>
                                <th>الإجمالي</th>
                                <th>القيمة</th>
                                <th>الحالة</th>
                                <th>المواقع</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($products as $product)
                                <tr>
                                    <td>{{ $product->name }}</td>
                                    <td>{{ $product->category }}</td>
                                    <td>{{ $product->sku ?? '-' }}</td>
                                    <td>{{ number_format($product->price, 2) }} ج.م</td>
                                    <td>{{ number_format($product->branch_quantity, 2) }}</td>
                                    <td>{{ number_format($product->store_quantity, 2) }}</td>
                                    <td class="fw-bold">{{ number_format($product->total_quantity, 2) }}</td>
                                    <td>{{ number_format($product->total_value, 2) }} ج.م</td>
                                    <td>
                                        @if($product->stock_status == 'available')
                                            <span class="badge bg-success">متوفر</span>
                                        @elseif($product->stock_status == 'low_stock')
                                            <span class="badge bg-warning">منخفض</span>
                                        @else
                                            <span class="badge bg-danger">نفد المخزون</span>
                                        @endif
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-info" type="button" 
                                                data-bs-toggle="collapse" data-bs-target="#locations{{ $product->id }}">
                                            <i class="fas fa-eye"></i> عرض
                                        </button>
                                    </td>
                                </tr>
                                <tr class="collapse" id="locations{{ $product->id }}">
                                    <td colspan="10">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>الفروع:</h6>
                                                @forelse($product->locations['branches'] as $branch)
                                                    <small class="d-block">{{ $branch['name'] }}: {{ $branch['quantity'] }} ({{ number_format($branch['cost_price'], 2) }} ج.م)</small>
                                                @empty
                                                    <small class="text-muted">لا توجد كمية في الفروع</small>
                                                @endforelse
                                            </div>
                                            <div class="col-md-6">
                                                <h6>المخازن:</h6>
                                                @forelse($product->locations['stores'] as $store)
                                                    <small class="d-block">{{ $store['name'] }}: {{ $store['quantity'] }}</small>
                                                @empty
                                                    <small class="text-muted">لا توجد كمية في المخازن</small>
                                                @endforelse
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد منتجات</p>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

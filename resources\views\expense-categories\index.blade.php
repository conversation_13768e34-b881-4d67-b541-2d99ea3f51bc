<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('تصنيفات المصروفات') }}
            </h2>
            <div>
                <a href="{{ route('expense-categories.export') }}" class="btn btn-success me-2">
                    <i class="fas fa-file-export"></i> {{ __('تصدير') }}
                </a>
                <a href="{{ route('expense-categories.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {{ __('إضافة تصنيف جديد') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Create Button -->
                    <div class="mb-4">
                        <a href="{{ route('expense-categories.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> {{ __('إضافة تصنيف جديد') }}
                        </a>
                    </div>

                    <!-- Filters -->
                    <form action="{{ route('expense-categories.index') }}" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">{{ __('بحث') }}</label>
                                    <input type="text" name="search" id="search" class="form-control" value="{{ request('search') }}" placeholder="{{ __('اسم التصنيف') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="status">{{ __('الحالة') }}</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>{{ __('نشط') }}</option>
                                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>{{ __('غير نشط') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="type">{{ __('النوع') }}</label>
                                    <select name="type" id="type" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        <option value="fixed" {{ request('type') == 'fixed' ? 'selected' : '' }}>{{ __('ثابت') }}</option>
                                        <option value="variable" {{ request('type') == 'variable' ? 'selected' : '' }}>{{ __('متغير') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="parent">{{ __('التصنيف الأب') }}</label>
                                    <select name="parent" id="parent" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        <option value="root" {{ request('parent') == 'root' ? 'selected' : '' }}>{{ __('التصنيفات الرئيسية') }}</option>
                                        @foreach($parentCategories as $parent)
                                            <option value="{{ $parent->id }}" {{ request('parent') == $parent->id ? 'selected' : '' }}>
                                                {{ $parent->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> {{ __('بحث') }}
                                </button>
                                <a href="{{ route('expense-categories.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> {{ __('إعادة تعيين') }}
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Bulk Actions -->
                    <form action="{{ route('expense-categories.bulk-action') }}" method="POST" id="bulk-action-form" class="mb-4">
                        @csrf
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select name="action" class="form-select" id="bulk-action">
                                    <option value="">{{ __('إجراءات متعددة') }}</option>
                                    <option value="activate">{{ __('تفعيل') }}</option>
                                    <option value="deactivate">{{ __('إيقاف') }}</option>
                                    <option value="delete">{{ __('حذف') }}</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary" id="bulk-action-button" disabled>
                                    {{ __('تطبيق') }}
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Categories Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th>{{ __('الاسم') }}</th>
                                    <th>{{ __('الوصف') }}</th>
                                    <th>{{ __('التصنيف الأب') }}</th>
                                    <th>{{ __('الميزانية الشهرية') }}</th>
                                    <th>{{ __('الميزانية السنوية') }}</th>
                                    <th>{{ __('عدد المصروفات') }}</th>
                                    <th>{{ __('إجمالي المصروفات') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($categories as $category)
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="categories[]" value="{{ $category->id }}" class="category-checkbox">
                                        </td>
                                        <td>
                                            @if($category->icon)
                                                <img src="{{ Storage::url($category->icon) }}" alt="{{ $category->name }}" class="category-icon me-2" style="width: 24px; height: 24px;">
                                            @endif
                                            <span style="color: {{ $category->color ?: '#000' }}">{{ $category->name }}</span>
                                        </td>
                                        <td>{{ $category->description ?: '-' }}</td>
                                        <td>{{ $category->parent ? $category->parent->name : '-' }}</td>
                                        <td>
                                            @if($category->monthly_budget)
                                                {{ number_format($category->monthly_budget, 2) }}
                                                <div class="progress mt-1" style="height: 5px;">
                                                    <div class="progress-bar {{ $category->monthly_budget_usage > 100 ? 'bg-danger' : 'bg-success' }}"
                                                         role="progressbar"
                                                         style="width: {{ min($category->monthly_budget_usage, 100) }}%">
                                                    </div>
                                                </div>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td>
                                            @if($category->yearly_budget)
                                                {{ number_format($category->yearly_budget, 2) }}
                                                <div class="progress mt-1" style="height: 5px;">
                                                    <div class="progress-bar {{ $category->yearly_budget_usage > 100 ? 'bg-danger' : 'bg-success' }}"
                                                         role="progressbar"
                                                         style="width: {{ min($category->yearly_budget_usage, 100) }}%">
                                                    </div>
                                                </div>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td>{{ $category->expenses_count }}</td>
                                        <td>{{ number_format($category->expenses_sum_amount, 2) }}</td>
                                        <td>
                                            @if($category->is_active)
                                                <span class="badge bg-success">{{ __('نشط') }}</span>
                                            @else
                                                <span class="badge bg-danger">{{ __('غير نشط') }}</span>
                                            @endif
                                            @if($category->is_fixed)
                                                <span class="badge bg-info">{{ __('ثابت') }}</span>
                                            @endif
                                            @if($category->requires_approval)
                                                <span class="badge bg-warning">{{ __('يتطلب موافقة') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('expense-categories.show', $category) }}" class="btn btn-info btn-sm" title="{{ __('عرض') }}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('expense-categories.edit', $category) }}" class="btn btn-primary btn-sm" title="{{ __('تعديل') }}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('expense-categories.destroy', $category) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('{{ __('هل أنت متأكد من حذف هذا التصنيف؟') }}')" title="{{ __('حذف') }}">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center">{{ __('لا توجد تصنيفات') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $categories->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .btn-group {
            flex-direction: row-reverse;
        }
        .rtl .btn-group .btn {
            margin-right: 0;
            margin-left: 0.25rem;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .category-icon {
            object-fit: cover;
            border-radius: 4px;
        }
        .progress {
            background-color: #e9ecef;
            border-radius: 0.25rem;
        }
    </style>
    @endpush

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAll = document.getElementById('select-all');
            const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
            const bulkActionButton = document.getElementById('bulk-action-button');
            const bulkAction = document.getElementById('bulk-action');

            selectAll.addEventListener('change', function() {
                categoryCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActionButton();
            });

            categoryCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateBulkActionButton();
                    selectAll.checked = Array.from(categoryCheckboxes).every(cb => cb.checked);
                });
            });

            bulkAction.addEventListener('change', function() {
                updateBulkActionButton();
            });

            function updateBulkActionButton() {
                const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
                bulkActionButton.disabled = !checkedBoxes.length || !bulkAction.value;
            }

            document.getElementById('bulk-action-form').addEventListener('submit', function(e) {
                if (bulkAction.value === 'delete') {
                    if (!confirm('{{ __("هل أنت متأكد من حذف التصنيفات المحددة؟") }}')) {
                        e.preventDefault();
                    }
                }
            });
        });
    </script>
    @endpush
</x-app-layout>

<x-app-layout>
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-plus-circle text-primary me-2"></i>
                    إضافة مورد جديد
                </h1>
                <p class="text-muted mb-0">إضافة مورد جديد إلى قاعدة البيانات</p>
            </div>
            <a href="{{ user_route('suppliers.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة إلى الموردين
            </a>
        </div>

        <!-- Progress Steps -->
        <div class="card shadow-sm mb-4">
            <div class="card-body py-3">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="step-indicator active">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="col">
                        <h6 class="mb-0 text-primary">الخطوة 1 من 1</h6>
                        <small class="text-muted">إدخال بيانات المورد الجديد</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Form -->
        <form action="{{ user_route('suppliers.store') }}" method="POST" class="rtl" id="supplierForm">
            @csrf

            <div class="row">
                <!-- Basic Information Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header bg-primary text-white">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-user me-2"></i>
                                المعلومات الأساسية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <label for="name" class="form-label fw-bold">
                                    <i class="fas fa-signature text-primary me-1"></i>
                                    اسم المورد
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                    class="form-control form-control-lg @error('name') is-invalid @enderror"
                                    id="name" name="name" value="{{ old('name') }}"
                                    placeholder="أدخل اسم المورد الكامل" required>
                                @error('name')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">اسم المورد كما سيظهر في النظام</div>
                            </div>

                            <div class="mb-4">
                                <label for="phone" class="form-label fw-bold">
                                    <i class="fas fa-phone text-success me-1"></i>
                                    رقم الهاتف
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-phone-alt"></i>
                                    </span>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                        id="phone" name="phone" value="{{ old('phone') }}"
                                        placeholder="01xxxxxxxx" required>
                                </div>
                                @error('phone')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">رقم الهاتف للتواصل مع المورد</div>
                            </div>

                            <div class="mb-4">
                                <label for="email" class="form-label fw-bold">
                                    <i class="fas fa-envelope text-info me-1"></i>
                                    البريد الإلكتروني
                                    <span class="text-muted">(اختياري)</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-at"></i>
                                    </span>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                        id="email" name="email" value="{{ old('email') }}"
                                        placeholder="<EMAIL>">
                                </div>
                                @error('email')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">البريد الإلكتروني للمراسلات الرسمية</div>
                            </div>

                            <div class="mb-0">
                                <label for="tax_number" class="form-label fw-bold">
                                    <i class="fas fa-receipt text-warning me-1"></i>
                                    الرقم الضريبي
                                    <span class="text-muted">(اختياري)</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-hashtag"></i>
                                    </span>
                                    <input type="text" class="form-control @error('tax_number') is-invalid @enderror"
                                        id="tax_number" name="tax_number" value="{{ old('tax_number') }}"
                                        placeholder="300xxxxxxxxx">
                                </div>
                                @error('tax_number')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">الرقم الضريبي للمورد (إن وجد)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header bg-info text-white">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات إضافية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <label for="address" class="form-label fw-bold">
                                    <i class="fas fa-map-marker-alt text-danger me-1"></i>
                                    العنوان
                                    <span class="text-muted">(اختياري)</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-building"></i>
                                    </span>
                                    <textarea class="form-control @error('address') is-invalid @enderror" id="address" name="address" rows="3"
                                        placeholder="أدخل عنوان المورد الكامل">{{ old('address') }}</textarea>
                                </div>
                                @error('address')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">العنوان الفعلي للمورد أو مقر العمل</div>
                            </div>

                            <div class="mb-4">
                                <label for="notes" class="form-label fw-bold">
                                    <i class="fas fa-sticky-note text-secondary me-1"></i>
                                    ملاحظات
                                    <span class="text-muted">(اختياري)</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-comment"></i>
                                    </span>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="4"
                                        placeholder="أي ملاحظات إضافية حول المورد...">{{ old('notes') }}</textarea>
                                </div>
                                @error('notes')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">ملاحظات عامة أو تعليمات خاصة بالمورد</div>
                            </div>

                            <!-- Status Preview -->
                            <div class="alert alert-info border-0 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle fa-2x text-info me-3"></i>
                                    <div>
                                        <h6 class="alert-heading mb-1">حالة المورد</h6>
                                        <p class="mb-0">سيتم تفعيل المورد تلقائياً بعد الحفظ</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    تأكد من صحة البيانات قبل الحفظ
                                </div>
                                <div class="btn-group" role="group">
                                    <a href="{{ user_route('suppliers.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المورد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    @push('styles')
        <style>
            .rtl {
                direction: rtl;
                text-align: right;
            }

            .rtl .form-select {
                text-align: right;
            }

            .rtl .form-control {
                text-align: right;
            }

            .rtl .invalid-feedback {
                text-align: right;
            }

            .rtl .btn i {
                margin-left: 0.5rem;
                margin-right: 0;
            }

            .rtl .text-start {
                text-align: right !important;
            }

            .rtl .text-end {
                text-align: left !important;
            }

            .rtl .card-title {
                text-align: right;
            }

            .step-indicator {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 1.2rem;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }

            .step-indicator.active {
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% {
                    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
                }

                70% {
                    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
                }

                100% {
                    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
                }
            }

            .form-control:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            }

            .input-group-text {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-color: #ced4da;
            }

            .card {
                transition: transform 0.2s ease-in-out;
            }

            .card:hover {
                transform: translateY(-2px);
            }

            .btn-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                transition: all 0.3s ease;
            }

            .btn-primary:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }

            .text-gray-800 {
                color: #5a5c69 !important;
            }

            .alert-info {
                background: linear-gradient(135deg, rgba(54, 185, 204, 0.1) 0%, rgba(54, 185, 204, 0.05) 100%);
                border-left: 4px solid #36b9cc;
            }

            .form-text {
                font-size: 0.875rem;
                color: #6c757d;
            }

            .rtl .me-1 {
                margin-left: 0.25rem !important;
                margin-right: 0 !important;
            }

            .rtl .me-2 {
                margin-left: 0.5rem !important;
                margin-right: 0 !important;
            }

            .rtl .me-3 {
                margin-left: 1rem !important;
                margin-right: 0 !important;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Form validation and submission
                const form = document.getElementById('supplierForm');
                const submitBtn = document.getElementById('submitBtn');

                // Add loading state to submit button
                form.addEventListener('submit', function(e) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
                    submitBtn.disabled = true;
                });

                // Real-time validation feedback
                const requiredFields = ['name', 'phone'];

                requiredFields.forEach(fieldName => {
                    const field = document.getElementById(fieldName);
                    if (field) {
                        field.addEventListener('blur', function() {
                            validateField(this);
                        });

                        field.addEventListener('input', function() {
                            if (this.classList.contains('is-invalid')) {
                                validateField(this);
                            }
                        });
                    }
                });

                function validateField(field) {
                    const value = field.value.trim();
                    const isValid = value.length > 0;

                    if (isValid) {
                        field.classList.remove('is-invalid');
                        field.classList.add('is-valid');
                    } else {
                        field.classList.remove('is-valid');
                        field.classList.add('is-invalid');
                    }
                }

                // Phone number formatting
                const phoneField = document.getElementById('phone');
                if (phoneField) {
                    phoneField.addEventListener('input', function(e) {
                        let value = e.target.value.replace(/\D/g, '');
                        if (value.length > 10) {
                            value = value.substring(0, 10);
                        }
                        e.target.value = value;
                    });
                }

                // Auto-resize textareas
                const textareas = document.querySelectorAll('textarea');
                textareas.forEach(textarea => {
                    textarea.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = (this.scrollHeight) + 'px';
                    });
                });
            });
        </script>
    @endpush
</x-app-layout>

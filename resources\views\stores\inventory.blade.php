<x-app-layout>
    <div class="container-fluid">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">مخزون المخزن: {{ $store->name }}</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('admin.stores.index') }}">المخازن</a></li>
                                <li class="breadcrumb-item"><a
                                        href="{{ route('admin.stores.show', $store) }}">{{ $store->name }}</a></li>
                                <li class="breadcrumb-item active">المخزون</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="btn-group">
                        <a href="{{ route('admin.stores.add-product', $store) }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة منتجات
                        </a>
                        <div class="btn-group">
                            <a href="{{ route('admin.transfers.direct.create') }}?source_type=store&source_id={{ $store->id }}"
                                class="btn btn-success">
                                <i class="fas fa-arrow-right"></i> نقل من المخزن
                            </a>
                            <a href="{{ route('admin.transfers.direct.create') }}?destination_type=store&destination_id={{ $store->id }}"
                                class="btn btn-outline-success">
                                <i class="fas fa-arrow-left"></i> نقل إلى المخزن
                            </a>
                        </div>
                        <a href="{{ route('admin.stores.show', $store) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">إجمالي المنتجات</h6>
                                <h3 class="mb-0">{{ number_format($stats['total_products']) }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-boxes fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">مخزون منخفض</h6>
                                <h3 class="mb-0">{{ number_format($stats['low_stock_count']) }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">نفد المخزون</h6>
                                <h3 class="mb-0">{{ number_format($stats['out_of_stock_count']) }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">قيمة المخزون</h6>
                                <h3 class="mb-0">{{ number_format($stats['total_value'], 2) }} ج.م</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" action="{{ route('admin.stores.inventory', $store) }}">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                        value="{{ request('search') }}" placeholder="اسم المنتج أو الباركود">
                                </div>
                                <div class="col-md-3">
                                    <label for="stock_status" class="form-label">حالة المخزون</label>
                                    <select class="form-select" id="stock_status" name="stock_status">
                                        <option value="">جميع الحالات</option>
                                        <option value="low"
                                            {{ request('stock_status') === 'low' ? 'selected' : '' }}>مخزون منخفض
                                        </option>
                                        <option value="out"
                                            {{ request('stock_status') === 'out' ? 'selected' : '' }}>نفد المخزون
                                        </option>
                                        <option value="over"
                                            {{ request('stock_status') === 'over' ? 'selected' : '' }}>مخزون زائد
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="category_id" class="form-label">الفئة</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <option value="">جميع الفئات</option>
                                        @foreach ($categories as $category)
                                            @if ($category)
                                                <option value="{{ $category->id }}"
                                                    {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                                    {{ $category->name }}
                                                </option>
                                            @endif
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="{{ route('admin.stores.inventory', $store) }}"
                                        class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-warehouse text-primary"></i> مخزون المنتجات
                        </h5>
                    </div>
                    <div class="card-body">
                        @if ($inventory->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الفئة</th>
                                            <th>الكمية المتاحة</th>
                                            <th>الحد الأدنى</th>
                                            <th>الحد الأقصى</th>
                                            <th>حالة المخزون</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($inventory as $item)
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        @if ($item->product->image)
                                                            <img src="{{ asset('storage/' . $item->product->image) }}"
                                                                alt="{{ $item->product->name }}" class="rounded me-2"
                                                                style="width: 40px; height: 40px; object-fit: cover;">
                                                        @else
                                                            <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center"
                                                                style="width: 40px; height: 40px;">
                                                                <i class="fas fa-box text-muted"></i>
                                                            </div>
                                                        @endif
                                                        <div>
                                                            <div class="fw-bold">{{ $item->product->name }}</div>
                                                            <small
                                                                class="text-muted">{{ $item->product->barcode }}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    @if ($item->product->category)
                                                        <span
                                                            class="badge bg-info">{{ $item->product->category->name }}</span>
                                                    @else
                                                        <span class="text-muted">غير محدد</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <span
                                                        class="fw-bold">{{ number_format($item->quantity, 2) }}</span>
                                                    <small
                                                        class="text-muted">{{ $item->product->unit ?? 'قطعة' }}</small>
                                                </td>
                                                <td>{{ number_format($item->minimum_stock, 2) }}</td>
                                                <td>{{ $item->maximum_stock ? number_format($item->maximum_stock, 2) : '-' }}
                                                </td>
                                                <td>
                                                    <span class="badge {{ $item->getStockStatusBadgeClass() }}">
                                                        {{ $item->getStockStatusText() }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#editInventoryModal{{ $item->id }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        @if ($item->quantity > 0)
                                                            <a href="{{ route('admin.transfers.direct.create') }}?source_type=store&source_id={{ $store->id }}&product_id={{ $item->product_id }}"
                                                                class="btn btn-outline-success" title="نقل سريع">
                                                                <i class="fas fa-arrow-right"></i>
                                                            </a>
                                                        @endif
                                                        @if ($item->quantity <= 0)
                                                            <button type="button" class="btn btn-outline-danger"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#deleteInventoryModal{{ $item->id }}">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>

                                            <!-- Edit Modal -->
                                            <div class="modal fade" id="editInventoryModal{{ $item->id }}"
                                                tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <form
                                                            action="{{ route('admin.stores.update-inventory', [$store, $item]) }}"
                                                            method="POST">
                                                            @csrf
                                                            @method('PUT')
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">تعديل مخزون:
                                                                    {{ $item->product->name }}</h5>
                                                                <button type="button" class="btn-close"
                                                                    data-bs-dismiss="modal"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="mb-3">
                                                                    <label for="quantity{{ $item->id }}"
                                                                        class="form-label">الكمية المتاحة</label>
                                                                    <input type="number" class="form-control"
                                                                        id="quantity{{ $item->id }}"
                                                                        name="quantity" value="{{ $item->quantity }}"
                                                                        step="0.01" min="0" required>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label for="minimum_stock{{ $item->id }}"
                                                                        class="form-label">الحد الأدنى</label>
                                                                    <input type="number" class="form-control"
                                                                        id="minimum_stock{{ $item->id }}"
                                                                        name="minimum_stock"
                                                                        value="{{ $item->minimum_stock }}"
                                                                        step="0.01" min="0" required>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label for="maximum_stock{{ $item->id }}"
                                                                        class="form-label">الحد الأقصى</label>
                                                                    <input type="number" class="form-control"
                                                                        id="maximum_stock{{ $item->id }}"
                                                                        name="maximum_stock"
                                                                        value="{{ $item->maximum_stock }}"
                                                                        step="0.01" min="0">
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary"
                                                                    data-bs-dismiss="modal">إلغاء</button>
                                                                <button type="submit" class="btn btn-primary">حفظ
                                                                    التغييرات</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Delete Modal -->
                                            @if ($item->quantity <= 0)
                                                <div class="modal fade" id="deleteInventoryModal{{ $item->id }}"
                                                    tabindex="-1">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <form
                                                                action="{{ route('admin.stores.remove-product', [$store, $item]) }}"
                                                                method="POST">
                                                                @csrf
                                                                @method('DELETE')
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title">حذف المنتج من المخزن</h5>
                                                                    <button type="button" class="btn-close"
                                                                        data-bs-dismiss="modal"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <p>هل أنت متأكد من حذف المنتج
                                                                        <strong>{{ $item->product->name }}</strong> من
                                                                        مخزون هذا المخزن؟
                                                                    </p>
                                                                    <div class="alert alert-warning">
                                                                        <i class="fas fa-exclamation-triangle"></i>
                                                                        هذا الإجراء لا يمكن التراجع عنه.
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary"
                                                                        data-bs-dismiss="modal">إلغاء</button>
                                                                    <button type="submit" class="btn btn-danger">حذف
                                                                        المنتج</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-center">
                                {{ $inventory->appends(request()->query())->links() }}
                            </div>
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد منتجات في المخزون</h5>
                                <p class="text-muted">ابدأ بإضافة منتجات إلى هذا المخزن</p>
                                <a href="{{ route('admin.stores.add-product', $store) }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة منتجات
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

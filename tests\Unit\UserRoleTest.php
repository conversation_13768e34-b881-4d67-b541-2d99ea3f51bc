<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase as BaseTestCase;

class UserRoleTest extends BaseTestCase
{
    use RefreshDatabase;

    public function test_user_role_relationship(): void
    {
        // Create a role
        $role = Role::create([
            'name' => 'admin',
            'description' => 'Administrator',
            'permissions' => ['users.view', 'users.create'],
            'is_active' => true,
        ]);

        // Create a user with the role
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $role->id,
            'is_active' => true,
        ]);

        // Test the relationship
        $this->assertInstanceOf(Role::class, $user->role);
        $this->assertEquals('admin', $user->role->name);
    }

    public function test_user_has_permission_method(): void
    {
        // Create a role with specific permissions
        $role = Role::create([
            'name' => 'seller',
            'description' => 'Seller',
            'permissions' => ['sales.create', 'products.view'],
            'is_active' => true,
        ]);

        // Create a user with the role
        $user = User::create([
            'name' => 'Seller User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $role->id,
            'is_active' => true,
        ]);

        // Test permission checking
        $this->assertTrue($user->hasPermission('sales.create'));
        $this->assertTrue($user->hasPermission('products.view'));
        $this->assertFalse($user->hasPermission('users.delete'));
    }

    public function test_user_role_checking_methods(): void
    {
        // Create admin role
        $adminRole = Role::create([
            'name' => 'admin',
            'description' => 'Administrator',
            'permissions' => ['users.view'],
            'is_active' => true,
        ]);

        // Create seller role
        $sellerRole = Role::create([
            'name' => 'seller',
            'description' => 'Seller',
            'permissions' => ['sales.create'],
            'is_active' => true,
        ]);

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $adminRole->id,
            'is_active' => true,
        ]);

        // Create seller user
        $seller = User::create([
            'name' => 'Seller User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $sellerRole->id,
            'is_active' => true,
        ]);

        // Test role checking methods
        $this->assertTrue($admin->isAdmin());
        $this->assertFalse($admin->isSeller());
        $this->assertTrue($admin->hasRole('admin'));
        $this->assertFalse($admin->hasRole('seller'));

        $this->assertFalse($seller->isAdmin());
        $this->assertTrue($seller->isSeller());
        $this->assertTrue($seller->hasRole('seller'));
        $this->assertFalse($seller->hasRole('admin'));
    }

    public function test_inactive_role_permissions(): void
    {
        // Create an inactive role
        $inactiveRole = Role::create([
            'name' => 'inactive',
            'description' => 'Inactive Role',
            'permissions' => ['sales.create'],
            'is_active' => false,
        ]);

        // Create a user with the inactive role
        $user = User::create([
            'name' => 'Inactive User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $inactiveRole->id,
            'is_active' => true,
        ]);

        // User with inactive role should not have permissions
        $this->assertFalse($user->hasPermission('sales.create'));
    }
}

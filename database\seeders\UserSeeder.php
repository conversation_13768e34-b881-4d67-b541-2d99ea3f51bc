<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use App\Models\Branch;
use App\Models\Store;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Get roles
        $adminRole = Role::where('name', 'admin')->first();
        $managerRole = Role::where('name', 'manager')->first();
        $sellerRole = Role::where('name', 'seller')->first();

        // Create default admin user
        $admin = User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'password' => Hash::make('admin123'),
            'role_id' => $adminRole->id,
            'branch_id' => null, // Admin can access all branches
            'store_id' => null,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create sample branches if they don't exist
        $mainBranch = Branch::firstOrCreate([
            'code' => 'MAIN',
        ], [
            'name' => 'Main Branch',
            'address' => '123 Main Street, City Center',
            'phone' => '+1234567891',
            'email' => '<EMAIL>',
            'opening_balance' => 10000.00,
            'is_active' => true,
        ]);

        $secondBranch = Branch::firstOrCreate([
            'code' => 'BRANCH2',
        ], [
            'name' => 'Second Branch',
            'address' => '456 Second Street, Downtown',
            'phone' => '+1234567892',
            'email' => '<EMAIL>',
            'opening_balance' => 5000.00,
            'is_active' => true,
        ]);

        // Create sample stores
        $mainStore = Store::firstOrCreate([
            'code' => 'STORE001',
            'branch_id' => $mainBranch->id,
        ], [
            'name' => 'Main Store',
            'address' => '123 Main Street, Ground Floor',
            'phone' => '+1234567893',
            'email' => '<EMAIL>',
            'opening_balance' => 5000.00,
            'is_active' => true,
        ]);

        $secondStore = Store::firstOrCreate([
            'code' => 'STORE002',
            'branch_id' => $secondBranch->id,
        ], [
            'name' => 'Second Store',
            'address' => '456 Second Street, First Floor',
            'phone' => '+1234567894',
            'email' => '<EMAIL>',
            'opening_balance' => 3000.00,
            'is_active' => true,
        ]);

        // Create branch manager for main branch
        $mainManager = User::create([
            'name' => 'Main Branch Manager',
            'email' => '<EMAIL>',
            'phone' => '+1234567895',
            'password' => Hash::make('manager123'),
            'role_id' => $managerRole->id,
            'branch_id' => $mainBranch->id,
            'store_id' => $mainStore->id,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create seller for main branch
        $mainSeller = User::create([
            'name' => 'Main Branch Seller',
            'email' => '<EMAIL>',
            'phone' => '+1234567896',
            'password' => Hash::make('seller123'),
            'role_id' => $sellerRole->id,
            'branch_id' => $mainBranch->id,
            'store_id' => $mainStore->id,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create seller for second branch
        $secondSeller = User::create([
            'name' => 'Second Branch Seller',
            'email' => '<EMAIL>',
            'phone' => '+1234567897',
            'password' => Hash::make('seller123'),
            'role_id' => $sellerRole->id,
            'branch_id' => $secondBranch->id,
            'store_id' => $secondStore->id,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Update store managers
        $mainStore->update(['manager_id' => $mainManager->id]);
        $secondStore->update(['manager_id' => $secondSeller->id]);

        $this->command->info('Users created successfully!');
        $this->command->info('Admin: <EMAIL> / admin123');
        $this->command->info('Manager: <EMAIL> / manager123');
        $this->command->info('Seller: <EMAIL> / seller123');
        $this->command->info('Seller 2: <EMAIL> / seller123');
    }
}

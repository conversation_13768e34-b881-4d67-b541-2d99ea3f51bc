<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Helpers\AlertHelper;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Category::withCount('products');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', '%' . $searchTerm . '%')
                    ->orWhere('description', 'like', '%' . $searchTerm . '%');
            });
        }

        // Filter functionality
        if ($request->has('filter') && $request->filter) {
            switch ($request->filter) {
                case 'with_products':
                    $query->has('products');
                    break;
                case 'empty':
                    $query->doesntHave('products');
                    break;
            }
        }

        // Sorting functionality
        if ($request->has('sort') && $request->sort) {
            switch ($request->sort) {
                case 'name_asc':
                    $query->orderBy('name', 'asc');
                    break;
                case 'name_desc':
                    $query->orderBy('name', 'desc');
                    break;
                case 'products_count_desc':
                    $query->orderBy('products_count', 'desc');
                    break;
                case 'created_at_desc':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'created_at_asc':
                    $query->orderBy('created_at', 'asc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $categories = $query->paginate(10)->withQueryString();

        // Overall statistics (not dependent on pagination)
        $totalCategories = Category::count();
        $categoriesWithProducts = Category::has('products')->count();
        $emptyCategories = Category::doesntHave('products')->count();
        $totalProducts = Category::withCount('products')->get()->sum('products_count');

        return view('categories.index', compact(
            'categories',
            'totalCategories',
            'categoriesWithProducts',
            'emptyCategories',
            'totalProducts'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:categories',
            'description' => 'nullable|string|max:1000'
        ]);

        Category::create($validated);
        AlertHelper::success('تم إضافة الفئة بنجاح');

        return redirect(user_route('categories.index'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category)
    {
        $category->load([
            'products' => function ($query) {
                $query->with([
                    'branchInventories',
                    'storeInventories'
                ]);
            }
        ]);

        // Calculate total stock for each product
        $category->products->each(function ($product) {
            $branchStock = $product->branchInventories->sum('quantity');
            $storeStock = $product->storeInventories->sum('quantity');
            $product->total_stock = $branchStock + $storeStock;
        });

        return view('categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Category $category)
    {
        $category->loadCount('products');
        $category->load([
            'products' => function ($query) {
                $query->with([
                    'branchInventories',
                    'storeInventories'
                ]);
            }
        ]);

        // Calculate total stock for each product
        $category->products->each(function ($product) {
            $branchStock = $product->branchInventories->sum('quantity');
            $storeStock = $product->storeInventories->sum('quantity');
            $product->total_stock = $branchStock + $storeStock;
        });

        return view('categories.edit', compact('category'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:categories,name,' . $category->id,
            'description' => 'nullable|string'
        ]);

        $category->update($validated);
        AlertHelper::success('تم تحديث الفئة بنجاح');

        return redirect(user_route('categories.index'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Category $category)
    {
        if ($category->products()->count() > 0) {
            AlertHelper::error('لا يمكن حذف الفئة لوجود منتجات مرتبطة بها');
            return redirect(user_route('categories.index'));
        }

        $category->delete();
        AlertHelper::success('تم حذف الفئة بنجاح');

        return redirect(user_route('categories.index'));
    }
}

<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Branch;
use App\Models\BranchInventory;
use App\Models\Customer;
use App\Models\Sale;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SaleControllerPriceTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $admin;
    protected $branch;
    protected $product;
    protected $customer;

    protected function setUp(): void
    {
        parent::setUp();

        $this->branch = Branch::factory()->create();
        $this->user = User::factory()->create(['branch_id' => $this->branch->id]);
        $this->user->assignRole('seller');
        
        $this->admin = User::factory()->create(['branch_id' => $this->branch->id]);
        $this->admin->assignRole('admin');

        $this->product = Product::factory()->create([
            'cost_price' => 80.00,
            'selling_price' => 120.00
        ]);

        $this->customer = Customer::factory()->create();

        // Create branch inventory
        BranchInventory::create([
            'branch_id' => $this->branch->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'cost_price' => 85.00,
            'sale_price_1' => 130.00
        ]);
    }

    public function test_creates_sale_with_valid_price()
    {
        $response = $this->actingAs($this->user)->post(route('seller.sales.store'), [
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'discount_amount' => 0,
            'paid_amount' => 130.00,
            'notes' => 'Test sale',
            'products' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 1,
                    'price' => 130.00 // Valid branch price
                ]
            ]
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('sales', [
            'total_amount' => 130.00,
            'user_id' => $this->user->id
        ]);
    }

    public function test_rejects_sale_with_price_below_cost_for_seller()
    {
        $response = $this->actingAs($this->user)->post(route('seller.sales.store'), [
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'discount_amount' => 0,
            'paid_amount' => 70.00,
            'notes' => 'Test sale',
            'products' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 1,
                    'price' => 70.00 // Below cost price
                ]
            ]
        ]);

        $response->assertSessionHas('error');
        $this->assertStringContains('لا يمكن البيع بسعر أقل من سعر التكلفة', session('error'));
    }

    public function test_allows_sale_with_price_below_cost_for_admin()
    {
        $response = $this->actingAs($this->admin)->post(route('admin.sales.store'), [
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'discount_amount' => 0,
            'paid_amount' => 70.00,
            'notes' => 'Admin override sale',
            'products' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 1,
                    'price' => 70.00 // Below cost price
                ]
            ]
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('sales', [
            'total_amount' => 70.00,
            'user_id' => $this->admin->id
        ]);
    }

    public function test_logs_price_warnings_for_low_margin_sales()
    {
        $response = $this->actingAs($this->user)->post(route('seller.sales.store'), [
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'discount_amount' => 0,
            'paid_amount' => 87.00,
            'notes' => 'Low margin sale',
            'products' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 1,
                    'price' => 87.00 // Low margin (2.35%)
                ]
            ]
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Check that warning is included in success message
        $successMessage = session('success');
        $this->assertStringContains('تحذيرات', $successMessage);
    }

    public function test_get_product_price_endpoint()
    {
        $response = $this->actingAs($this->user)->get(route('seller.sales.product-price'), [
            'product_id' => $this->product->id,
            'branch_id' => $this->branch->id
        ]);

        $response->assertOk();
        $response->assertJson([
            'success' => true,
            'price_info' => [
                'price' => 130.00,
                'source' => 'branch_inventory',
                'cost_price' => 85.00
            ]
        ]);
    }

    public function test_validate_price_endpoint()
    {
        $response = $this->actingAs($this->user)->post(route('seller.sales.validate-price'), [
            'product_id' => $this->product->id,
            'branch_id' => $this->branch->id,
            'price' => 140.00
        ]);

        $response->assertOk();
        $response->assertJson([
            'success' => true,
            'validation' => [
                'valid' => true,
                'message' => 'السعر مقبول'
            ]
        ]);
    }

    public function test_validate_price_endpoint_rejects_low_price()
    {
        $response = $this->actingAs($this->user)->post(route('seller.sales.validate-price'), [
            'product_id' => $this->product->id,
            'branch_id' => $this->branch->id,
            'price' => 70.00 // Below cost
        ]);

        $response->assertOk();
        $response->assertJson([
            'success' => true,
            'validation' => [
                'valid' => false,
                'message' => 'لا يمكن البيع بسعر أقل من سعر التكلفة'
            ]
        ]);
    }

    public function test_updates_sale_with_price_validation()
    {
        $sale = Sale::factory()->create([
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 130.00,
            'status' => 'pending'
        ]);

        $response = $this->actingAs($this->user)->put(route('seller.sales.update', $sale), [
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'discount_amount' => 0,
            'paid_amount' => 140.00,
            'notes' => 'Updated sale',
            'products' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 1,
                    'price' => 140.00 // Valid price
                ]
            ]
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('sales', [
            'id' => $sale->id,
            'total_amount' => 140.00
        ]);
    }

    public function test_json_response_includes_warnings()
    {
        $response = $this->actingAs($this->user)
            ->withHeaders(['Accept' => 'application/json'])
            ->post(route('seller.sales.store'), [
                'customer_id' => $this->customer->id,
                'branch_id' => $this->branch->id,
                'discount_amount' => 0,
                'paid_amount' => 87.00,
                'notes' => 'API sale',
                'products' => [
                    [
                        'product_id' => $this->product->id,
                        'quantity' => 1,
                        'price' => 87.00 // Low margin
                    ]
                ]
            ]);

        $response->assertOk();
        $response->assertJson([
            'success' => true,
            'message' => 'تم إنشاء عملية البيع بنجاح'
        ]);
        
        $responseData = $response->json();
        $this->assertArrayHasKey('warnings', $responseData);
        $this->assertNotEmpty($responseData['warnings']);
    }

    public function test_denies_access_to_other_branch_products()
    {
        $otherBranch = Branch::factory()->create();
        
        $response = $this->actingAs($this->user)->get(route('seller.sales.product-price'), [
            'product_id' => $this->product->id,
            'branch_id' => $otherBranch->id
        ]);

        $response->assertStatus(403);
    }

    public function test_handles_nonexistent_product_gracefully()
    {
        $response = $this->actingAs($this->user)->get(route('seller.sales.product-price'), [
            'product_id' => 99999,
            'branch_id' => $this->branch->id
        ]);

        $response->assertStatus(422);
    }
}

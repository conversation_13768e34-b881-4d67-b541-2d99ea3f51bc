<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add the new status value to the enum
        DB::statement("ALTER TABLE purchases MODIFY COLUMN status ENUM('pending', 'pending_distribution', 'completed', 'cancelled') DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the pending_distribution status (but first update any existing records)
        DB::statement("UPDATE purchases SET status = 'pending' WHERE status = 'pending_distribution'");
        DB::statement("ALTER TABLE purchases MODIFY COLUMN status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending'");
    }
};

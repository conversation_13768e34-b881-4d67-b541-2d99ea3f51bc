<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-boxes text-primary"></i> توزيع المشتريات
                </h2>
                <p class="text-muted small mb-0">توزيع منتجات المشتريات على الفروع والمخازن</p>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="container-fluid">
        <!-- Stepper -->
        

        <!-- Purchase Info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-receipt"></i> معلومات المشتريات</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>رقم الفاتورة:</strong><br>
                                <span class="badge bg-primary"><?php echo e($purchase->id); ?></span>
                            </div>
                            <div class="col-6">
                                <strong>المورد:</strong><br>
                                <?php echo e($purchase->supplier->name); ?>

                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>إجمالي المبلغ:</strong><br>
                                <?php echo e(number_format($purchase->total_amount, 2)); ?> ج.م
                            </div>
                            <div class="col-6">
                                <strong>تاريخ الشراء:</strong><br>
                                <?php echo e($purchase->purchase_date); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> تنبيه مهم</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-2"><strong>يجب توزيع جميع المنتجات لإكمال عملية الشراء</strong></p>
                        <ul class="mb-0">
                            <li>اختر الفرع أو المخزن المناسب لكل منتج</li>
                            <li>لا يمكن تعديل التوزيع بعد التأكيد</li>
                            <li>ستتم إضافة المنتجات للمخزون فور التأكيد</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Distribution Form -->
        <form method="POST" action="<?php echo e(route('admin.purchases.distribute.store', $purchase)); ?>">
            <?php echo csrf_field(); ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-boxes"></i> توزيع المنتجات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <?php $__currentLoopData = $purchase->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="mb-0">
                                                <strong><?php echo e($item->product->name); ?></strong>
                                                <small
                                                    class="text-muted d-block"><?php echo e($item->product->sku ?? 'لا يوجد كود'); ?></small>
                                            </h6>
                                        </div>
                                        <div class="col-md-6 text-end">
                                            <span class="badge bg-info me-2">الكمية:
                                                <?php echo e(number_format($item->quantity)); ?></span>
                                            <span class="badge bg-secondary me-2">التكلفة:
                                                <?php echo e(number_format($item->cost_price, 2)); ?> ج.م</span>
                                            <span class="badge bg-success">الإجمالي:
                                                <?php echo e(number_format($item->total_price, 2)); ?> ج.م</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="distribution-container" data-item-id="<?php echo e($item->id); ?>"
                                        data-total-quantity="<?php echo e($item->quantity); ?>"
                                        data-cost-price="<?php echo e($item->cost_price); ?>"
                                        data-product-id="<?php echo e($item->product_id); ?>">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">توزيع المنتج</h6>
                                            <div>
                                                <span class="remaining-quantity badge bg-warning">متبقي:
                                                    <?php echo e(number_format($item->quantity)); ?></span>
                                                <button type="button" class="btn btn-sm btn-primary add-distribution"
                                                    data-item-id="<?php echo e($item->id); ?>">
                                                    <i class="fas fa-plus"></i> إضافة توزيع
                                                </button>
                                            </div>
                                        </div>

                                        <div class="distributions-list">
                                            <!-- Distribution rows will be added here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <div class="d-flex gap-2">
                            
                            <a href="<?php echo e(user_route('purchases.print', $purchase)); ?>" class="btn btn-outline-info"
                                target="_blank">
                                <i class="fas fa-print"></i> طباعة الفاتورة
                            </a>
                        </div>
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-check"></i> تأكيد التوزيع وإضافة للمخزون
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <?php $__env->startPush('styles'); ?>
        <style>
            .stepper {
                display: flex !important;
                flex-direction: row !important;
                align-items: flex-start !important;
                justify-content: space-between !important;
                padding: 20px 0;
                position: relative;
                width: 100% !important;
                max-width: 800px;
                margin: 0 auto;
                gap: 20px;
                border: 2px solid red !important;
                /* Debug - remove after testing */
            }

            .step {
                display: flex !important;
                flex-direction: column !important;
                align-items: center !important;
                text-align: center !important;
                position: relative;
                z-index: 2;
                flex: 1 !important;
                max-width: 200px;
                min-width: 120px;
                border: 1px solid blue !important;
                /* Debug - remove after testing */
            }

            .step-number {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: #f8f9fa;
                border: 3px solid #e9ecef;
                color: #6c757d;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 16px;
                margin-bottom: 12px;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .step.completed .step-number {
                background: linear-gradient(135deg, #28a745, #20c997);
                border-color: #28a745;
                color: white;
                box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
            }

            .step.active .step-number {
                background: linear-gradient(135deg, #007bff, #0056b3);
                border-color: #007bff;
                color: white;
                box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% {
                    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
                }

                50% {
                    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.5);
                }

                100% {
                    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
                }
            }

            .step-line {
                position: absolute;
                top: 25px;
                left: 50%;
                right: 50%;
                height: 4px;
                background: #e9ecef;
                z-index: 1;
                border-radius: 2px;
                transition: all 0.3s ease;
            }

            .stepper::before {
                content: '';
                position: absolute;
                top: 25px;
                left: 25px;
                right: 25px;
                height: 4px;
                background: #e9ecef;
                z-index: 1;
                border-radius: 2px;
            }

            .stepper::after {
                content: '';
                position: absolute;
                top: 25px;
                left: 25px;
                width: 33.33%;
                height: 4px;
                background: linear-gradient(90deg, #28a745, #20c997);
                z-index: 1;
                border-radius: 2px;
                transition: width 0.3s ease;
            }

            .step-content h6 {
                margin: 0 0 4px 0;
                font-size: 15px;
                font-weight: 600;
                color: #495057;
            }

            .step.completed .step-content h6 {
                color: #28a745;
            }

            .step.active .step-content h6 {
                color: #007bff;
            }

            .step-content small {
                color: #6c757d;
                font-size: 12px;
                line-height: 1.3;
                display: block;
                max-width: 100px;
            }

            .step.completed .step-content small {
                color: #28a745;
            }

            .step.active .step-content small {
                color: #007bff;
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .stepper {
                    padding: 15px 10px;
                    justify-content: space-around;
                }

                .step {
                    flex: 1;
                    max-width: 120px;
                }

                .step-number {
                    width: 40px;
                    height: 40px;
                    font-size: 14px;
                }

                .step:not(:last-child)::after {
                    top: 20px;
                    left: calc(50% + 20px);
                    right: calc(-100% + 20px);
                }

                .step-content h6 {
                    font-size: 12px;
                }

                .step-content small {
                    font-size: 10px;
                    max-width: 90px;
                }
            }

            @media (max-width: 480px) {
                .stepper {
                    padding: 10px 5px;
                }

                .step-content h6 {
                    font-size: 11px;
                }

                .step-content small {
                    font-size: 9px;
                    max-width: 70px;
                }
            }
        </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const branches = <?php echo json_encode($branches, 15, 512) ?>;
                const stores = <?php echo json_encode($stores, 15, 512) ?>;
                let distributionCounter = 0;

                // Add distribution row
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('add-distribution') || e.target.closest(
                            '.add-distribution')) {
                        const button = e.target.closest('.add-distribution');
                        const itemId = button.dataset.itemId;
                        const container = document.querySelector(
                            `[data-item-id="${itemId}"] .distributions-list`);

                        addDistributionRow(container, itemId);
                    }
                });

                // Remove distribution row
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('remove-distribution') || e.target.closest(
                            '.remove-distribution')) {
                        const button = e.target.closest('.remove-distribution');
                        const row = button.closest('.distribution-row');
                        const itemId = row.dataset.itemId;

                        row.remove();
                        updateRemainingQuantity(itemId);
                    }
                });

                // Handle location type change
                document.addEventListener('change', function(e) {
                    if (e.target.classList.contains('location-type')) {
                        const select = e.target;
                        const row = select.closest('.distribution-row');
                        const locationSelect = row.querySelector('.location-id');
                        const locationType = select.value;

                        // Clear and enable location select
                        locationSelect.innerHTML = '<option value="">اختر الموقع</option>';
                        locationSelect.disabled = !locationType;

                        if (locationType === 'branch') {
                            branches.forEach(branch => {
                                locationSelect.innerHTML +=
                                    `<option value="${branch.id}">${branch.name}</option>`;
                            });
                        } else if (locationType === 'store') {
                            stores.forEach(store => {
                                locationSelect.innerHTML +=
                                    `<option value="${store.id}">${store.name}</option>`;
                            });
                        }

                        // Clear sale prices when location type changes
                        row.querySelector('.sale-price-1').value = '';
                        row.querySelector('.sale-price-2').value = '';
                        row.querySelector('.sale-price-3').value = '';
                    }
                });

                // Handle location change - detect sale prices
                document.addEventListener('change', function(e) {
                    if (e.target.classList.contains('location-id')) {
                        const select = e.target;
                        const row = select.closest('.distribution-row');
                        const locationId = select.value;
                        const locationType = row.querySelector('.location-type').value;
                        const productId = row.dataset.productId;

                        if (locationId && locationType && productId) {
                            detectSalePrices(row, locationType, locationId, productId);
                        }
                    }
                });

                // Handle quantity change
                document.addEventListener('input', function(e) {
                    if (e.target.classList.contains('distribution-quantity')) {
                        const input = e.target;
                        const itemId = input.closest('.distribution-row').dataset.itemId;
                        updateRemainingQuantity(itemId);
                    }
                });

                function addDistributionRow(container, itemId) {
                    const distributionContainer = container.closest('.distribution-container');
                    const totalQuantity = parseFloat(distributionContainer.dataset.totalQuantity);
                    const costPrice = parseFloat(distributionContainer.dataset.costPrice);
                    const productId = distributionContainer.dataset.productId || '';

                    distributionCounter++;
                    const rowId = `dist_${itemId}_${distributionCounter}`;

                    const row = document.createElement('div');
                    row.className = 'distribution-row border rounded p-3 mb-3';
                    row.dataset.itemId = itemId;
                    row.dataset.productId = productId;

                    row.innerHTML = `
                        <div class="row">
                            <div class="col-md-2">
                                <label class="form-label">النوع</label>
                                <select name="distributions[${itemId}][${rowId}][location_type]" class="form-select location-type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="branch">فرع</option>
                                    <option value="store">مخزن</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الموقع</label>
                                <select name="distributions[${itemId}][${rowId}][location_id]" class="form-select location-id" required disabled>
                                    <option value="">اختر الموقع</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">الكمية</label>
                                <input type="number" name="distributions[${itemId}][${rowId}][quantity]"
                                       class="form-control distribution-quantity" step="0.01" min="0.01" max="${totalQuantity}" required>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">سعر البيع 1</label>
                                <input type="number" name="distributions[${itemId}][${rowId}][sale_price_1]"
                                       class="form-control sale-price-1" step="0.01" min="${costPrice}" required>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">سعر البيع 2</label>
                                <input type="number" name="distributions[${itemId}][${rowId}][sale_price_2]"
                                       class="form-control sale-price-2" step="0.01" min="${costPrice}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">سعر البيع 3</label>
                                <input type="number" name="distributions[${itemId}][${rowId}][sale_price_3]"
                                       class="form-control sale-price-3" step="0.01" min="${costPrice}">
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="button" class="btn btn-danger btn-sm remove-distribution">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;

                    container.appendChild(row);
                    updateRemainingQuantity(itemId);
                }

                function updateRemainingQuantity(itemId) {
                    const container = document.querySelector(`[data-item-id="${itemId}"]`);
                    const totalQuantity = parseFloat(container.dataset.totalQuantity);
                    const quantityInputs = container.querySelectorAll('.distribution-quantity');

                    let distributedQuantity = 0;
                    quantityInputs.forEach(input => {
                        const value = parseFloat(input.value) || 0;
                        distributedQuantity += value;
                    });

                    const remaining = totalQuantity - distributedQuantity;
                    const remainingBadge = container.querySelector('.remaining-quantity');
                    remainingBadge.textContent = `متبقي: ${remaining.toFixed(2)}`;

                    // Update badge color based on remaining quantity
                    remainingBadge.className = 'remaining-quantity badge ' +
                        (remaining === 0 ? 'bg-success' : remaining < 0 ? 'bg-danger' : 'bg-warning');
                }

                async function detectSalePrices(row, locationType, locationId, productId) {
                    try {
                        const response = await fetch(`/admin/products/${productId}/sale-prices`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content')
                            },
                            body: JSON.stringify({
                                location_type: locationType,
                                location_id: locationId
                            })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.sale_price_1) row.querySelector('.sale-price-1').value = data.sale_price_1;
                            if (data.sale_price_2) row.querySelector('.sale-price-2').value = data.sale_price_2;
                            if (data.sale_price_3) row.querySelector('.sale-price-3').value = data.sale_price_3;
                        }
                    } catch (error) {
                        console.log('Could not detect sale prices:', error);
                    }
                }

                // Add initial distribution row for each item
                document.querySelectorAll('.distribution-container').forEach(container => {
                    const itemId = container.dataset.itemId;
                    const distributionsList = container.querySelector('.distributions-list');
                    addDistributionRow(distributionsList, itemId);
                });

                // Form validation before submission
                document.querySelector('form').addEventListener('submit', function(e) {
                    e.preventDefault();

                    let isValid = true;
                    let errorMessages = [];

                    // Check each product's distribution
                    document.querySelectorAll('.distribution-container').forEach(container => {
                        const itemId = container.dataset.itemId;
                        const totalQuantity = parseFloat(container.dataset.totalQuantity);
                        const productName = container.closest('.card').querySelector('strong')
                            .textContent;

                        // Calculate total distributed quantity for this item
                        let distributedQuantity = 0;
                        container.querySelectorAll('.distribution-row').forEach(row => {
                            const quantity = parseFloat(row.querySelector('.quantity-input')
                                .value) || 0;
                            distributedQuantity += quantity;
                        });

                        // Update remaining quantity display
                        const remainingSpan = document.querySelector(
                            `[data-item-id="${itemId}"].remaining-quantity`);
                        const remaining = totalQuantity - distributedQuantity;
                        remainingSpan.textContent = remaining.toFixed(2);

                        // Validate quantity
                        if (Math.abs(remaining) > 0.01) {
                            isValid = false;
                            if (remaining > 0) {
                                errorMessages.push(
                                    `المنتج "${productName}": يجب توزيع كامل الكمية. متبقي: ${remaining.toFixed(2)}`
                                );
                            } else {
                                errorMessages.push(
                                    `المنتج "${productName}": الكمية الموزعة تزيد عن المتاح. زيادة: ${Math.abs(remaining).toFixed(2)}`
                                );
                            }
                            remainingSpan.classList.remove('bg-warning');
                            remainingSpan.classList.add('bg-danger');
                        } else {
                            remainingSpan.classList.remove('bg-danger');
                            remainingSpan.classList.add('bg-success');
                        }

                        // Validate that each distribution row has required fields
                        container.querySelectorAll('.distribution-row').forEach(row => {
                            const quantity = parseFloat(row.querySelector('.quantity-input')
                                .value) || 0;
                            const location = row.querySelector('.location-select').value;
                            const salePrice1 = parseFloat(row.querySelector('.sale-price-1')
                                .value) || 0;

                            if (quantity <= 0) {
                                isValid = false;
                                errorMessages.push(
                                    `المنتج "${productName}": يجب إدخال كمية صحيحة`);
                            }
                            if (!location) {
                                isValid = false;
                                errorMessages.push(
                                    `المنتج "${productName}": يجب اختيار موقع التوزيع`);
                            }
                            if (salePrice1 <= 0) {
                                isValid = false;
                                errorMessages.push(
                                    `المنتج "${productName}": يجب إدخال سعر بيع صحيح`);
                            }
                        });
                    });

                    if (isValid) {
                        // If validation passes, submit the form
                        this.submit();
                    } else {
                        // Show error messages
                        alert('يرجى تصحيح الأخطاء التالية:\n\n' + errorMessages.join('\n'));
                    }
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views/admin/purchases/distribute.blade.php ENDPATH**/ ?>
<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-undo text-warning me-2"></i>
                    مرتجعات المبيعات
                </h1>
                <p class="mb-0 text-muted">إدارة ومتابعة مرتجعات المبيعات</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('sale-returns.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إنشاء مرتجع جديد
                </a>
                <a href="{{ user_route('sales.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المبيعات
                </a>
            </div>
        </div>

        <!-- Filters Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter me-2"></i>البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ user_route('sale-returns.index') }}">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="customer_id" class="form-label">العميل</label>
                            <select class="form-select" id="customer_id" name="customer_id">
                                <option value="">جميع العملاء</option>
                                @foreach ($customers as $customer)
                                    <option value="{{ $customer->id }}"
                                        {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                        {{ $customer->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>قيد
                                    الانتظار</option>
                                <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>موافق
                                    عليه</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل
                                </option>
                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغي
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="return_type" class="form-label">نوع المرتجع</label>
                            <select class="form-select" id="return_type" name="return_type">
                                <option value="">جميع الأنواع</option>
                                <option value="full" {{ request('return_type') == 'full' ? 'selected' : '' }}>مرتجع
                                    كامل</option>
                                <option value="partial" {{ request('return_type') == 'partial' ? 'selected' : '' }}>
                                    مرتجع جزئي</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                value="{{ request('date_from') }}">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-1 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="{{ user_route('sale-returns.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Returns Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>قائمة المرتجعات
                </h6>
            </div>
            <div class="card-body">
                @if ($returns->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم المرتجع</th>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>نوع المرتجع</th>
                                    {{-- <th>الحالة</th> --}}
                                    <th>تاريخ المرتجع</th>
                                    <th>المستخدم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($returns as $return)
                                    <tr>
                                        <td>
                                            <a href="{{ user_route('sale-returns.show', $return) }}"
                                                class="text-decoration-none fw-bold text-primary">
                                                {{ $return->return_number }}
                                            </a>
                                        </td>
                                        <td>
                                            <a href="{{ user_route('sales.show', $return->sale) }}"
                                                class="text-decoration-none text-info">
                                                {{ $return->sale->invoice_number }}
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-info text-white me-2">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        {{ $return->customer?->name ?? 'عميل نقدي' }}
                                                    </div>
                                                    @if ($return->customer?->phone)
                                                        <small
                                                            class="text-muted">{{ $return->customer->phone }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-warning">
                                                {{ number_format($return->total_amount, 2) }} ج.م
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ $return->return_type_label }}
                                            </span>
                                        </td>
                                        {{-- <td>
                                            <span class="badge bg-{{ $return->status_color }}">
                                                {{ $return->status_label }}
                                            </span>
                                        </td> --}}
                                        <td>
                                            <div>{{ $return->return_date->format('d/m/Y') }}</div>
                                            <small
                                                class="text-muted">{{ $return->created_at->format('h:i A') }}</small>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $return->user->name }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ user_route('sale-returns.show', $return) }}"
                                                    class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if ($return->canBeEdited())
                                                    <a href="{{ user_route('sale-returns.edit', $return) }}"
                                                        class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                @endif
                                                @if (auth()->user()->hasRole('admin') && $return->canBeCancelled())
                                                    <form method="POST"
                                                        action="{{ user_route('sale-returns.destroy', $return) }}"
                                                        class="d-inline"
                                                        onsubmit="return confirm('هل أنت متأكد من إلغاء هذا المرتجع؟')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                            title="إلغاء">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="3" class="text-end">إجمالي المرتجعات:</th>
                                    <th class="text-warning">
                                        {{ number_format($returns->sum('total_amount'), 2) }} ج.م
                                    </th>
                                    <th colspan="5"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $returns->withQueryString()->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-undo fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مرتجعات</h5>
                        <p class="text-muted">لم يتم العثور على أي مرتجعات تطابق معايير البحث</p>
                        <a href="{{ user_route('sale-returns.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إنشاء مرتجع جديد
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            .avatar-circle {
                width: 35px;
                height: 35px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 12px;
            }
        </style>
    @endpush
</x-app-layout>

<x-app-layout>
    {{-- <x-slot name="header"> --}}
    <div class="container-fluid px-4">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <div class="avatar-circle bg-warning text-white me-3 d-inline-flex">
                    <i class="fas fa-edit"></i>
                </div>
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 leading-tight mb-0">
                        تعديل المورد
                    </h2>
                    <p class="text-muted mb-0 small">تحديث بيانات المورد: {{ $supplier->name }}</p>
                </div>
            </div>
            <a href="{{ user_route('suppliers.index', $supplier) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة إلى الموردين
            </a>
        </div>
    </div>
    {{-- </x-slot> --}}

    <div class="py-4">
        <div class="container-fluid">
            <!-- Progress Steps -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-center">
                        <div class="step-indicator active">
                            <i class="fas fa-user-edit"></i>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <small class="text-muted">خطوة 1 من 1</small>
                        <h6 class="text-primary mb-0">تحديث بيانات المورد</h6>
                    </div>
                </div>
            </div>
            <form action="{{ user_route('suppliers.update', $supplier) }}" method="POST" class="rtl"
                id="supplierEditForm">
                @csrf
                @method('PUT')

                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-6">
                        <div class="card shadow border-0 mb-4">
                            <div class="card-header bg-gradient-primary text-white border-0">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="fas fa-user me-2"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="card-body p-4">
                                <div class="mb-4">
                                    <label for="name" class="form-label fw-bold text-primary">
                                        <i class="fas fa-signature me-2 text-primary"></i>
                                        اسم المورد
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-user text-muted"></i>
                                        </span>
                                        <input type="text"
                                            class="form-control border-start-0 @error('name') is-invalid @enderror"
                                            id="name" name="name" value="{{ old('name', $supplier->name) }}"
                                            placeholder="أدخل اسم المورد" required>
                                        @error('name')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        اسم المورد كما سيظهر في النظام
                                    </small>
                                </div>

                                <div class="mb-4">
                                    <label for="phone" class="form-label fw-bold text-success">
                                        <i class="fas fa-phone me-2 text-success"></i>
                                        رقم الهاتف
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-phone text-muted"></i>
                                        </span>
                                        <input type="text"
                                            class="form-control border-start-0 @error('phone') is-invalid @enderror"
                                            id="phone" name="phone" value="{{ old('phone', $supplier->phone) }}"
                                            placeholder="05xxxxxxxx" required>
                                        @error('phone')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        رقم الهاتف للتواصل مع المورد
                                    </small>
                                </div>

                                <div class="mb-4">
                                    <label for="email" class="form-label fw-bold text-info">
                                        <i class="fas fa-envelope me-2 text-info"></i>
                                        البريد الإلكتروني
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-envelope text-muted"></i>
                                        </span>
                                        <input type="email"
                                            class="form-control border-start-0 @error('email') is-invalid @enderror"
                                            id="email" name="email" value="{{ old('email', $supplier->email) }}"
                                            placeholder="<EMAIL>">
                                        @error('email')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        البريد الإلكتروني للمراسلات (اختياري)
                                    </small>
                                </div>

                                <div class="mb-0">
                                    <label for="status" class="form-label fw-bold text-warning">
                                        <i class="fas fa-toggle-on me-2 text-warning"></i>
                                        حالة المورد
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-flag text-muted"></i>
                                        </span>
                                        <select class="form-select border-start-0 @error('status') is-invalid @enderror"
                                            id="status" name="status">
                                            <option value="active"
                                                {{ old('status', $supplier->status) == 'active' ? 'selected' : '' }}>
                                                نشط
                                            </option>
                                            <option value="inactive"
                                                {{ old('status', $supplier->status) == 'inactive' ? 'selected' : '' }}>
                                                غير نشط
                                            </option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        حالة المورد في النظام
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="col-md-6">
                        <div class="card shadow border-0 mb-4">
                            <div class="card-header bg-gradient-info text-white border-0">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات إضافية
                                </h6>
                            </div>
                            <div class="card-body p-4">
                                <div class="mb-4">
                                    <label for="address" class="form-label fw-bold text-secondary">
                                        <i class="fas fa-map-marker-alt me-2 text-secondary"></i>
                                        العنوان
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-map-marker-alt text-muted"></i>
                                        </span>
                                        <textarea class="form-control border-start-0 @error('address') is-invalid @enderror" id="address" name="address"
                                            rows="3" placeholder="أدخل عنوان المورد">{{ old('address', $supplier->address) }}</textarea>
                                        @error('address')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        العنوان الفعلي للمورد (اختياري)
                                    </small>
                                </div>

                                <div class="mb-4">
                                    <label for="tax_number" class="form-label fw-bold text-dark">
                                        <i class="fas fa-receipt me-2 text-dark"></i>
                                        الرقم الضريبي
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-receipt text-muted"></i>
                                        </span>
                                        <input type="text"
                                            class="form-control border-start-0 @error('tax_number') is-invalid @enderror"
                                            id="tax_number" name="tax_number"
                                            value="{{ old('tax_number', $supplier->tax_number) }}"
                                            placeholder="أدخل الرقم الضريبي">
                                        @error('tax_number')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        الرقم الضريبي للمورد (اختياري)
                                    </small>
                                </div>

                                <div class="mb-0">
                                    <label for="notes" class="form-label fw-bold text-muted">
                                        <i class="fas fa-sticky-note me-2 text-muted"></i>
                                        ملاحظات
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-sticky-note text-muted"></i>
                                        </span>
                                        <textarea class="form-control border-start-0 @error('notes') is-invalid @enderror" id="notes" name="notes"
                                            rows="3" placeholder="أضف أي ملاحظات حول المورد">{{ old('notes', $supplier->notes) }}</textarea>
                                        @error('notes')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        ملاحظات إضافية حول المورد (اختياري)
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow border-0">
                            <div class="card-body p-4">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-muted mb-1">
                                            <i class="fas fa-check-circle me-2"></i>
                                            جاهز للحفظ؟
                                        </h6>
                                        <small class="text-muted">تأكد من صحة جميع البيانات قبل الحفظ</small>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <a href="{{ user_route('suppliers.show', $supplier) }}"
                                            class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>
                                            إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-primary" id="saveBtn">
                                            <span class="btn-text">
                                                <i class="fas fa-save me-2"></i>
                                                حفظ التغييرات
                                            </span>
                                            <span class="btn-loading d-none">
                                                <i class="fas fa-spinner fa-spin me-2"></i>
                                                جاري الحفظ...
                                            </span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @push('styles')
        <style>
            .rtl {
                direction: rtl;
                text-align: right;
            }

            .rtl .form-select {
                text-align: right;
            }

            .rtl .form-control {
                text-align: right;
            }

            .rtl .invalid-feedback {
                text-align: right;
            }

            .rtl .btn i {
                margin-left: 0.5rem;
                margin-right: 0;
            }

            .rtl .text-start {
                text-align: right !important;
            }

            .rtl .text-end {
                text-align: left !important;
            }

            .rtl .card-title {
                text-align: right;
            }

            /* Enhanced Styles for Edit Form */
            .avatar-circle {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
                font-weight: bold;
            }

            .step-indicator {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.5rem;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                position: relative;
            }

            .step-indicator.active {
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% {
                    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
                }

                70% {
                    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
                }

                100% {
                    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
                }
            }

            .bg-gradient-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            .bg-gradient-info {
                background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            }



            .form-label.fw-bold {
                font-size: 0.95rem;
                margin-bottom: 0.75rem;
            }

            .card.shadow {
                box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
            }

            .border-start-0 {
                border-left: 0 !important;
            }

            .border-end-0 {
                border-right: 0 !important;
            }

            .btn-loading {
                opacity: 0.8;
            }

            .gap-2 {
                gap: 0.5rem;
            }

            /* RTL specific adjustments */
            .rtl .border-start-0 {
                border-right: 0 !important;
                border-left: 1px solid #ced4da !important;
            }

            .rtl .border-end-0 {
                border-left: 0 !important;
                border-right: 1px solid #ced4da !important;
            }

            .rtl .me-2 {
                margin-left: 0.5rem !important;
                margin-right: 0 !important;
            }

            .rtl .me-1 {
                margin-left: 0.25rem !important;
                margin-right: 0 !important;
            }

            /* Auto-resize textareas */
            textarea.auto-resize {
                resize: vertical;
                min-height: 100px;
            }

            /* Form validation enhancements */
            .was-validated .form-control:valid,
            .form-control.is-valid {
                border-color: #28a745;
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
            }

            .was-validated .form-control:invalid,
            .form-control.is-invalid {
                border-color: #dc3545;
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const form = document.getElementById('supplierEditForm');
                const saveBtn = document.getElementById('saveBtn');
                const btnText = saveBtn.querySelector('.btn-text');
                const btnLoading = saveBtn.querySelector('.btn-loading');

                // Form submission with loading state
                form.addEventListener('submit', function() {
                    saveBtn.disabled = true;
                    btnText.classList.add('d-none');
                    btnLoading.classList.remove('d-none');
                });

                // Phone number formatting
                const phoneInput = document.getElementById('phone');
                phoneInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 10) {
                        value = value.substring(0, 10);
                    }
                    e.target.value = value;
                });

                // Auto-resize textareas
                const textareas = document.querySelectorAll('textarea');
                textareas.forEach(textarea => {
                    textarea.classList.add('auto-resize');
                    textarea.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = (this.scrollHeight) + 'px';
                    });
                });

                // Real-time validation feedback
                const inputs = form.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    input.addEventListener('blur', function() {
                        if (this.checkValidity()) {
                            this.classList.remove('is-invalid');
                            this.classList.add('is-valid');
                        } else {
                            this.classList.remove('is-valid');
                            this.classList.add('is-invalid');
                        }
                    });

                    input.addEventListener('input', function() {
                        if (this.classList.contains('is-invalid') && this.checkValidity()) {
                            this.classList.remove('is-invalid');
                            this.classList.add('is-valid');
                        }
                    });
                });
            });
        </script>
    @endpush
</x-app-layout>

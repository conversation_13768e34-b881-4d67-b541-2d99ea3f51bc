<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-plus-circle text-primary me-2"></i>
                    إنشاء طلب نقل مخزون
                </h1>
                <p class="text-muted mb-0">إنشاء طلب جديد لنقل المنتجات بين المخازن والفروع</p>
            </div>
            <div>
                <a href="{{ user_route('inventory-transfers.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>

        <form method="POST" action="{{ user_route('inventory-transfers.store') }}" id="transferForm">
            @csrf
            
            <!-- Transfer Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">تفاصيل النقل</h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- Transfer Type -->
                        <div class="col-md-6">
                            <label for="type" class="form-label">نوع النقل <span class="text-danger">*</span></label>
                            <select name="type" id="type" class="form-select @error('type') is-invalid @enderror" required>
                                <option value="">اختر نوع النقل</option>
                                <option value="store_to_branch" {{ old('type') === 'store_to_branch' ? 'selected' : '' }}>من مخزن إلى فرع</option>
                                <option value="branch_to_store" {{ old('type') === 'branch_to_store' ? 'selected' : '' }}>من فرع إلى مخزن</option>
                                <option value="store_to_store" {{ old('type') === 'store_to_store' ? 'selected' : '' }}>من مخزن إلى مخزن</option>
                                <option value="branch_to_branch" {{ old('type') === 'branch_to_branch' ? 'selected' : '' }}>من فرع إلى فرع</option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Source Location -->
                        <div class="col-md-6">
                            <label for="source" class="form-label">المصدر <span class="text-danger">*</span></label>
                            <select name="source_id" id="source" class="form-select @error('source_id') is-invalid @enderror" required>
                                <option value="">اختر المصدر</option>
                            </select>
                            <input type="hidden" name="source_type" id="source_type">
                            @error('source_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Destination Location -->
                        <div class="col-md-6">
                            <label for="destination" class="form-label">الوجهة <span class="text-danger">*</span></label>
                            <select name="destination_id" id="destination" class="form-select @error('destination_id') is-invalid @enderror" required>
                                <option value="">اختر الوجهة</option>
                            </select>
                            <input type="hidden" name="destination_type" id="destination_type">
                            @error('destination_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="col-md-6">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea name="notes" id="notes" class="form-control @error('notes') is-invalid @enderror" 
                                      rows="3" placeholder="ملاحظات إضافية...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">المنتجات المطلوب نقلها</h6>
                    <button type="button" class="btn btn-sm btn-success" onclick="addProductRow()">
                        <i class="fas fa-plus me-2"></i>إضافة منتج
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="productsTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="40%">المنتج</th>
                                    <th width="20%">الكمية المطلوبة</th>
                                    <th width="30%">ملاحظات</th>
                                    <th width="10%" class="text-center">إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- Product rows will be added here -->
                            </tbody>
                        </table>
                    </div>
                    
                    @error('items')
                        <div class="text-danger mt-2">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="d-flex justify-content-end gap-2 mb-4">
                <a href="{{ user_route('inventory-transfers.index') }}" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>إنشاء طلب النقل
                </button>
            </div>
        </form>
    </div>

    <script>
        // Data from server
        const stores = @json($stores);
        const branches = @json($branches);
        const products = @json($products);
        
        let productRowIndex = 0;

        // Handle transfer type change
        document.getElementById('type').addEventListener('change', function() {
            const type = this.value;
            updateLocationOptions(type);
        });

        function updateLocationOptions(type) {
            const sourceSelect = document.getElementById('source');
            const destinationSelect = document.getElementById('destination');
            const sourceTypeInput = document.getElementById('source_type');
            const destinationTypeInput = document.getElementById('destination_type');
            
            // Clear existing options
            sourceSelect.innerHTML = '<option value="">اختر المصدر</option>';
            destinationSelect.innerHTML = '<option value="">اختر الوجهة</option>';
            
            if (!type) return;
            
            const [sourceType, destinationType] = type.split('_to_');
            sourceTypeInput.value = sourceType;
            destinationTypeInput.value = destinationType;
            
            // Populate source options
            if (sourceType === 'store') {
                stores.forEach(store => {
                    const option = document.createElement('option');
                    option.value = store.id;
                    option.textContent = store.name + (store.branch ? ` (${store.branch.name})` : ' (مستقل)');
                    sourceSelect.appendChild(option);
                });
            } else if (sourceType === 'branch') {
                branches.forEach(branch => {
                    const option = document.createElement('option');
                    option.value = branch.id;
                    option.textContent = branch.name;
                    sourceSelect.appendChild(option);
                });
            }
            
            // Populate destination options
            if (destinationType === 'store') {
                stores.forEach(store => {
                    const option = document.createElement('option');
                    option.value = store.id;
                    option.textContent = store.name + (store.branch ? ` (${store.branch.name})` : ' (مستقل)');
                    destinationSelect.appendChild(option);
                });
            } else if (destinationType === 'branch') {
                branches.forEach(branch => {
                    const option = document.createElement('option');
                    option.value = branch.id;
                    option.textContent = branch.name;
                    destinationSelect.appendChild(option);
                });
            }
        }

        function addProductRow() {
            const tbody = document.getElementById('productsTableBody');
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <select name="items[${productRowIndex}][product_id]" class="form-select" required>
                        <option value="">اختر المنتج</option>
                        ${products.map(product => `<option value="${product.id}">${product.name} - ${product.sku || 'بدون رمز'}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <input type="number" name="items[${productRowIndex}][requested_quantity]" 
                           class="form-control" min="0.01" step="0.01" required>
                </td>
                <td>
                    <input type="text" name="items[${productRowIndex}][notes]" 
                           class="form-control" placeholder="ملاحظات...">
                </td>
                <td class="text-center">
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeProductRow(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
            productRowIndex++;
        }

        function removeProductRow(button) {
            button.closest('tr').remove();
        }

        // Add initial product row
        document.addEventListener('DOMContentLoaded', function() {
            addProductRow();
        });

        // Form validation
        document.getElementById('transferForm').addEventListener('submit', function(e) {
            const productsTable = document.getElementById('productsTableBody');
            if (productsTable.children.length === 0) {
                e.preventDefault();
                alert('يجب إضافة منتج واحد على الأقل');
                return;
            }
            
            const sourceId = document.getElementById('source').value;
            const destinationId = document.getElementById('destination').value;
            const sourceType = document.getElementById('source_type').value;
            const destinationType = document.getElementById('destination_type').value;
            
            if (sourceType === destinationType && sourceId === destinationId) {
                e.preventDefault();
                alert('لا يمكن أن يكون المصدر والوجهة نفس المكان');
                return;
            }
        });
    </script>
</x-app-layout>

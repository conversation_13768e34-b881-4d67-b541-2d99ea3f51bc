<?php

use App\Http\Controllers\ProductImportController;
use Illuminate\Support\Facades\Route;

// Product Import/Export Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('products/import', [ProductImportController::class, 'showImport'])->name('products.import');
    Route::post('products/import', [ProductImportController::class, 'import'])->name('products.import.store');
    Route::get('products/import/template', [ProductImportController::class, 'downloadTemplate'])->name('products.import.template');
});

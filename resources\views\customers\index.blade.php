<x-app-layout>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">العملاء</h5>
                        <a href="{{ user_route('customers.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة عميل
                        </a>
                    </div>
                    <div class="card-body">
                        <form action="{{ user_route('customers.index') }}" method="GET" class="mb-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control" placeholder="بحث..."
                                            value="{{ request('search') }}">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>

                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهاتف</th>
                                    <th>عدد المشتريات</th>
                                    <th>إجمالي المشتريات</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>الإجراءات</th>

                                </thead>
                                <tbody>
                                    @forelse($customers as $customer)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle bg-primary text-white me-2 d-flex align-items-center justify-content-center"
                                                        style="width: 40px; height: 40px; border-radius: 50%; font-weight: bold;">
                                                        {{ strtoupper(substr($customer->name, 0, 1)) }}
                                                    </div>
                                                    <div>
                                                        <strong>{{ $customer->name }}</strong>
                                                        @if ($customer->address)
                                                            <br><small
                                                                class="text-muted">{{ Str::limit($customer->address, 30) }}</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if ($customer->email)
                                                    <a href="mailto:{{ $customer->email }}"
                                                        class="text-decoration-none">
                                                        {{ $customer->email }}
                                                    </a>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if ($customer->phone)
                                                    <a href="tel:{{ $customer->phone }}" class="text-decoration-none">
                                                        <i class="fas fa-phone text-success me-1"></i>
                                                        {{ $customer->phone }}
                                                    </a>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-info fs-6">{{ $customer->sales_count }}</span>
                                            </td>
                                            <td>
                                                <strong
                                                    class="text-primary">{{ number_format($customer->total_sales_amount ?? 0, 2) }}
                                                    ج.م</strong>
                                            </td>
                                            <td>
                                                @if (($customer->total_remaining_amount ?? 0) > 0)
                                                    <span
                                                        class="badge bg-warning fs-6">{{ number_format($customer->total_remaining_amount, 2) }}
                                                        ج.م</span>
                                                @else
                                                    <span class="badge bg-success fs-6">مسدد</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ user_route('customers.show', $customer) }}"
                                                        class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ user_route('customers.edit', $customer) }}"
                                                        class="btn btn-sm btn-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if (($customer->total_remaining_amount ?? 0) > 0)
                                                        <a href="{{ user_route('customer-payments.create', ['customer_id' => $customer->id]) }}"
                                                            class="btn btn-sm btn-warning" title="تسجيل دفعة">
                                                            <i class="fas fa-money-bill-wave"></i>
                                                        </a>
                                                    @endif
                                                    @if (auth()->user()->isAdmin())
                                                        <form action="{{ user_route('customers.destroy', $customer) }}"
                                                            method="POST" class="d-inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-danger"
                                                                onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')"
                                                                title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="fas fa-users fa-3x mb-3"></i>
                                                    <p>لا يوجد عملاء</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4">
                            {{ $customers->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

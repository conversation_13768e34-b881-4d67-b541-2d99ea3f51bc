<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-tags text-primary"></i> إدارة الأسعار
            </h2>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.price-management.analytics') }}" class="btn btn-info btn-sm">
                    <i class="fas fa-chart-line"></i> التحليلات
                </a>
                <a href="{{ route('admin.price-management.export', request()->query()) }}" class="btn btn-success btn-sm">
                    <i class="fas fa-download"></i> تصدير
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter"></i> البحث والتصفية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.price-management.index') }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">الفرع</label>
                                <select name="branch_id" class="form-select">
                                    <option value="">جميع الفروع</option>
                                    @foreach($branches as $branch)
                                        <option value="{{ $branch->id }}" 
                                            {{ $branchId == $branch->id ? 'selected' : '' }}>
                                            {{ $branch->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الفئة</label>
                                <select name="category_id" class="form-select">
                                    <option value="">جميع الفئات</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" 
                                            {{ $categoryId == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">البحث</label>
                                <input type="text" name="search" class="form-control" 
                                    placeholder="اسم المنتج أو الرمز" value="{{ $search }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Update Button -->
            @if($branchId)
                <div class="mb-3">
                    <a href="{{ route('admin.price-management.bulk-update', ['branch_id' => $branchId, 'category_id' => $categoryId]) }}" 
                        class="btn btn-warning">
                        <i class="fas fa-edit"></i> تحديث أسعار مجمع
                    </a>
                </div>
            @endif

            <!-- Products Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i> المنتجات والأسعار
                    </h5>
                </div>
                <div class="card-body">
                    @if($products->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الفئة</th>
                                        <th>الرمز</th>
                                        @if($branchId)
                                            <th>السعر الحالي</th>
                                            <th>مصدر السعر</th>
                                            <th>هامش الربح</th>
                                        @else
                                            <th>السعر الأساسي</th>
                                            <th>سعر البيع</th>
                                        @endif
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($products as $product)
                                        <tr>
                                            <td>
                                                <strong>{{ $product->name }}</strong>
                                                @if($product->description)
                                                    <br><small class="text-muted">{{ Str::limit($product->description, 50) }}</small>
                                                @endif
                                            </td>
                                            <td>{{ $product->category->name }}</td>
                                            <td><code>{{ $product->sku }}</code></td>
                                            
                                            @if($branchId && isset($product->resolved_price_info))
                                                <td>
                                                    <span class="fw-bold">{{ number_format($product->resolved_price_info['price'], 2) }} ج.م</span>
                                                </td>
                                                <td>
                                                    @php
                                                        $sourceLabels = [
                                                            'branch_inventory' => 'سعر الفرع',
                                                            'product_selling_price' => 'سعر المنتج',
                                                            'product_price' => 'السعر الأساسي',
                                                            'cost_plus_calculated' => 'محسوب تلقائياً'
                                                        ];
                                                    @endphp
                                                    <span class="badge bg-info">
                                                        {{ $sourceLabels[$product->resolved_price_info['source']] ?? $product->resolved_price_info['source'] }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @if($product->resolved_price_info['profit_margin'] !== null)
                                                        <span class="badge {{ $product->resolved_price_info['profit_margin'] > 0.2 ? 'bg-success' : ($product->resolved_price_info['profit_margin'] > 0.05 ? 'bg-warning' : 'bg-danger') }}">
                                                            {{ number_format($product->resolved_price_info['profit_margin'] * 100, 1) }}%
                                                        </span>
                                                    @else
                                                        <span class="text-muted">غير محدد</span>
                                                    @endif
                                                </td>
                                            @else
                                                <td>{{ number_format($product->price ?? 0, 2) }} ج.م</td>
                                                <td>{{ number_format($product->selling_price ?? 0, 2) }} ج.م</td>
                                            @endif
                                            
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('admin.price-management.history', ['product' => $product->id, 'branch_id' => $branchId]) }}" 
                                                        class="btn btn-outline-info" title="تاريخ الأسعار">
                                                        <i class="fas fa-history"></i>
                                                    </a>
                                                    @if($branchId)
                                                        <button type="button" class="btn btn-outline-primary" 
                                                            onclick="showPriceModal({{ $product->id }}, '{{ $product->name }}', {{ $branchId }})"
                                                            title="تعديل السعر">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $products->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد منتجات مطابقة للبحث</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Price Edit Modal -->
    <div class="modal fade" id="priceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل سعر المنتج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="priceForm">
                        @csrf
                        <input type="hidden" id="productId" name="product_id">
                        <input type="hidden" id="branchIdInput" name="branch_id">
                        
                        <div class="mb-3">
                            <label class="form-label">اسم المنتج</label>
                            <input type="text" id="productName" class="form-control" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">مستوى السعر</label>
                            <select id="priceLevel" name="price_level" class="form-select">
                                <option value="1">السعر الأول</option>
                                <option value="2">السعر الثاني</option>
                                <option value="3">السعر الثالث</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">السعر الجديد</label>
                            <div class="input-group">
                                <input type="number" step="0.01" id="newPrice" name="price" class="form-control" required>
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                        
                        <div id="priceSuggestions" class="mb-3" style="display: none;">
                            <label class="form-label">اقتراحات الأسعار</label>
                            <div id="suggestionsContainer"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updatePrice()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            async function showPriceModal(productId, productName, branchId) {
                document.getElementById('productId').value = productId;
                document.getElementById('productName').value = productName;
                document.getElementById('branchIdInput').value = branchId;
                
                // Load price suggestions
                try {
                    const response = await fetch(`{{ route('admin.price-management.suggestions') }}?product_id=${productId}&branch_id=${branchId}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        showPriceSuggestions(data.suggestions);
                    }
                } catch (error) {
                    console.error('Error loading suggestions:', error);
                }
                
                new bootstrap.Modal(document.getElementById('priceModal')).show();
            }
            
            function showPriceSuggestions(suggestions) {
                const container = document.getElementById('suggestionsContainer');
                const suggestionsDiv = document.getElementById('priceSuggestions');
                
                container.innerHTML = '';
                
                Object.entries(suggestions).forEach(([key, suggestion]) => {
                    const button = document.createElement('button');
                    button.type = 'button';
                    button.className = 'btn btn-outline-secondary btn-sm me-2 mb-2';
                    button.textContent = `${suggestion.label}: ${suggestion.price.toFixed(2)} ج.م`;
                    button.onclick = () => {
                        document.getElementById('newPrice').value = suggestion.price.toFixed(2);
                    };
                    container.appendChild(button);
                });
                
                suggestionsDiv.style.display = 'block';
            }
            
            async function updatePrice() {
                const form = document.getElementById('priceForm');
                const formData = new FormData(form);
                
                try {
                    const response = await fetch('{{ route("admin.price-management.bulk-update") }}', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            branch_id: formData.get('branch_id'),
                            updates: [{
                                product_id: formData.get('product_id'),
                                price: parseFloat(formData.get('price')),
                                price_level: parseInt(formData.get('price_level'))
                            }]
                        }),
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });
                    
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء تحديث السعر');
                    }
                } catch (error) {
                    console.error('Error updating price:', error);
                    alert('حدث خطأ أثناء تحديث السعر');
                }
            }
        </script>
    @endpush
</x-app-layout>

<?php

namespace App\Helpers;

class AlertHelper
{
    /**
     * Show a success toast notification
     */
    public static function success($message)
    {
        session()->flash('toastr', [
            'type' => 'success',
            'message' => $message
        ]);
    }

    /**
     * Show an error toast notification
     */
    public static function error($message)
    {
        session()->flash('toastr', [
            'type' => 'error',
            'message' => $message
        ]);
    }

    /**
     * Show a warning toast notification
     */
    public static function warning($message)
    {
        session()->flash('toastr', [
            'type' => 'warning',
            'message' => $message
        ]);
    }

    /**
     * Show an info toast notification
     */
    public static function info($message)
    {
        session()->flash('toastr', [
            'type' => 'info',
            'message' => $message
        ]);
    }

    /**
     * Show a confirmation dialog using SweetAlert2
     */
    public static function confirm($title, $text, $confirmButtonText = 'نعم', $cancelButtonText = 'إلغاء')
    {
        return "
            Swal.fire({
                title: '$title',
                text: '$text',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: '$confirmButtonText',
                cancelButtonText: '$cancelButtonText',
                reverseButtons: true
            })
        ";
    }
}

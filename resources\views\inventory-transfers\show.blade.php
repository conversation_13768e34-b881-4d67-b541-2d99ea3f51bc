<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-exchange-alt text-primary me-2"></i>
                    تفاصيل طلب النقل {{ $inventoryTransfer->transfer_number }}
                </h1>
                <p class="text-muted mb-0">عرض تفاصيل طلب نقل المخزون والمنتجات المرتبطة</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('inventory-transfers.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
                
                @if($inventoryTransfer->canBeApproved() && auth()->user()->isAdmin())
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#approveModal">
                        <i class="fas fa-check me-2"></i>موافقة
                    </button>
                @endif
                
                @if($inventoryTransfer->canBeShipped() && auth()->user()->isAdmin())
                    <form method="POST" action="{{ user_route('inventory-transfers.ship', $inventoryTransfer) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-info" onclick="return confirm('هل أنت متأكد من شحن هذا الطلب؟')">
                            <i class="fas fa-shipping-fast me-2"></i>شحن
                        </button>
                    </form>
                @endif
                
                @if($inventoryTransfer->canBeReceived())
                    <form method="POST" action="{{ user_route('inventory-transfers.receive', $inventoryTransfer) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-primary" onclick="return confirm('هل أنت متأكد من استلام هذا الطلب؟')">
                            <i class="fas fa-check-circle me-2"></i>استلام
                        </button>
                    </form>
                @endif
                
                @if($inventoryTransfer->canBeCancelled())
                    <form method="POST" action="{{ user_route('inventory-transfers.cancel', $inventoryTransfer) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من إلغاء هذا الطلب؟')">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                    </form>
                @endif
            </div>
        </div>

        <div class="row">
            <!-- Transfer Information -->
            <div class="col-lg-8">
                <!-- Basic Info -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">معلومات النقل</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">رقم النقل:</td>
                                        <td>{{ $inventoryTransfer->transfer_number }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">نوع النقل:</td>
                                        <td>
                                            @php
                                                $typeLabels = [
                                                    'store_to_branch' => 'من مخزن إلى فرع',
                                                    'branch_to_store' => 'من فرع إلى مخزن',
                                                    'store_to_store' => 'من مخزن إلى مخزن',
                                                    'branch_to_branch' => 'من فرع إلى فرع'
                                                ];
                                            @endphp
                                            <span class="badge bg-info">{{ $typeLabels[$inventoryTransfer->type] ?? $inventoryTransfer->type }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">الحالة:</td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'pending' => 'warning',
                                                    'approved' => 'info',
                                                    'in_transit' => 'secondary',
                                                    'completed' => 'success',
                                                    'cancelled' => 'danger'
                                                ];
                                                $statusLabels = [
                                                    'pending' => 'في الانتظار',
                                                    'approved' => 'موافق عليه',
                                                    'in_transit' => 'في الطريق',
                                                    'completed' => 'مكتمل',
                                                    'cancelled' => 'ملغي'
                                                ];
                                            @endphp
                                            <span class="badge bg-{{ $statusColors[$inventoryTransfer->status] ?? 'secondary' }}">
                                                {{ $statusLabels[$inventoryTransfer->status] ?? $inventoryTransfer->status }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">طلب بواسطة:</td>
                                        <td>{{ $inventoryTransfer->requestedBy->name ?? 'غير محدد' }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">تاريخ الطلب:</td>
                                        <td>{{ $inventoryTransfer->requested_at ? $inventoryTransfer->requested_at->format('Y-m-d H:i') : $inventoryTransfer->created_at->format('Y-m-d H:i') }}</td>
                                    </tr>
                                    @if($inventoryTransfer->approved_at)
                                        <tr>
                                            <td class="fw-bold">تاريخ الموافقة:</td>
                                            <td>{{ $inventoryTransfer->approved_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">موافق بواسطة:</td>
                                            <td>{{ $inventoryTransfer->approvedBy->name ?? 'غير محدد' }}</td>
                                        </tr>
                                    @endif
                                    @if($inventoryTransfer->received_at)
                                        <tr>
                                            <td class="fw-bold">تاريخ الاستلام:</td>
                                            <td>{{ $inventoryTransfer->received_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">مستلم بواسطة:</td>
                                            <td>{{ $inventoryTransfer->receivedBy->name ?? 'غير محدد' }}</td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                        </div>
                        
                        @if($inventoryTransfer->notes)
                            <div class="mt-3">
                                <h6 class="fw-bold">ملاحظات:</h6>
                                <p class="text-muted">{{ $inventoryTransfer->notes }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Transfer Items -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">المنتجات</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية المطلوبة</th>
                                        @if($inventoryTransfer->status !== 'pending')
                                            <th>الكمية الموافق عليها</th>
                                        @endif
                                        @if($inventoryTransfer->status === 'completed')
                                            <th>الكمية المستلمة</th>
                                        @endif
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($inventoryTransfer->items as $item)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle bg-primary text-white me-2">
                                                        {{ substr($item->product->name, 0, 1) }}
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">{{ $item->product->name }}</div>
                                                        <small class="text-muted">{{ $item->product->sku ?? 'بدون رمز' }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ number_format($item->requested_quantity, 2) }}</td>
                                            @if($inventoryTransfer->status !== 'pending')
                                                <td>{{ number_format($item->approved_quantity ?? 0, 2) }}</td>
                                            @endif
                                            @if($inventoryTransfer->status === 'completed')
                                                <td>{{ number_format($item->received_quantity ?? 0, 2) }}</td>
                                            @endif
                                            <td>{{ $item->notes ?? '-' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Information -->
            <div class="col-lg-4">
                <!-- Source Location -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-secondary">المصدر</h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="avatar-circle bg-secondary text-white mx-auto mb-3" style="width: 60px; height: 60px; font-size: 24px;">
                            {{ substr($inventoryTransfer->source->name ?? 'غير محدد', 0, 1) }}
                        </div>
                        <h5 class="fw-bold">{{ $inventoryTransfer->source->name ?? 'غير محدد' }}</h5>
                        <p class="text-muted mb-0">
                            {{ $inventoryTransfer->source_type === 'store' ? 'مخزن' : 'فرع' }}
                            @if($inventoryTransfer->source_type === 'store' && $inventoryTransfer->source)
                                @if($inventoryTransfer->source->isIndependent())
                                    (مستقل)
                                @else
                                    ({{ $inventoryTransfer->source->branch->name ?? 'فرع غير محدد' }})
                                @endif
                            @endif
                        </p>
                    </div>
                </div>

                <!-- Destination Location -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">الوجهة</h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="avatar-circle bg-success text-white mx-auto mb-3" style="width: 60px; height: 60px; font-size: 24px;">
                            {{ substr($inventoryTransfer->destination->name ?? 'غير محدد', 0, 1) }}
                        </div>
                        <h5 class="fw-bold">{{ $inventoryTransfer->destination->name ?? 'غير محدد' }}</h5>
                        <p class="text-muted mb-0">
                            {{ $inventoryTransfer->destination_type === 'store' ? 'مخزن' : 'فرع' }}
                            @if($inventoryTransfer->destination_type === 'store' && $inventoryTransfer->destination)
                                @if($inventoryTransfer->destination->isIndependent())
                                    (مستقل)
                                @else
                                    ({{ $inventoryTransfer->destination->branch->name ?? 'فرع غير محدد' }})
                                @endif
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
    </style>
</x-app-layout>

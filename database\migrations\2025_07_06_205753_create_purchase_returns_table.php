<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_returns', function (Blueprint $table) {
            $table->id();
            $table->string('return_number')->unique();
            $table->foreignId('purchase_id')->constrained()->onDelete('cascade');
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('store_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // User who processed the return
            $table->decimal('total_amount', 10, 2);
            $table->decimal('refund_amount', 10, 2)->default(0); // Amount refunded to supplier account
            $table->enum('status', ['pending', 'approved', 'completed', 'cancelled'])->default('pending');
            $table->enum('return_type', ['full', 'partial'])->default('partial');
            $table->text('reason')->nullable(); // Reason for return (damaged, wrong item, etc.)
            $table->text('notes')->nullable();
            $table->date('return_date');
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['supplier_id', 'return_date']);
            $table->index(['purchase_id', 'status']);
            $table->index(['status', 'return_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_returns');
    }
};

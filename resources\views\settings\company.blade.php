<x-app-layout>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">معلومات الشركة</h4>
                    </div>

                    <div class="card-body">
                        <form method="POST" action="{{ route('settings.company.update') }}" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')

                            <div class="mb-3">
                                <label for="company_name" class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control @error('company_name') is-invalid @enderror"
                                    id="company_name" name="company_name"
                                    value="{{ old('company_name', $settings->company_name ?? '') }}" required>
                                @error('company_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="company_address" class="form-label">عنوان الشركة</label>
                                <textarea class="form-control @error('company_address') is-invalid @enderror" id="company_address"
                                    name="company_address" rows="3">{{ old('company_address', $settings->company_address ?? '') }}</textarea>
                                @error('company_address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="company_phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control @error('company_phone') is-invalid @enderror"
                                    id="company_phone" name="company_phone"
                                    value="{{ old('company_phone', $settings->company_phone ?? '') }}">
                                @error('company_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="company_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control @error('company_email') is-invalid @enderror"
                                    id="company_email" name="company_email"
                                    value="{{ old('company_email', $settings->company_email ?? '') }}">
                                @error('company_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="company_tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text"
                                    class="form-control @error('company_tax_number') is-invalid @enderror"
                                    id="company_tax_number" name="company_tax_number"
                                    value="{{ old('company_tax_number', $settings->company_tax_number ?? '') }}">
                                @error('company_tax_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="company_logo" class="form-label">شعار الشركة</label>
                                @if (isset($settings->company_logo))
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings->company_logo) }}" alt="Company Logo"
                                            class="img-thumbnail" style="max-height: 100px;">
                                    </div>
                                @endif
                                <input type="file" class="form-control @error('company_logo') is-invalid @enderror"
                                    id="company_logo" name="company_logo" accept="image/*">
                                @error('company_logo')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="company_currency" class="form-label">العملة</label>
                                <select class="form-select @error('company_currency') is-invalid @enderror"
                                    id="company_currency" name="company_currency" required>
                                    <option value="EGP"
                                        {{ old('company_currency', $settings->company_currency ?? '') == 'EGP' ? 'selected' : '' }}>
                                        جنيه مصري</option>
                                    <option value="SAR"
                                        {{ old('company_currency', $settings->company_currency ?? '') == 'SAR' ? 'selected' : '' }}>
                                        ريال سعودي</option>
                                    <option value="USD"
                                        {{ old('company_currency', $settings->company_currency ?? '') == 'USD' ? 'selected' : '' }}>
                                        دولار أمريكي</option>
                                    <option value="EUR"
                                        {{ old('company_currency', $settings->company_currency ?? '') == 'EUR' ? 'selected' : '' }}>
                                        يورو</option>
                                </select>
                                @error('company_currency')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

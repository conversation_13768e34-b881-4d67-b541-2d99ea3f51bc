<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sale_return_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sale_return_id')->constrained()->onDelete('cascade');
            $table->foreignId('sale_item_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->decimal('quantity_returned', 10, 2);
            $table->decimal('original_quantity', 10, 2);
            $table->decimal('sale_price', 10, 2);
            $table->decimal('total_amount', 10, 2);
            $table->enum('condition', ['good', 'damaged', 'expired', 'defective'])->default('good');
            $table->text('item_notes')->nullable(); // Notes specific to this item
            $table->boolean('inventory_adjusted')->default(false); // Track if inventory was adjusted
            $table->timestamps();

            $table->index(['sale_return_id', 'product_id']);
            $table->index(['sale_item_id', 'condition']);
            $table->index(['product_id', 'condition']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sale_return_items');
    }
};

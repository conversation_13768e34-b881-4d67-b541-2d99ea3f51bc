<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\Branch;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MultiBranchSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $adminRole;
    protected $sellerRole;
    protected $branch1;
    protected $branch2;
    protected $store1;
    protected $store2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $this->adminRole = Role::create([
            'name' => 'admin',
            'description' => 'Administrator',
            'permissions' => ['users.view', 'branches.view', 'sales.view'],
            'is_active' => true,
        ]);

        $this->sellerRole = Role::create([
            'name' => 'seller',
            'description' => 'Seller',
            'permissions' => ['sales.view', 'products.view'],
            'is_active' => true,
        ]);

        // Create branches
        $this->branch1 = Branch::create([
            'code' => 'BR001',
            'name' => 'Main Branch',
            'address' => '123 Main St',
            'phone' => '+1234567890',
            'email' => '<EMAIL>',
            'opening_balance' => 10000,
            'is_active' => true,
        ]);

        $this->branch2 = Branch::create([
            'code' => 'BR002',
            'name' => 'Second Branch',
            'address' => '456 Second St',
            'phone' => '+1234567891',
            'email' => '<EMAIL>',
            'opening_balance' => 5000,
            'is_active' => true,
        ]);

        // Create stores
        $this->store1 = Store::create([
            'code' => 'ST001',
            'name' => 'Main Store',
            'branch_id' => $this->branch1->id,
            'address' => '123 Main St',
            'phone' => '+1234567892',
            'email' => '<EMAIL>',
            'opening_balance' => 5000,
            'is_active' => true,
        ]);

        $this->store2 = Store::create([
            'code' => 'ST002',
            'name' => 'Second Store',
            'branch_id' => $this->branch2->id,
            'address' => '456 Second St',
            'phone' => '+1234567893',
            'email' => '<EMAIL>',
            'opening_balance' => 3000,
            'is_active' => true,
        ]);
    }

    public function test_admin_can_access_all_branches(): void
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $this->adminRole->id,
            'branch_id' => null, // Admin not restricted to any branch
            'store_id' => null,
            'is_active' => true,
        ]);

        $this->assertTrue($admin->isAdmin());
        $this->assertTrue($admin->canAccessBranch($this->branch1->id));
        $this->assertTrue($admin->canAccessBranch($this->branch2->id));
    }

    public function test_seller_can_only_access_assigned_branch(): void
    {
        $seller = User::create([
            'name' => 'Seller User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $this->sellerRole->id,
            'branch_id' => $this->branch1->id,
            'store_id' => $this->store1->id,
            'is_active' => true,
        ]);

        $this->assertTrue($seller->isSeller());
        $this->assertTrue($seller->canAccessBranch($this->branch1->id));
        $this->assertFalse($seller->canAccessBranch($this->branch2->id));
    }

    public function test_admin_dashboard_access(): void
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $this->adminRole->id,
            'branch_id' => null,
            'store_id' => null,
            'is_active' => true,
        ]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');
        $response->assertStatus(200);
    }

    public function test_seller_dashboard_access(): void
    {
        $seller = User::create([
            'name' => 'Seller User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $this->sellerRole->id,
            'branch_id' => $this->branch1->id,
            'store_id' => $this->store1->id,
            'is_active' => true,
        ]);

        $response = $this->actingAs($seller)->get('/seller/dashboard');
        $response->assertStatus(200);
    }

    public function test_seller_cannot_access_admin_dashboard(): void
    {
        $seller = User::create([
            'name' => 'Seller User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $this->sellerRole->id,
            'branch_id' => $this->branch1->id,
            'store_id' => $this->store1->id,
            'is_active' => true,
        ]);

        $response = $this->actingAs($seller)->get('/admin/dashboard');
        $response->assertStatus(403);
    }
}

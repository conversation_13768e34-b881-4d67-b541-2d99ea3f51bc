<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_id',
        'product_id',
        'quantity',
        'cost_price',
        'total_price',
        'distributed_quantity',
        'is_distributed'
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'distributed_quantity' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'is_distributed' => 'boolean'
    ];

    public function purchase()
    {
        return $this->belongsTo(Purchase::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function branch()
    {
        return $this->hasOneThrough(Branch::class, Purchase::class, 'id', 'id', 'purchase_id', 'branch_id');
    }

    public function returnItems()
    {
        return $this->hasMany(PurchaseReturnItem::class);
    }

    public function distributions()
    {
        return $this->hasMany(PurchaseItemDistribution::class);
    }

    // Accessors
    public function getRemainingDistributionQuantityAttribute()
    {
        return $this->quantity - $this->distributed_quantity;
    }

    public function getIsFullyDistributedAttribute()
    {
        return $this->distributed_quantity >= $this->quantity;
    }

    public function getTotalReturnedQuantityAttribute(): float
    {
        return $this->returnItems()->sum('quantity_returned');
    }

    public function getRemainingQuantityAttribute(): float
    {
        return $this->quantity - $this->total_returned_quantity;
    }

    public function canBeReturned(): bool
    {
        return $this->remaining_quantity > 0;
    }
}

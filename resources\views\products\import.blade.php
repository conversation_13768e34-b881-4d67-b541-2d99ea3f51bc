<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-upload text-primary"></i> {{ __('استيراد المنتجات') }}
                </h2>
                <p class="text-muted small mb-0">استيراد المنتجات من ملف Excel أو CSV</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid py-4">
        <div class="row">
            <!-- Instructions Section -->
            <div class="col-xl-4 col-lg-5 mb-4">
                <div class="card shadow border-0 h-100">
                    <div class="card-header bg-gradient-info text-white py-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle fa-lg me-3"></i>
                            <h5 class="mb-0 fw-bold">تعليمات الاستيراد</h5>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <div class="alert alert-success border-0 mb-4">
                            <h6 class="alert-heading fw-bold">
                                <i class="fas fa-rocket me-2"></i>استيراد سريع لآلاف المنتجات!
                            </h6>
                            <p class="mb-0 small">يمكنك استيراد 1000 منتج أو أكثر في دقائق معدودة</p>
                        </div>

                        <div class="mb-4">
                            <h6 class="fw-bold text-primary mb-3">
                                <i class="fas fa-list-ol me-2"></i>خطوات الاستيراد:
                            </h6>
                            <ol class="small">
                                <li class="mb-2">حمل قالب Excel من الأسفل</li>
                                <li class="mb-2">املأ البيانات في القالب</li>
                                <li class="mb-2">احفظ الملف بصيغة Excel أو CSV</li>
                                <li class="mb-2">ارفع الملف هنا</li>
                                <li class="mb-2">انقر "استيراد المنتجات"</li>
                            </ol>
                        </div>

                        <div class="mb-4">
                            <h6 class="fw-bold text-primary mb-3">
                                <i class="fas fa-table me-2"></i>الأعمدة المطلوبة:
                            </h6>
                            <ul class="list-unstyled small">
                                <li><strong>name:</strong> اسم المنتج (مطلوب)</li>
                                <li><strong>category:</strong> الفئة (مطلوب)</li>
                                <li><strong>description:</strong> الوصف (اختياري)</li>
                                <li><strong>price:</strong> سعر التكلفة (اختياري)</li>
                                <li><strong>selling_price:</strong> سعر البيع (اختياري)</li>
                                <li><strong>is_active:</strong> الحالة (اختياري)</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning border-0">
                            <small>
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>ملاحظة:</strong> إذا لم تكن الفئة موجودة، سيتم إنشاؤها تلقائياً
                            </small>
                        </div>

                        <div class="text-center">
                            <a href="{{ route('admin.products.import.template') }}" class="btn btn-success">
                                <i class="fas fa-download"></i> تحميل قالب Excel
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload Section -->
            <div class="col-xl-8 col-lg-7">
                <div class="card shadow border-0">
                    <div class="card-header bg-gradient-primary text-white py-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-cloud-upload-alt fa-lg me-3"></i>
                            <h5 class="mb-0 fw-bold">رفع ملف المنتجات</h5>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <form action="{{ route('admin.products.import.store') }}" method="POST"
                            enctype="multipart/form-data" id="import-form">
                            @csrf

                            <!-- File Upload Area -->
                            <div class="mb-4">
                                <label for="file" class="form-label fw-bold">
                                    <i class="fas fa-file-excel text-success me-2"></i>اختر ملف Excel أو CSV
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="upload-area border-2 border-dashed rounded p-4 text-center"
                                    id="upload-area">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">اسحب الملف هنا أو انقر للاختيار</h5>
                                        <p class="text-muted small mb-3">
                                            الصيغ المدعومة: Excel (.xlsx, .xls) أو CSV (.csv)
                                        </p>
                                        <p class="text-muted small">الحد الأقصى: 10 ميجابايت</p>
                                    </div>
                                    <input type="file" name="file" id="file"
                                        class="form-control @error('file') is-invalid @enderror"
                                        accept=".xlsx,.xls,.csv" required style="display: none;">
                                </div>
                                @error('file')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- File Info Display -->
                            <div id="file-info" class="mb-4" style="display: none;">
                                <div class="alert alert-info border-0">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-excel fa-2x text-success me-3"></i>
                                        <div>
                                            <h6 class="mb-1" id="file-name"></h6>
                                            <small class="text-muted" id="file-size"></small>
                                        </div>
                                        <button type="button" class="btn btn-outline-danger btn-sm ms-auto"
                                            onclick="clearFile()">
                                            <i class="fas fa-times"></i> إزالة
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div id="progress-container" class="mb-4" style="display: none;">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                        role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="text-muted mt-2 d-block">جاري استيراد المنتجات...</small>
                            </div>

                            <!-- Form Actions -->
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <button type="submit" class="btn btn-primary" id="import-btn">
                                        <i class="fas fa-upload"></i> استيراد المنتجات
                                    </button>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-outline-secondary me-2"
                                        onclick="window.history.back()">
                                        <i class="fas fa-times"></i> إلغاء
                                    </button>

                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Statistics Card -->
                <div class="card shadow border-0 mt-4">
                    <div class="card-header bg-gradient-secondary text-white py-3">
                        <h6 class="mb-0 fw-bold">
                            <i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <h4 class="text-primary mb-1">{{ \App\Models\Product::count() }}</h4>
                                    <small class="text-muted">إجمالي المنتجات</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <h4 class="text-success mb-1">{{ \App\Models\Category::count() }}</h4>
                                    <small class="text-muted">إجمالي الفئات</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <h4 class="text-info mb-1">
                                        {{ \App\Models\Product::where('is_active', true)->count() }}</h4>
                                    <small class="text-muted">المنتجات النشطة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            .upload-area {
                border-color: #dee2e6 !important;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .upload-area:hover {
                border-color: #007bff !important;
                background-color: #f8f9fa;
            }

            .upload-area.dragover {
                border-color: #007bff !important;
                background-color: #e3f2fd;
            }

            .stat-item {
                padding: 1rem;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('file');
            const fileInfo = document.getElementById('file-info');
            const fileName = document.getElementById('file-name');
            const fileSize = document.getElementById('file-size');
            const importForm = document.getElementById('import-form');
            const progressContainer = document.getElementById('progress-container');
            const importBtn = document.getElementById('import-btn');

            // Click to upload
            uploadArea.addEventListener('click', () => fileInput.click());

            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    displayFileInfo(files[0]);
                }
            });

            // File selection
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    displayFileInfo(e.target.files[0]);
                }
            });

            function displayFileInfo(file) {
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileInfo.style.display = 'block';
                uploadArea.style.display = 'none';
            }

            function clearFile() {
                fileInput.value = '';
                fileInfo.style.display = 'none';
                uploadArea.style.display = 'block';
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Form submission with progress
            importForm.addEventListener('submit', function(e) {
                if (!fileInput.files.length) {
                    e.preventDefault();
                    alert('يرجى اختيار ملف للاستيراد');
                    return;
                }

                // Show progress
                progressContainer.style.display = 'block';
                importBtn.disabled = true;
                importBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاستيراد...';

                // Simulate progress (since we can't track actual upload progress easily)
                let progress = 0;
                const progressBar = document.querySelector('.progress-bar');
                const interval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress > 90) progress = 90;
                    progressBar.style.width = progress + '%';
                }, 500);

                // Clear interval after form submission
                setTimeout(() => clearInterval(interval), 1000);
            });
        </script>
    @endpush
</x-app-layout>

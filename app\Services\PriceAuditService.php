<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Branch;
use App\Models\User;
use App\Models\PriceChangeLog;
use App\Models\BranchInventory;

class PriceAuditService
{
    protected $priceResolutionService;

    public function __construct(PriceResolutionService $priceResolutionService)
    {
        $this->priceResolutionService = $priceResolutionService;
    }

    /**
     * Log a price change
     *
     * @param Product $product
     * @param string $priceType
     * @param float|null $oldPrice
     * @param float $newPrice
     * @param Branch|int|null $branch
     * @param User|null $user
     * @param array $options
     * @return PriceChangeLog
     */
    public function logPriceChange(
        Product $product,
        string $priceType,
        ?float $oldPrice,
        float $newPrice,
        $branch = null,
        ?User $user = null,
        array $options = []
    ): PriceChangeLog {
        $branchId = is_object($branch) ? $branch->id : $branch;
        
        // Calculate price difference and percentage change
        $priceDifference = $newPrice - ($oldPrice ?? 0);
        $percentageChange = null;
        
        if ($oldPrice && $oldPrice > 0) {
            $percentageChange = ($priceDifference / $oldPrice);
        }

        // Get cost price for validation
        $costPrice = $this->getCostPrice($product, $branchId);
        $belowCostPrice = $newPrice < $costPrice;

        // Calculate profit margins
        $profitMarginBefore = null;
        $profitMarginAfter = null;
        
        if ($costPrice > 0) {
            if ($oldPrice) {
                $profitMarginBefore = ($oldPrice - $costPrice) / $costPrice;
            }
            $profitMarginAfter = ($newPrice - $costPrice) / $costPrice;
        }

        // Validate the new price
        $validation = $this->priceResolutionService->validateSellingPrice(
            $newPrice,
            $product,
            $branchId ?: $product->id, // Fallback for product-level changes
            $user
        );

        return PriceChangeLog::create([
            'product_id' => $product->id,
            'branch_id' => $branchId,
            'user_id' => $user?->id,
            'price_type' => $priceType,
            'old_price' => $oldPrice,
            'new_price' => $newPrice,
            'price_difference' => $priceDifference,
            'percentage_change' => $percentageChange,
            'change_reason' => $options['reason'] ?? 'manual_update',
            'change_source' => $options['source'] ?? 'manual',
            'metadata' => $options['metadata'] ?? null,
            'validation_warnings' => $validation['warnings'] ?? [],
            'below_cost_price' => $belowCostPrice,
            'profit_margin_before' => $profitMarginBefore,
            'profit_margin_after' => $profitMarginAfter,
        ]);
    }

    /**
     * Log product price change
     */
    public function logProductPriceChange(
        Product $product,
        string $field, // 'price' or 'selling_price'
        ?float $oldPrice,
        float $newPrice,
        ?User $user = null,
        array $options = []
    ): PriceChangeLog {
        $priceType = $field === 'selling_price' ? 'product_selling_price' : 'product_price';
        
        return $this->logPriceChange(
            $product,
            $priceType,
            $oldPrice,
            $newPrice,
            null,
            $user,
            $options
        );
    }

    /**
     * Log branch inventory price change
     */
    public function logBranchPriceChange(
        Product $product,
        Branch $branch,
        string $field, // 'sale_price_1', 'sale_price_2', 'sale_price_3', 'cost_price'
        ?float $oldPrice,
        float $newPrice,
        ?User $user = null,
        array $options = []
    ): PriceChangeLog {
        $priceType = $field === 'cost_price' ? 'branch_cost_price' : "branch_{$field}";
        
        return $this->logPriceChange(
            $product,
            $priceType,
            $oldPrice,
            $newPrice,
            $branch,
            $user,
            $options
        );
    }

    /**
     * Get price change history for a product
     */
    public function getProductPriceHistory(Product $product, ?Branch $branch = null, int $limit = 50): array
    {
        $query = PriceChangeLog::where('product_id', $product->id)
            ->with(['user', 'branch'])
            ->orderBy('created_at', 'desc')
            ->limit($limit);

        if ($branch) {
            $query->where(function ($q) use ($branch) {
                $q->where('branch_id', $branch->id)
                  ->orWhereNull('branch_id'); // Include product-level changes
            });
        }

        return $query->get()->toArray();
    }

    /**
     * Get recent significant price changes
     */
    public function getRecentSignificantChanges(int $days = 7, float $threshold = 0.20): array
    {
        return PriceChangeLog::with(['product', 'branch', 'user'])
            ->recent($days)
            ->significant($threshold)
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Get changes below cost price
     */
    public function getChangesBelowCost(int $days = 30): array
    {
        return PriceChangeLog::with(['product', 'branch', 'user'])
            ->recent($days)
            ->belowCost()
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Get price change statistics
     */
    public function getPriceChangeStats(int $days = 30): array
    {
        $changes = PriceChangeLog::recent($days);
        
        return [
            'total_changes' => $changes->count(),
            'price_increases' => $changes->where('price_difference', '>', 0)->count(),
            'price_decreases' => $changes->where('price_difference', '<', 0)->count(),
            'significant_changes' => $changes->significant()->count(),
            'below_cost_changes' => $changes->belowCost()->count(),
            'average_change_percentage' => $changes->avg('percentage_change'),
            'products_affected' => $changes->distinct('product_id')->count(),
            'branches_affected' => $changes->whereNotNull('branch_id')->distinct('branch_id')->count(),
        ];
    }

    /**
     * Get cost price for validation
     */
    private function getCostPrice(Product $product, ?int $branchId = null): float
    {
        if ($branchId) {
            $branchInventory = BranchInventory::where('branch_id', $branchId)
                ->where('product_id', $product->id)
                ->first();
                
            if ($branchInventory && $branchInventory->cost_price > 0) {
                return (float) $branchInventory->cost_price;
            }
        }

        return (float) ($product->cost_price ?? 0);
    }
}

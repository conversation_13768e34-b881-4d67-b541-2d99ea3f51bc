<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\Branch;
use App\Models\Product;
use App\Models\Category;
use App\Models\BranchInventory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BranchAddProductTest extends TestCase
{
    use RefreshDatabase;

    protected $sellerUser;
    protected $branch;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();

        // Create seller role with add-products permission
        $sellerRole = Role::create([
            'name' => 'seller',
            'description' => 'Seller',
            'permissions' => [
                'branches.sales-inventory.view',
                'branches.sales-inventory.add-products',
            ],
            'is_active' => true,
        ]);

        // Create test branch
        $this->branch = Branch::create([
            'code' => 'BR001',
            'name' => 'Test Branch',
            'address' => '123 Test St',
            'phone' => '+1234567890',
            'email' => '<EMAIL>',
            'opening_balance' => 10000,
            'is_active' => true,
        ]);

        // Create seller user
        $this->sellerUser = User::create([
            'name' => 'Test Seller',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $sellerRole->id,
            'branch_id' => $this->branch->id,
            'is_active' => true,
        ]);

        // Create test category and product
        $category = Category::create([
            'name' => 'Test Category',
            'description' => 'Test category description',
            'is_active' => true,
        ]);

        $this->product = Product::create([
            'name' => 'Test Product',
            'sku' => 'TEST001',
            'category_id' => $category->id,
            'price' => 20.00,
            'selling_price' => 30.00,
            'is_active' => true,
        ]);
    }

    public function test_seller_can_access_add_product_page()
    {
        $this->actingAs($this->sellerUser);

        $response = $this->get(route('admin.branches.add-product', $this->branch));
        
        $response->assertStatus(200);
        $response->assertSee('إضافة منتجات للفرع');
        $response->assertSee($this->product->name);
    }

    public function test_seller_can_add_products_to_branch_inventory()
    {
        $this->actingAs($this->sellerUser);

        // Ensure product is not in branch inventory initially
        $this->assertDatabaseMissing('branch_inventory', [
            'branch_id' => $this->branch->id,
            'product_id' => $this->product->id,
        ]);

        // Submit form to add product
        $response = $this->post(route('admin.branches.store-product', $this->branch), [
            'products' => [
                $this->product->id => [
                    'product_id' => $this->product->id,
                    'quantity' => 10,
                    'cost_price' => 25.50,
                    'sale_price_1' => 35.00,
                    'sale_price_2' => null,
                    'sale_price_3' => null,
                ]
            ]
        ]);

        // Should redirect to sales inventory page
        $response->assertRedirect(route('admin.branches.sales-inventory', $this->branch));
        $response->assertSessionHas('success');

        // Verify product was added to branch inventory
        $this->assertDatabaseHas('branch_inventory', [
            'branch_id' => $this->branch->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'cost_price' => 25.50,
            'sale_price_1' => 35.00,
        ]);
    }

    public function test_form_validation_requires_products()
    {
        $this->actingAs($this->sellerUser);

        // Submit form without products
        $response = $this->post(route('admin.branches.store-product', $this->branch), [
            'products' => []
        ]);

        $response->assertSessionHasErrors(['products']);
    }

    public function test_form_validation_requires_quantity_and_cost_price()
    {
        $this->actingAs($this->sellerUser);

        // Submit form with missing required fields
        $response = $this->post(route('admin.branches.store-product', $this->branch), [
            'products' => [
                $this->product->id => [
                    'product_id' => $this->product->id,
                    // Missing quantity and cost_price
                ]
            ]
        ]);

        $response->assertSessionHasErrors(['products.*.quantity', 'products.*.cost_price']);
    }
}

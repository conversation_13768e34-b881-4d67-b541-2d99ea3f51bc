<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('purchase_details', function (Blueprint $table) {
            // Drop old columns
            // $table->dropColumn(['branch_id', 'purchase_price', 'sale_price_1', 'sale_price_2', 'sale_price_3']);

            // Add new column
            // $table->decimal('cost_price', 10, 2)->after('quantity');
        });
    }

    public function down()
    {
        Schema::table('purchase_details', function (Blueprint $table) {
            // Drop new column
            $table->dropColumn('cost_price');

            // Add back old columns
            $table->foreignId('branch_id')->after('product_id')->constrained()->onDelete('cascade');
            $table->decimal('purchase_price', 10, 2)->after('quantity');
            $table->decimal('sale_price_1', 10, 2)->after('purchase_price');
            $table->decimal('sale_price_2', 10, 2)->after('sale_price_1');
            $table->decimal('sale_price_3', 10, 2)->after('sale_price_2');
        });
    }
};

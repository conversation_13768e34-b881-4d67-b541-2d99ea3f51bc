<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Purchase;
use Illuminate\Support\Facades\DB;

class UpdatePurchaseRemainingAmounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'purchase:update-remaining-amounts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update remaining amounts for all purchases based on total_amount - paid_amount';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating purchase remaining amounts...');

        // Update all purchases where remaining_amount doesn't match calculated value
        $updated = DB::table('purchases')
            ->whereRaw('remaining_amount != (total_amount - paid_amount)')
            ->update([
                'remaining_amount' => DB::raw('total_amount - paid_amount'),
                'updated_at' => now()
            ]);

        $this->info("Updated {$updated} purchase records.");

        // Show some statistics
        $totalPurchases = Purchase::count();
        $purchasesWithRemaining = Purchase::where('remaining_amount', '>', 0)->count();
        $totalRemaining = Purchase::where('remaining_amount', '>', 0)->sum('remaining_amount');

        $this->info("Total purchases: {$totalPurchases}");
        $this->info("Purchases with remaining amounts: {$purchasesWithRemaining}");
        $this->info("Total remaining amount: " . number_format($totalRemaining, 2));

        return 0;
    }
}

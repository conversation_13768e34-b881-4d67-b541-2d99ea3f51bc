<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_return_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_return_id')->constrained()->onDelete('cascade');
            $table->foreignId('purchase_item_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->decimal('quantity_returned', 8, 2); // Quantity being returned
            $table->decimal('original_quantity', 8, 2); // Original purchased quantity
            $table->decimal('cost_price', 10, 2); // Cost price per unit
            $table->decimal('total_cost', 10, 2); // Total cost for returned quantity
            $table->enum('condition', ['good', 'damaged', 'expired', 'defective'])->default('good');
            $table->text('item_notes')->nullable(); // Notes specific to this item
            $table->boolean('inventory_adjusted')->default(false); // Track if inventory was adjusted
            $table->timestamps();

            $table->index(['purchase_return_id', 'product_id']);
            $table->index(['purchase_item_id', 'condition']);
            $table->index(['product_id', 'condition']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_return_items');
    }
};

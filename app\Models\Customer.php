<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\DB;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'opening_balance',
        'is_active',
    ];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function account(): MorphOne
    {
        return $this->morphOne(Account::class, 'accountable');
    }

    public function getRemainingBalance(): float
    {
        return $this->account?->getRemainingBalance() ?? 0;
    }

    public function getOwedAmount(): float
    {
        return $this->account?->getOwedAmount() ?? 0;
    }

    public function getCreditAmount(): float
    {
        return $this->account?->getCreditAmount() ?? 0;
    }

    public function getTotalSales(): float
    {
        return $this->sales()->sum('total_amount');
    }

    public function getTotalPaid(): float
    {
        return $this->account?->transactions()
            ->where('type', 'deposit')
            ->sum('amount') ?? 0;
    }

    public function getOutstandingAmount(): float
    {
        return $this->getTotalSales() - $this->getTotalPaid();
    }

    /**
     * Get total outstanding amount from unpaid sales.
     */
    public function getTotalOutstandingAmount(): float
    {
        return $this->sales()->whereRaw('total_amount > paid_amount')->sum(DB::raw('total_amount - paid_amount'));
    }
}

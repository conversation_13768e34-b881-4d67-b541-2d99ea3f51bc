<x-app-layout>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">تعديل عملية البيع: {{ $sale->invoice_number }}</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ user_route('sales.update', $sale) }}" method="POST" id="saleForm">
                            @csrf
                            @method('PUT')
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="customer_id" class="form-label">العميل</label>
                                    <select class="form-select @error('customer_id') is-invalid @enderror"
                                        id="customer_id" name="customer_id" required>
                                        <option value="">اختر العميل</option>
                                        @foreach ($customers as $customer)
                                            <option value="{{ $customer->id }}"
                                                {{ old('customer_id', $sale->customer_id) == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('customer_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select @error('status') is-invalid @enderror" id="status"
                                        name="status" required>
                                        <option value="pending"
                                            {{ old('status', $sale->status) == 'pending' ? 'selected' : '' }}>قيد
                                            الانتظار</option>
                                        <option value="completed"
                                            {{ old('status', $sale->status) == 'completed' ? 'selected' : '' }}>مكتمل
                                        </option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6>المنتجات</h6>
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="productsTable">
                                            <thead>
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>الكمية</th>
                                                    <th>السعر</th>
                                                    <th>المجموع</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($sale->products as $index => $product)
                                                    <tr>
                                                        <td>
                                                            <select class="form-select product-select"
                                                                name="products[{{ $index }}][id]" required>
                                                                <option value="">اختر المنتج</option>
                                                                @foreach ($products as $p)
                                                                    <option value="{{ $p->id }}"
                                                                        data-price="{{ $p->price }}"
                                                                        data-stock="{{ $p->stock }}"
                                                                        {{ $product->id == $p->id ? 'selected' : '' }}>
                                                                        {{ $p->name }} (المخزون:
                                                                        {{ $p->stock }})
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <input type="number" class="form-control quantity-input"
                                                                name="products[{{ $index }}][quantity]"
                                                                min="1" value="{{ $product->pivot->quantity }}"
                                                                required>
                                                        </td>
                                                        <td>
                                                            <input type="number" class="form-control price-input"
                                                                name="products[{{ $index }}][price]"
                                                                step="0.01" value="{{ $product->pivot->price }}"
                                                                readonly>
                                                        </td>
                                                        <td>
                                                            <input type="number" class="form-control subtotal-input"
                                                                value="{{ $product->pivot->quantity * $product->pivot->price }}"
                                                                readonly>
                                                        </td>
                                                        <td>
                                                            <button type="button"
                                                                class="btn btn-danger remove-product">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="5">
                                                        <button type="button" class="btn btn-success" id="addProduct">
                                                            <i class="fas fa-plus"></i> إضافة منتج
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3" class="text-end"><strong>الإجمالي:</strong></td>
                                                    <td colspan="2">
                                                        <input type="number" class="form-control" id="total"
                                                            name="total" value="{{ $sale->total }}" readonly>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                <a href="{{ route('sales.index') }}" class="btn btn-secondary">إلغاء</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const productsTable = document.getElementById('productsTable');
                const addProductBtn = document.getElementById('addProduct');
                let productCount = {{ count($sale->products) }};

                // Add new product row
                addProductBtn.addEventListener('click', function() {
                    const newRow = productsTable.querySelector('tbody tr').cloneNode(true);
                    const inputs = newRow.querySelectorAll('input, select');
                    inputs.forEach(input => {
                        input.value = '';
                        input.name = input.name.replace(/\[\d+\]/, `[${productCount}]`);
                    });
                    productsTable.querySelector('tbody').appendChild(newRow);
                    productCount++;
                    updateEventListeners();
                });

                // Remove product row
                function updateEventListeners() {
                    document.querySelectorAll('.remove-product').forEach(button => {
                        button.addEventListener('click', function() {
                            if (productsTable.querySelectorAll('tbody tr').length > 1) {
                                this.closest('tr').remove();
                                calculateTotal();
                            }
                        });
                    });

                    document.querySelectorAll('.product-select').forEach(select => {
                        select.addEventListener('change', function() {
                            const option = this.options[this.selectedIndex];
                            const price = option.dataset.price;
                            const stock = option.dataset.stock;
                            const row = this.closest('tr');
                            row.querySelector('.price-input').value = price;
                            row.querySelector('.quantity-input').max = stock;
                            calculateRowTotal(row);
                        });
                    });

                    document.querySelectorAll('.quantity-input').forEach(input => {
                        input.addEventListener('input', function() {
                            calculateRowTotal(this.closest('tr'));
                        });
                    });
                }

                // Calculate row total
                function calculateRowTotal(row) {
                    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
                    const price = parseFloat(row.querySelector('.price-input').value) || 0;
                    const subtotal = quantity * price;
                    row.querySelector('.subtotal-input').value = subtotal.toFixed(2);
                    calculateTotal();
                }

                // Calculate total
                function calculateTotal() {
                    const subtotals = Array.from(document.querySelectorAll('.subtotal-input')).map(input => parseFloat(
                        input.value) || 0);
                    const total = subtotals.reduce((sum, subtotal) => sum + subtotal, 0);
                    document.getElementById('total').value = total.toFixed(2);
                }

                // Form validation
                document.getElementById('saleForm').addEventListener('submit', function(e) {
                    const products = document.querySelectorAll('.product-select');
                    let hasProducts = false;

                    products.forEach(select => {
                        if (select.value) {
                            hasProducts = true;
                        }
                    });

                    if (!hasProducts) {
                        e.preventDefault();
                        alert('يجب إضافة منتج واحد على الأقل');
                    }
                });

                // Initial event listeners
                updateEventListeners();
            });
        </script>
    @endpush
</x-app-layout>

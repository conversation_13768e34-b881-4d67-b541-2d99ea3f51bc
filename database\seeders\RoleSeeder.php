<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    public function run(): void
    {
        // Create Admin Role
        $adminRole = Role::updateOrCreate([
            'name' => 'admin',
        ], [
            'description' => 'Administrator with full access to all branches and stores',
            'permissions' => [
                'users.view',
                'users.create',
                'users.edit',
                'users.delete',
                'roles.view',
                'roles.create',
                'roles.edit',
                'roles.delete',
                'products.view',
                'products.create',
                'products.edit',
                'products.delete',
                'categories.view',
                'categories.create',
                'categories.edit',
                'categories.delete',
                'branches.view',
                'branches.create',
                'branches.edit',
                'branches.delete',
                'stores.view',
                'stores.create',
                'stores.edit',
                'stores.delete',
                'sales.view',
                'sales.create',
                'sales.edit',
                'sales.delete',
                'sales.complete',
                'sales.cancel',
                'purchases.view',
                'purchases.create',
                'purchases.edit',
                'purchases.delete',
                'purchases.complete',
                'purchases.cancel',
                'customers.view',
                'customers.create',
                'customers.edit',
                'customers.delete',
                'suppliers.view',
                'suppliers.create',
                'suppliers.edit',
                'suppliers.delete',
                'expenses.view',
                'expenses.create',
                'expenses.edit',
                'expenses.delete',
                'reports.sales',
                'reports.purchases',
                'reports.inventory',
                'reports.profit',
                'settings.company',
                'accounts.view',
                'accounts.create',
                'accounts.edit',
                'accounts.delete',
                'account-transactions.view',
                'account-transactions.create',
                'account-transactions.edit',
                'account-transactions.delete',
                // Store (Warehouse) Operations - Full Access
                'stores.inventory.manage',
                'stores.inventory.add-products',
                'stores.inventory.adjust',
                'stores.inventory.transfer-out',
                'stores.inventory.receive',
                'stores.inventory.reports',
                // Branch (Sales) Operations - Full Access
                'branches.sales-inventory.view',
                'branches.sales-inventory.manage',
                'branches.sales-inventory.add-products',
                'branches.sales-inventory.receive',
                'branches.sales-inventory.adjust',
                'branches.sales-inventory.pricing',
                'branches.sales-inventory.request-transfer',
                // Inventory Transfer Operations - Full Access
                'inventory-transfers.view',
                'inventory-transfers.create',
                'inventory-transfers.approve',
                'inventory-transfers.ship',
                'inventory-transfers.receive',
                'inventory-transfers.cancel',
            ],
            'is_active' => true,
        ]);

        // Create Manager Role
        $managerRole = Role::updateOrCreate([
            'name' => 'manager',
        ], [
            'description' => 'Branch manager with access to single branch operations',
            'permissions' => [
                'products.view',
                'products.create',
                'products.edit',
                'categories.view',
                'categories.create',
                'categories.edit',
                'stores.view',
                'sales.view',
                'sales.create',
                'sales.edit',
                'sales.complete',
                'purchases.view',
                'purchases.create',
                'purchases.edit',
                'purchases.complete',
                'customers.view',
                'customers.create',
                'customers.edit',
                'suppliers.view',
                'suppliers.create',
                'suppliers.edit',
                'expenses.view',
                'expenses.create',
                'expenses.edit',
                'reports.sales',
                'reports.purchases',
                'reports.inventory',
                'reports.profit',
                'accounts.view',
                'accounts.create',
                'accounts.edit',
                'account-transactions.view',
                'account-transactions.create',
                'account-transactions.edit',
                // Branch (Sales) Operations - Full Access
                'branches.sales-inventory.view',
                'branches.sales-inventory.manage',
                'branches.sales-inventory.add-products',
                'branches.sales-inventory.receive',
                'branches.sales-inventory.adjust',
                'branches.sales-inventory.pricing',
                'branches.sales-inventory.request-transfer',
                // Store Operations - Limited Access (View Only)
                'stores.inventory.reports',
                // Inventory Transfer Operations - Limited Access
                'inventory-transfers.view',
                'inventory-transfers.create',
                'inventory-transfers.receive',
            ],
            'is_active' => true,
        ]);

        // Create Seller Role
        $sellerRole = Role::updateOrCreate([
            'name' => 'seller',
        ], [
            'description' => 'Seller with limited access to selling operations only',
            'permissions' => [
                'products.view',
                'sales.view',
                'sales.create',
                'customers.view',
                'customers.create',
                'accounts.view',
                'account-transactions.view',
                // Branch Sales Operations - Limited Access
                'branches.sales-inventory.view',
                'branches.sales-inventory.add-products',
                'branches.sales-inventory.request-transfer',
                // Inventory Transfer Operations - Very Limited Access
                'inventory-transfers.view',
            ],
            'is_active' => true,
        ]);

        // Create Warehouse Staff Role (for store operations)
        $warehouseRole = Role::firstOrCreate([
            'name' => 'warehouse_staff',
        ], [
            'description' => 'Warehouse staff with access to store inventory operations',
            'permissions' => [
                'products.view',
                'stores.view',
                // Store (Warehouse) Operations - Full Access
                'stores.inventory.manage',
                'stores.inventory.add-products',
                'stores.inventory.adjust',
                'stores.inventory.transfer-out',
                'stores.inventory.receive',
                'stores.inventory.reports',
                // Inventory Transfer Operations - Warehouse Side
                'inventory-transfers.view',
                'inventory-transfers.approve',
                'inventory-transfers.ship',
                'inventory-transfers.receive',
            ],
            'is_active' => true,
        ]);

        // Create Employee Role (alternative to seller)
        $employeeRole = Role::firstOrCreate([
            'name' => 'employee',
        ], [
            'description' => 'General employee with basic access',
            'permissions' => [
                'products.view',
                'sales.view',
                'sales.create',
                'customers.view',
                'accounts.view',
                // Branch Sales Operations - Basic Access
                'branches.sales-inventory.view',
            ],
            'is_active' => true,
        ]);

        $this->command->info('Roles created successfully!');
    }
}

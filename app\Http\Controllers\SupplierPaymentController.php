<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use App\Models\Supplier;
use App\Models\AccountTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Helpers\AlertHelper;

class SupplierPaymentController extends Controller
{
    /**
     * Display outstanding payments for all suppliers.
     */
    public function index(Request $request)
    {
        $query = Purchase::with(['supplier', 'supplier.account'])
            ->where('remaining_amount', '>', 0)
            ->orderBy('purchase_date', 'asc');

        // Filter by supplier if specified
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // Filter by date range if specified
        if ($request->filled('date_from')) {
            $query->whereDate('purchase_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('purchase_date', '<=', $request->date_to);
        }

        $outstandingPurchases = $query->paginate(15);
        $suppliers = Supplier::where('is_active', true)->orderBy('name')->get();

        // Calculate summary statistics
        $totalOutstanding = Purchase::where('remaining_amount', '>', 0)->sum('remaining_amount');
        $totalPurchases = Purchase::where('remaining_amount', '>', 0)->count();
        $suppliersWithOutstanding = Purchase::where('remaining_amount', '>', 0)
            ->distinct('supplier_id')
            ->count('supplier_id');

        return view('supplier-payments.index', compact(
            'outstandingPurchases',
            'suppliers',
            'totalOutstanding',
            'totalPurchases',
            'suppliersWithOutstanding'
        ));
    }

    /**
     * Show payment form for a specific purchase.
     */
    public function create(Purchase $purchase)
    {
        if ($purchase->actual_remaining_amount <= 0) {
            AlertHelper::warning('هذه العملية مدفوعة بالكامل');
            return redirect()->to(user_route('supplier-payments.index'));
        }

        return view('supplier-payments.create', compact('purchase'));
    }

    /**
     * Process payment for a purchase.
     */
    public function store(Request $request, Purchase $purchase)
    {
        $request->validate([
            'amount' => [
                'required',
                'numeric',
                'min:0.01',
                'max:' . $purchase->actual_remaining_amount
            ],
            'payment_method' => 'required|in:cash,bank_transfer,check,credit_card,other',
            'notes' => 'nullable|string|max:500',
            'reference_number' => 'nullable|string|max:100'
        ], [
            'amount.required' => 'مبلغ الدفع مطلوب',
            'amount.numeric' => 'مبلغ الدفع يجب أن يكون رقماً',
            'amount.min' => 'مبلغ الدفع يجب أن يكون أكبر من صفر',
            'amount.max' => 'مبلغ الدفع لا يمكن أن يتجاوز المبلغ المتبقي',
            'payment_method.required' => 'طريقة الدفع مطلوبة',
            'payment_method.in' => 'طريقة الدفع غير صحيحة'
        ]);

        try {
            DB::beginTransaction();

            $paymentMethods = [
                'cash' => 'نقدي',
                'bank_transfer' => 'تحويل بنكي',
                'check' => 'شيك',
                'credit_card' => 'بطاقة ائتمان',
                'other' => 'أخرى'
            ];

            $notes = $request->notes ?: "دفعة إضافية لعملية شراء رقم {$purchase->id} - طريقة الدفع: {$paymentMethods[$request->payment_method]}";

            if ($request->filled('reference_number')) {
                $notes .= " - رقم المرجع: {$request->reference_number}";
            }

            // Make the payment using the Purchase model method
            $transaction = $purchase->makePayment(
                $request->amount,
                $request->payment_method,
                $notes
            );

            DB::commit();

            AlertHelper::success('تم تسجيل الدفعة بنجاح');
            return redirect()->to(user_route('supplier-payments.index'));
        } catch (\Exception $e) {
            DB::rollBack();
            AlertHelper::error('حدث خطأ أثناء تسجيل الدفعة: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Show payment history for a specific purchase.
     */
    public function show(Purchase $purchase)
    {
        $paymentTransactions = $purchase->paymentTransactions()
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('supplier-payments.show', compact('purchase', 'paymentTransactions'));
    }

    /**
     * Show supplier payment summary.
     */
    public function supplierSummary(Supplier $supplier)
    {
        $outstandingPurchases = $supplier->purchasesWithOutstandingBalance()
            ->with('supplier.account')
            ->get();

        $paymentHistory = AccountTransaction::where('account_id', $supplier->account->id ?? 0)
            ->where('type', 'deposit')
            ->whereHasMorph('transactionable', [Purchase::class])
            ->with(['transactionable', 'user'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        $totalOutstanding = $supplier->getTotalOutstandingAmount();
        $totalPaid = $supplier->getTotalPaidAmount();
        $totalPurchases = $supplier->getTotalPurchases();

        return view('supplier-payments.supplier-summary', compact(
            'supplier',
            'outstandingPurchases',
            'paymentHistory',
            'totalOutstanding',
            'totalPaid',
            'totalPurchases'
        ));
    }
}

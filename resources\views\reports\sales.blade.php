<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('تقرير المبيعات') }}
            </h2>
            <div>
                <button type="button" class="btn btn-success" onclick="printReport()">
                    <i class="fas fa-print"></i> {{ __('طباعة') }}
                </button>
                <button type="button" class="btn btn-primary" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> {{ __('تصدير إلى Excel') }}
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Filters -->
                    <form action="{{ route('reports.sales') }}" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="branch_id">{{ __('الفرع') }}</label>
                                    <select name="branch_id" id="branch_id" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="customer_id">{{ __('العميل') }}</label>
                                    <select name="customer_id" id="customer_id" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}" {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="user_id">{{ __('المستخدم') }}</label>
                                    <select name="user_id" id="user_id" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="payment_method">{{ __('طريقة الدفع') }}</label>
                                    <select name="payment_method" id="payment_method" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>{{ __('نقدي') }}</option>
                                        <option value="card" {{ request('payment_method') == 'card' ? 'selected' : '' }}>{{ __('بطاقة') }}</option>
                                        <option value="bank" {{ request('payment_method') == 'bank' ? 'selected' : '' }}>{{ __('تحويل بنكي') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_from">{{ __('من تاريخ') }}</label>
                                    <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_to">{{ __('إلى تاريخ') }}</label>
                                    <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="group_by">{{ __('تجميع حسب') }}</label>
                                    <select name="group_by" id="group_by" class="form-select">
                                        <option value="daily" {{ request('group_by') == 'daily' ? 'selected' : '' }}>{{ __('يومي') }}</option>
                                        <option value="weekly" {{ request('group_by') == 'weekly' ? 'selected' : '' }}>{{ __('أسبوعي') }}</option>
                                        <option value="monthly" {{ request('group_by') == 'monthly' ? 'selected' : '' }}>{{ __('شهري') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="sort_by">{{ __('ترتيب حسب') }}</label>
                                    <select name="sort_by" id="sort_by" class="form-select">
                                        <option value="date_asc" {{ request('sort_by') == 'date_asc' ? 'selected' : '' }}>{{ __('التاريخ (تصاعدي)') }}</option>
                                        <option value="date_desc" {{ request('sort_by') == 'date_desc' ? 'selected' : '' }}>{{ __('التاريخ (تنازلي)') }}</option>
                                        <option value="amount_desc" {{ request('sort_by') == 'amount_desc' ? 'selected' : '' }}>{{ __('المبلغ (تنازلي)') }}</option>
                                        <option value="amount_asc" {{ request('sort_by') == 'amount_asc' ? 'selected' : '' }}>{{ __('المبلغ (تصاعدي)') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> {{ __('عرض التقرير') }}
                                </button>
                                <a href="{{ route('reports.sales') }}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> {{ __('إعادة تعيين') }}
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('إجمالي المبيعات') }}</h5>
                                    <h3 class="mb-0">{{ number_format($summary['total_sales'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('صافي المبيعات') }}</h5>
                                    <h3 class="mb-0">{{ number_format($summary['net_sales'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('عدد الفواتير') }}</h5>
                                    <h3 class="mb-0">{{ $summary['total_invoices'] }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('متوسط قيمة الفاتورة') }}</h5>
                                    <h3 class="mb-0">{{ number_format($summary['average_invoice'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('إجمالي الخصومات') }}</h5>
                                    <h3 class="mb-0">{{ number_format($summary['total_discount'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('عدد المنتجات المباعة') }}</h5>
                                    <h3 class="mb-0">{{ $summary['total_items_sold'] }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Top Products -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ __('أعلى المنتجات مبيعاً') }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('المنتج') }}</th>
                                                    <th>{{ __('الكمية') }}</th>
                                                    <th>{{ __('المبلغ') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($topProducts as $product)
                                                    <tr>
                                                        <td>{{ $product['name'] }}</td>
                                                        <td>{{ $product['quantity'] }}</td>
                                                        <td>{{ number_format($product['total'], 2) }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sales Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('الفترة') }}</th>
                                    <th>{{ __('عدد الفواتير') }}</th>
                                    <th>{{ __('إجمالي المبيعات') }}</th>
                                    <th>{{ __('الخصومات') }}</th>
                                    <th>{{ __('صافي المبيعات') }}</th>
                                    <th>{{ __('متوسط قيمة الفاتورة') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($sales as $sale)
                                    <tr>
                                        <td>{{ $sale->period }}</td>
                                        <td>{{ $sale->invoices_count }}</td>
                                        <td>{{ number_format($sale->total_sales, 2) }}</td>
                                        <td>{{ number_format($sale->total_discount, 2) }}</td>
                                        <td>{{ number_format($sale->net_sales, 2) }}</td>
                                        <td>{{ number_format($sale->average_invoice, 2) }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">{{ __('لا توجد بيانات') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $sales->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .rtl .card-title {
            text-align: right;
        }
        .card {
            margin-bottom: 1rem;
        }
        .card-body {
            padding: 1rem;
        }
        .card-title {
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        .card h3 {
            font-size: 1.5rem;
            margin-bottom: 0;
        }
        .card small {
            font-size: 0.75rem;
            opacity: 0.8;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .container-fluid {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            .card {
                break-inside: avoid;
            }
        }
    </style>
    @endpush

    @push('scripts')
    <script>
        function printReport() {
            window.print();
        }

        function exportToExcel() {
            // Get the current URL with all query parameters
            let url = new URL(window.location.href);
            url.searchParams.set('export', 'excel');

            // Redirect to the export URL
            window.location.href = url.toString();
        }
    </script>
    @endpush
</x-app-layout>

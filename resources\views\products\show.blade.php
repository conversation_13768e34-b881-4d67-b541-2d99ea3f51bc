<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-box text-primary me-2"></i>
                    تفاصيل المنتج: {{ $product->name }}
                </h1>
                <p class="text-muted mb-0">عرض شامل لمعلومات المنتج والمخزون والحركات</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('products.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
                </a>
                @if (auth()->user()->isAdmin())
                    <a href="{{ user_route('products.edit', $product) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                @endif
            </div>
        </div>

        <div class="row">
            <!-- Product Information -->
            <div class="col-lg-4">
                <!-- Basic Info -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">معلومات أساسية</h6>
                    </div>
                    <div class="card-body text-center">
                        @if ($product->image_path)
                            <img src="{{ asset('storage/' . $product->image_path) }}" alt="{{ $product->name }}"
                                class="img-fluid rounded mb-3" style="max-height: 200px;">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3"
                                style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif

                        <h5 class="fw-bold">{{ $product->name }}</h5>
                        <p class="text-muted mb-2">{{ $product->sku ?: 'بدون رمز' }}</p>

                        <div class="d-flex justify-content-center gap-2 mb-3">
                            @if ($product->is_active)
                                <span class="badge bg-success">نشط</span>
                            @else
                                <span class="badge bg-danger">غير نشط</span>
                            @endif
                            <span class="badge bg-info">{{ $product->category->name ?? 'بدون فئة' }}</span>
                        </div>

                        @if ($product->description)
                            <div class="text-start">
                                <h6 class="fw-bold">الوصف:</h6>
                                <p class="text-muted small">{{ $product->description }}</p>
                            </div>
                        @endif

                        <div class="text-start">
                            <small class="text-muted">
                                <strong>تاريخ الإنشاء:</strong> {{ $product->created_at->format('Y-m-d H:i') }}<br>
                                <strong>آخر تحديث:</strong> {{ $product->updated_at->format('Y-m-d H:i') }}
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Inventory Summary -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">ملخص المخزون</h6>
                    </div>
                    <div class="card-body">
                        @php
                            $totalBranchStock = $product->branchInventories->sum('quantity');
                            $totalStoreStock = $product->storeInventories->sum('quantity');
                            $totalStock = $totalBranchStock + $totalStoreStock;
                        @endphp

                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <div class="border-bottom pb-2">
                                    <h4 class="fw-bold text-primary">{{ number_format($totalStock, 2) }}</h4>
                                    <small class="text-muted">إجمالي المخزون</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="fw-bold text-info">{{ number_format($totalBranchStock, 2) }}</h6>
                                <small class="text-muted">في الفروع</small>
                            </div>
                            <div class="col-6">
                                <h6 class="fw-bold text-warning">{{ number_format($totalStoreStock, 2) }}</h6>
                                <small class="text-muted">في المخازن</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Information -->
            <div class="col-lg-8">
                <!-- Inventory by Location -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">المخزون حسب الموقع</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Branch Inventory -->
                            <div class="col-md-6">
                                <h6 class="fw-bold text-info mb-3">الفروع</h6>
                                @if ($product->branchInventories->count() > 0)
                                    @foreach ($product->branchInventories as $inventory)
                                        <div
                                            class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                            <div>
                                                <span class="fw-bold">{{ $inventory->branch->name }}</span>
                                                @if ($inventory->selling_price)
                                                    <br><small class="text-muted">سعر البيع:
                                                        {{ format_currency($inventory->selling_price) }}</small>
                                                @endif
                                            </div>
                                            <span
                                                class="badge bg-info">{{ number_format($inventory->quantity, 2) }}</span>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted">لا يوجد مخزون في الفروع</p>
                                @endif
                            </div>

                            <!-- Store Inventory -->
                            <div class="col-md-6">
                                <h6 class="fw-bold text-warning mb-3">المخازن</h6>
                                @if ($product->storeInventories->count() > 0)
                                    @foreach ($product->storeInventories as $inventory)
                                        <div
                                            class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                            <div>
                                                <span class="fw-bold">{{ $inventory->store->name }}</span>
                                                @if ($inventory->store->isIndependent())
                                                    <small class="text-success">(مستقل)</small>
                                                @else
                                                    <small
                                                        class="text-muted">({{ $inventory->store->branch->name ?? 'فرع غير محدد' }})</small>
                                                @endif
                                            </div>
                                            <span
                                                class="badge bg-warning">{{ number_format($inventory->quantity, 2) }}</span>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted">لا يوجد مخزون في المخازن</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transfer History -->
                @if (isset($transferHistory) && $transferHistory->count() > 0)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-warning">سجل النقل</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم النقل</th>
                                            <th>النوع</th>
                                            <th>من</th>
                                            <th>إلى</th>
                                            <th>الكمية</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($transferHistory as $transfer)
                                            <tr>
                                                <td>
                                                    <a href="{{ user_route('inventory-transfers.show', $transfer->inventoryTransfer) }}"
                                                        class="text-primary text-decoration-none">
                                                        {{ $transfer->inventoryTransfer->transfer_number }}
                                                    </a>
                                                </td>
                                                <td>
                                                    @php
                                                        $typeLabels = [
                                                            'store_to_branch' => 'مخزن → فرع',
                                                            'branch_to_store' => 'فرع → مخزن',
                                                            'store_to_store' => 'مخزن → مخزن',
                                                            'branch_to_branch' => 'فرع → فرع',
                                                        ];
                                                    @endphp
                                                    <span
                                                        class="badge bg-info">{{ $typeLabels[$transfer->inventoryTransfer->type] ?? $transfer->inventoryTransfer->type }}</span>
                                                </td>
                                                <td>{{ $transfer->inventoryTransfer->source->name ?? 'غير محدد' }}</td>
                                                <td>{{ $transfer->inventoryTransfer->destination->name ?? 'غير محدد' }}
                                                </td>
                                                <td>{{ number_format($transfer->requested_quantity, 2) }}</td>
                                                <td>
                                                    @php
                                                        $statusColors = [
                                                            'pending' => 'warning',
                                                            'approved' => 'info',
                                                            'in_transit' => 'secondary',
                                                            'completed' => 'success',
                                                            'cancelled' => 'danger',
                                                        ];
                                                        $statusLabels = [
                                                            'pending' => 'في الانتظار',
                                                            'approved' => 'موافق عليه',
                                                            'in_transit' => 'في الطريق',
                                                            'completed' => 'مكتمل',
                                                            'cancelled' => 'ملغي',
                                                        ];
                                                    @endphp
                                                    <span
                                                        class="badge bg-{{ $statusColors[$transfer->inventoryTransfer->status] ?? 'secondary' }}">
                                                        {{ $statusLabels[$transfer->inventoryTransfer->status] ?? $transfer->inventoryTransfer->status }}
                                                    </span>
                                                </td>
                                                <td>{{ $transfer->inventoryTransfer->created_at->format('Y-m-d H:i') }}
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Sales History -->
                @if ($product->saleItems->count() > 0)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">سجل المبيعات</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>الفرع</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>الإجمالي</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($product->saleItems->take(10) as $saleItem)
                                            <tr>
                                                <td>
                                                    <a href="{{ user_route('sales.show', $saleItem->sale) }}"
                                                        class="text-primary text-decoration-none">
                                                        {{ $saleItem->sale->invoice_number }}
                                                    </a>
                                                </td>
                                                <td>{{ $saleItem->sale->customer->name ?? 'عميل نقدي' }}</td>
                                                <td>{{ $saleItem->sale->branch->name ?? 'غير محدد' }}</td>
                                                <td>{{ number_format($saleItem->quantity, 2) }}</td>
                                                <td>{{ format_currency($saleItem->price) }}</td>
                                                <td>{{ format_currency($saleItem->subtotal) }}</td>
                                                <td>{{ $saleItem->created_at->format('Y-m-d H:i') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                                @if ($product->saleItems->count() > 10)
                                    <div class="text-center mt-3">
                                        <small class="text-muted">عرض آخر 10 مبيعات من أصل
                                            {{ $product->saleItems->count() }} مبيعة</small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Purchase History -->
                @if (isset($purchaseHistory) && $purchaseHistory->count() > 0)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">سجل المشتريات</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الشراء</th>
                                            <th>المورد</th>
                                            <th>الكمية</th>
                                            <th>سعر الشراء</th>
                                            <th>الإجمالي</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($purchaseHistory->take(10) as $purchase)
                                            @if (isset($purchase->purchase_item))
                                                <tr>
                                                    <td>
                                                        <a href="{{ user_route('purchases.show', $purchase) }}"
                                                            class="text-primary text-decoration-none">
                                                            {{ $purchase->invoice_number }}
                                                        </a>
                                                    </td>
                                                    <td>{{ $purchase->supplier->name ?? 'غير محدد' }}</td>
                                                    <td>{{ number_format($purchase->purchase_item->quantity, 2) }}</td>
                                                    <td>{{ number_format($purchase->purchase_item->cost_price, 2) }}
                                                        ج.م</td>
                                                    <td>{{ number_format($purchase->purchase_item->total_price, 2) }}
                                                        ج.م</td>
                                                    <td>{{ $purchase->created_at->format('Y-m-d H:i') }}</td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    </tbody>
                                </table>
                                @if ($purchaseHistory->count() > 10)
                                    <div class="text-center mt-3">
                                        <small class="text-muted">عرض آخر 10 مشتريات من أصل
                                            {{ $purchaseHistory->count() }} مشترية</small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>

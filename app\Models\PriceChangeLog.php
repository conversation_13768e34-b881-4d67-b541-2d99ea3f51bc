<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PriceChangeLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'branch_id',
        'user_id',
        'price_type',
        'old_price',
        'new_price',
        'price_difference',
        'percentage_change',
        'change_reason',
        'change_source',
        'metadata',
        'validation_warnings',
        'below_cost_price',
        'profit_margin_before',
        'profit_margin_after',
    ];

    protected $casts = [
        'old_price' => 'decimal:2',
        'new_price' => 'decimal:2',
        'price_difference' => 'decimal:2',
        'percentage_change' => 'decimal:4',
        'metadata' => 'array',
        'validation_warnings' => 'array',
        'below_cost_price' => 'boolean',
        'profit_margin_before' => 'decimal:4',
        'profit_margin_after' => 'decimal:4',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get formatted price change description
     */
    public function getChangeDescriptionAttribute(): string
    {
        $direction = $this->price_difference > 0 ? 'زيادة' : 'تخفيض';
        $amount = abs($this->price_difference);
        $percentage = $this->percentage_change ? abs($this->percentage_change) : 0;
        
        return "{$direction} بمقدار {$amount} ج.م ({$percentage}%)";
    }

    /**
     * Get formatted price type description
     */
    public function getPriceTypeDescriptionAttribute(): string
    {
        $types = [
            'product_price' => 'السعر الأساسي للمنتج',
            'product_selling_price' => 'سعر البيع للمنتج',
            'branch_sale_price_1' => 'سعر البيع الأول للفرع',
            'branch_sale_price_2' => 'سعر البيع الثاني للفرع',
            'branch_sale_price_3' => 'سعر البيع الثالث للفرع',
            'branch_cost_price' => 'سعر التكلفة للفرع',
        ];

        return $types[$this->price_type] ?? $this->price_type;
    }

    /**
     * Scope for recent changes
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope for significant changes
     */
    public function scopeSignificant($query, float $threshold = 0.20)
    {
        return $query->where('percentage_change', '>=', $threshold)
                    ->orWhere('percentage_change', '<=', -$threshold);
    }

    /**
     * Scope for changes below cost price
     */
    public function scopeBelowCost($query)
    {
        return $query->where('below_cost_price', true);
    }
}

<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('تفاصيل تصنيف المصروفات') }}
            </h2>
            <div>
                <a href="{{ route('expense-categories.edit', $expenseCategory) }}" class="btn btn-primary me-2">
                    <i class="fas fa-edit"></i> {{ __('تعديل') }}
                </a>
                <a href="{{ route('expense-categories.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> {{ __('عودة') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="row">
                <!-- Category Information -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('معلومات التصنيف') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>{{ __('الاسم') }}:</strong>
                                </div>
                                <div class="col-md-8">
                                    @if($expenseCategory->icon)
                                        <img src="{{ Storage::url($expenseCategory->icon) }}" alt="{{ $expenseCategory->name }}" class="category-icon me-2" style="width: 24px; height: 24px;">
                                    @endif
                                    <span style="color: {{ $expenseCategory->color ?: '#000' }}">{{ $expenseCategory->name }}</span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>{{ __('الوصف') }}:</strong>
                                </div>
                                <div class="col-md-8">
                                    {{ $expenseCategory->description ?: '-' }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>{{ __('التصنيف الأب') }}:</strong>
                                </div>
                                <div class="col-md-8">
                                    {{ $expenseCategory->parent ? $expenseCategory->parent->name : '-' }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>{{ __('الحالة') }}:</strong>
                                </div>
                                <div class="col-md-8">
                                    @if($expenseCategory->is_active)
                                        <span class="badge bg-success">{{ __('نشط') }}</span>
                                    @else
                                        <span class="badge bg-danger">{{ __('غير نشط') }}</span>
                                    @endif
                                    @if($expenseCategory->is_fixed)
                                        <span class="badge bg-info">{{ __('ثابت') }}</span>
                                    @endif
                                    @if($expenseCategory->requires_approval)
                                        <span class="badge bg-warning">{{ __('يتطلب موافقة') }}</span>
                                    @endif
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>{{ __('تاريخ الإنشاء') }}:</strong>
                                </div>
                                <div class="col-md-8">
                                    {{ $expenseCategory->created_at->format('Y-m-d H:i') }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>{{ __('آخر تحديث') }}:</strong>
                                </div>
                                <div class="col-md-8">
                                    {{ $expenseCategory->updated_at->format('Y-m-d H:i') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Budget Information -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('معلومات الميزانية') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>{{ __('الميزانية الشهرية') }}:</strong>
                                </div>
                                <div class="col-md-8">
                                    @if($expenseCategory->monthly_budget)
                                        <div class="d-flex align-items-center">
                                            <span class="me-2">{{ number_format($expenseCategory->monthly_budget, 2) }}</span>
                                            <div class="progress flex-grow-1" style="height: 5px;">
                                                <div class="progress-bar {{ $expenseCategory->monthly_budget_usage > 100 ? 'bg-danger' : 'bg-success' }}"
                                                     role="progressbar"
                                                     style="width: {{ min($expenseCategory->monthly_budget_usage, 100) }}%">
                                                </div>
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            {{ __('المصروفات الشهرية') }}: {{ number_format($monthlyExpenses, 2) }}
                                            ({{ number_format($expenseCategory->monthly_budget_usage, 1) }}%)
                                        </small>
                                    @else
                                        -
                                    @endif
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>{{ __('الميزانية السنوية') }}:</strong>
                                </div>
                                <div class="col-md-8">
                                    @if($expenseCategory->yearly_budget)
                                        <div class="d-flex align-items-center">
                                            <span class="me-2">{{ number_format($expenseCategory->yearly_budget, 2) }}</span>
                                            <div class="progress flex-grow-1" style="height: 5px;">
                                                <div class="progress-bar {{ $expenseCategory->yearly_budget_usage > 100 ? 'bg-danger' : 'bg-success' }}"
                                                     role="progressbar"
                                                     style="width: {{ min($expenseCategory->yearly_budget_usage, 100) }}%">
                                                </div>
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            {{ __('المصروفات السنوية') }}: {{ number_format($yearlyExpenses, 2) }}
                                            ({{ number_format($expenseCategory->yearly_budget_usage, 1) }}%)
                                        </small>
                                    @else
                                        -
                                    @endif
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>{{ __('عدد المصروفات') }}:</strong>
                                </div>
                                <div class="col-md-8">
                                    {{ $expenseCategory->expenses_count }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>{{ __('إجمالي المصروفات') }}:</strong>
                                </div>
                                <div class="col-md-8">
                                    {{ number_format($expenseCategory->expenses_sum_amount, 2) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subcategories -->
                @if($expenseCategory->children->isNotEmpty())
                    <div class="col-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">{{ __('التصنيفات الفرعية') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>{{ __('الاسم') }}</th>
                                                <th>{{ __('الوصف') }}</th>
                                                <th>{{ __('عدد المصروفات') }}</th>
                                                <th>{{ __('إجمالي المصروفات') }}</th>
                                                <th>{{ __('الحالة') }}</th>
                                                <th>{{ __('الإجراءات') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($expenseCategory->children as $child)
                                                <tr>
                                                    <td>
                                                        @if($child->icon)
                                                            <img src="{{ Storage::url($child->icon) }}" alt="{{ $child->name }}" class="category-icon me-2" style="width: 24px; height: 24px;">
                                                        @endif
                                                        <span style="color: {{ $child->color ?: '#000' }}">{{ $child->name }}</span>
                                                    </td>
                                                    <td>{{ $child->description ?: '-' }}</td>
                                                    <td>{{ $child->expenses_count }}</td>
                                                    <td>{{ number_format($child->expenses_sum_amount, 2) }}</td>
                                                    <td>
                                                        @if($child->is_active)
                                                            <span class="badge bg-success">{{ __('نشط') }}</span>
                                                        @else
                                                            <span class="badge bg-danger">{{ __('غير نشط') }}</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="{{ route('expense-categories.show', $child) }}" class="btn btn-info btn-sm" title="{{ __('عرض') }}">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="{{ route('expense-categories.edit', $child) }}" class="btn btn-primary btn-sm" title="{{ __('تعديل') }}">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Expenses -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('المصروفات') }}</h5>
                        </div>
                        <div class="card-body">
                            @if($expenseCategory->expenses->isNotEmpty())
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>{{ __('التاريخ') }}</th>
                                                <th>{{ __('المرجع') }}</th>
                                                <th>{{ __('الفرع') }}</th>
                                                <th>{{ __('المبلغ') }}</th>
                                                <th>{{ __('الحالة') }}</th>
                                                <th>{{ __('الإجراءات') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($expenses as $expense)
                                                <tr>
                                                    <td>{{ $expense->expense_date->format('Y-m-d') }}</td>
                                                    <td>{{ $expense->reference_number }}</td>
                                                    <td>{{ $expense->branch->name }}</td>
                                                    <td>{{ number_format($expense->amount, 2) }}</td>
                                                    <td>
                                                        @if($expense->status == 'pending')
                                                            <span class="badge bg-warning">{{ __('قيد الانتظار') }}</span>
                                                        @elseif($expense->status == 'approved')
                                                            <span class="badge bg-success">{{ __('تمت الموافقة') }}</span>
                                                        @else
                                                            <span class="badge bg-danger">{{ __('مرفوض') }}</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('expenses.show', $expense) }}" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="6" class="text-center">{{ __('لا توجد مصروفات') }}</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-4">
                                    {{ $expenses->links() }}
                                </div>
                            @else
                                <p class="text-center mb-0">{{ __('لا توجد مصروفات لهذا التصنيف') }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .card-title {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .btn-group {
            flex-direction: row-reverse;
        }
        .rtl .btn-group .btn {
            margin-right: 0;
            margin-left: 0.25rem;
        }
        .category-icon {
            object-fit: cover;
            border-radius: 4px;
        }
        .progress {
            background-color: #e9ecef;
            border-radius: 0.25rem;
        }
    </style>
    @endpush
</x-app-layout>

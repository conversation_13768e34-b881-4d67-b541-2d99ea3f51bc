@extends('layouts.app')

@section('title', 'تاريخ عمليات النقل')

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تاريخ عمليات النقل</h1>
                <p class="text-muted mb-0">سجل تفصيلي لجميع عمليات نقل المخزون</p>
            </div>
            <div>
                <a href="{{ user_route('inventory-transfer-reports.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
                <a href="{{ user_route('inventory-transfer-reports.export', array_merge(['type' => 'transfers'], request()->query())) }}"
                    class="btn btn-success">
                    <i class="fas fa-file-csv me-2"></i>تصدير
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-2">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="{{ $dateFrom }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="{{ $dateTo }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="pending" {{ $status == 'pending' ? 'selected' : '' }}>قيد الانتظار</option>
                            <option value="approved" {{ $status == 'approved' ? 'selected' : '' }}>موافق عليه</option>
                            <option value="shipped" {{ $status == 'shipped' ? 'selected' : '' }}>تم الشحن</option>
                            <option value="received" {{ $status == 'received' ? 'selected' : '' }}>تم الاستلام</option>
                            <option value="cancelled" {{ $status == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">من موقع</label>
                        <select name="from_location_id" class="form-select">
                            <option value="">جميع المواقع</option>
                            <optgroup label="المخازن">
                                @foreach ($stores as $store)
                                    <option value="{{ $store->id }}"
                                        {{ $fromLocationId == $store->id ? 'selected' : '' }}>
                                        {{ $store->name }}
                                    </option>
                                @endforeach
                            </optgroup>
                            <optgroup label="الفروع">
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                        {{ $fromLocationId == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </optgroup>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">إلى موقع</label>
                        <select name="to_location_id" class="form-select">
                            <option value="">جميع المواقع</option>
                            <optgroup label="المخازن">
                                @foreach ($stores as $store)
                                    <option value="{{ $store->id }}"
                                        {{ $toLocationId == $store->id ? 'selected' : '' }}>
                                        {{ $store->name }}
                                    </option>
                                @endforeach
                            </optgroup>
                            <optgroup label="الفروع">
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                        {{ $toLocationId == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </optgroup>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Transfer History Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">سجل عمليات النقل</h6>
            </div>
            <div class="card-body">
                @if ($transfers->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-bold">رقم النقل</th>
                                    <th class="border-0 fw-bold">من موقع</th>
                                    <th class="border-0 fw-bold">إلى موقع</th>
                                    <th class="border-0 fw-bold">عدد المنتجات</th>
                                    <th class="border-0 fw-bold">الحالة</th>
                                    <th class="border-0 fw-bold">تاريخ الإنشاء</th>
                                    <th class="border-0 fw-bold">المستخدم</th>
                                    <th class="border-0 fw-bold text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($transfers as $transfer)
                                    <tr class="border-bottom">
                                        <td>
                                            <a href="{{ user_route('inventory-transfers.show', $transfer) }}"
                                                class="fw-bold text-primary text-decoration-none">
                                                #{{ $transfer->id }}
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-info text-white me-2">
                                                    {{ substr($transfer->fromStore ? $transfer->fromStore->name : $transfer->fromBranch->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        {{ $transfer->fromStore ? $transfer->fromStore->name : $transfer->fromBranch->name }}
                                                    </div>
                                                    <small
                                                        class="text-muted">{{ $transfer->fromStore ? 'مخزن' : 'فرع' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-success text-white me-2">
                                                    {{ substr($transfer->toStore ? $transfer->toStore->name : $transfer->toBranch->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        {{ $transfer->toStore ? $transfer->toStore->name : $transfer->toBranch->name }}
                                                    </div>
                                                    <small
                                                        class="text-muted">{{ $transfer->toStore ? 'مخزن' : 'فرع' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ $transfer->items->count() }} منتج</span>
                                            <div class="small text-muted mt-1">
                                                إجمالي: {{ $transfer->items->sum('quantity') }} قطعة
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'pending' => 'warning',
                                                    'approved' => 'info',
                                                    'shipped' => 'secondary',
                                                    'received' => 'success',
                                                    'cancelled' => 'danger',
                                                ];
                                                $statusLabels = [
                                                    'pending' => 'قيد الانتظار',
                                                    'approved' => 'موافق عليه',
                                                    'shipped' => 'تم الشحن',
                                                    'received' => 'تم الاستلام',
                                                    'cancelled' => 'ملغي',
                                                ];
                                            @endphp
                                            <span class="badge bg-{{ $statusColors[$transfer->status] ?? 'secondary' }}">
                                                {{ $statusLabels[$transfer->status] ?? $transfer->status }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="fw-bold">{{ $transfer->created_at->format('Y-m-d') }}</div>
                                            <small class="text-muted">{{ $transfer->created_at->format('H:i') }}</small>
                                            <div class="small text-muted">{{ $transfer->created_at->diffForHumans() }}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2">
                                                    {{ substr($transfer->user->name ?? 'غير محدد', 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold small">{{ $transfer->user->name ?? 'غير محدد' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{{ user_route('inventory-transfers.show', $transfer) }}"
                                                    class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if ($transfer->status === 'pending')
                                                    <a href="{{ user_route('inventory-transfers.edit', $transfer) }}"
                                                        class="btn btn-outline-warning btn-sm" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $transfers->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد عمليات نقل</h5>
                        <p class="text-muted">لم يتم العثور على عمليات نقل في الفترة المحددة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .btn-group .btn {
            margin-left: 2px;
        }

        .btn-group .btn:first-child {
            margin-left: 0;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>
@endsection

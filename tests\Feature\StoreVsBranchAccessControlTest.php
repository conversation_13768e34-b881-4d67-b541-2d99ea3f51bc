<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\Branch;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StoreVsBranchAccessControlTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;
    protected $warehouseUser;
    protected $managerUser;
    protected $sellerUser;
    protected $branch;
    protected $store;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles with permissions
        $adminRole = Role::create([
            'name' => 'admin',
            'description' => 'Administrator',
            'permissions' => [
                'stores.inventory.manage',
                'branches.sales-inventory.manage',
                'inventory-transfers.approve',
            ],
            'is_active' => true,
        ]);

        $warehouseRole = Role::create([
            'name' => 'warehouse_staff',
            'description' => 'Warehouse Staff',
            'permissions' => [
                'stores.inventory.manage',
                'inventory-transfers.approve',
                'inventory-transfers.ship',
            ],
            'is_active' => true,
        ]);

        $managerRole = Role::create([
            'name' => 'manager',
            'description' => 'Branch Manager',
            'permissions' => [
                'branches.sales-inventory.manage',
                'inventory-transfers.create',
                'inventory-transfers.receive',
            ],
            'is_active' => true,
        ]);

        $sellerRole = Role::create([
            'name' => 'seller',
            'description' => 'Seller',
            'permissions' => [
                'branches.sales-inventory.view',
                'inventory-transfers.view',
            ],
            'is_active' => true,
        ]);

        // Create test entities
        $this->branch = Branch::create([
            'code' => 'BR001',
            'name' => 'Test Branch',
            'address' => '123 Test St',
            'phone' => '+1234567890',
            'email' => '<EMAIL>',
            'opening_balance' => 10000,
            'is_active' => true,
        ]);

        $this->store = Store::create([
            'code' => 'ST001',
            'name' => 'Test Store',
            'address' => '456 Store Ave',
            'phone' => '+0987654321',
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        // Create users
        $this->adminUser = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $adminRole->id,
        ]);

        $this->warehouseUser = User::create([
            'name' => 'Warehouse User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $warehouseRole->id,
            'store_id' => $this->store->id,
        ]);

        $this->managerUser = User::create([
            'name' => 'Manager User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $managerRole->id,
            'branch_id' => $this->branch->id,
        ]);

        $this->sellerUser = User::create([
            'name' => 'Seller User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => $sellerRole->id,
            'branch_id' => $this->branch->id,
        ]);
    }

    public function test_admin_can_access_both_store_and_branch_operations()
    {
        $this->actingAs($this->adminUser);

        // Should be able to access store operations
        $response = $this->get(route('admin.stores.inventory', $this->store));
        $response->assertStatus(200);

        // Should be able to access branch operations
        $response = $this->get(route('admin.branches.sales-inventory', $this->branch));
        $response->assertStatus(200);
    }

    public function test_warehouse_staff_can_access_store_operations_only()
    {
        $this->actingAs($this->warehouseUser);

        // Should be able to access store operations
        $response = $this->get(route('admin.stores.inventory', $this->store));
        $response->assertStatus(200);

        // Should NOT be able to access branch sales operations
        $response = $this->get(route('admin.branches.sales-inventory', $this->branch));
        $response->assertStatus(403);
    }

    public function test_manager_can_access_branch_operations_only()
    {
        $this->actingAs($this->managerUser);

        // Should be able to access branch operations
        $response = $this->get(route('admin.branches.sales-inventory', $this->branch));
        $response->assertStatus(200);

        // Should NOT be able to access store inventory operations
        $response = $this->get(route('admin.stores.inventory', $this->store));
        $response->assertStatus(403);
    }

    public function test_seller_has_limited_branch_access()
    {
        $this->actingAs($this->sellerUser);

        // Should be able to view branch sales inventory (read-only)
        $response = $this->get(route('admin.branches.sales-inventory', $this->branch));
        $response->assertStatus(200);

        // Should NOT be able to access store operations
        $response = $this->get(route('admin.stores.inventory', $this->store));
        $response->assertStatus(403);
    }

    public function test_user_access_methods_work_correctly()
    {
        // Test admin access methods
        $this->assertTrue($this->adminUser->canAccessStoreOperations());
        $this->assertTrue($this->adminUser->canAccessBranchSalesOperations());
        $this->assertTrue($this->adminUser->canManageStoreInventory());
        $this->assertTrue($this->adminUser->canManageBranchSalesInventory());

        // Test warehouse staff access methods
        $this->assertTrue($this->warehouseUser->canAccessStoreOperations());
        $this->assertFalse($this->warehouseUser->canAccessBranchSalesOperations());
        $this->assertTrue($this->warehouseUser->canManageStoreInventory());
        $this->assertFalse($this->warehouseUser->canManageBranchSalesInventory());

        // Test manager access methods
        $this->assertFalse($this->managerUser->canAccessStoreOperations());
        $this->assertTrue($this->managerUser->canAccessBranchSalesOperations());
        $this->assertFalse($this->managerUser->canManageStoreInventory());
        $this->assertTrue($this->managerUser->canManageBranchSalesInventory());

        // Test seller access methods
        $this->assertFalse($this->sellerUser->canAccessStoreOperations());
        $this->assertTrue($this->sellerUser->canAccessBranchSalesOperations());
        $this->assertFalse($this->sellerUser->canManageStoreInventory());
        $this->assertFalse($this->sellerUser->canManageBranchSalesInventory());
    }
}

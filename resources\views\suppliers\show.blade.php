<x-app-layout>
    <div class="container-fluid px-4">
        <!-- <PERSON> Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <div class="avatar-circle bg-primary text-white me-3 d-inline-flex">
                        {{ substr($supplier->name, 0, 1) }}
                    </div>
                    {{ $supplier->name }}
                </h1>
                <p class="text-muted mb-0">تفاصيل المورد ومعلومات الاتصال</p>
            </div>
            <div class="btn-group" role="group">
                <a href="{{ user_route('suppliers.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة إلى الموردين
                </a>
                {{-- @if (auth()->user()->isAdmin())
                    <a href="{{ user_route('suppliers.edit', $supplier) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المورد
                    </a>
                @endif --}}
            </div>
        </div>

        <!-- Status Banner -->
        <div class="row mb-4">
            <div class="col-12">
                <div
                    class="alert {{ $supplier->is_active == 1 ? 'alert-success' : 'alert-warning' }} border-0 shadow-sm">
                    <div class="d-flex align-items-center">
                        <i
                            class="fas {{ $supplier->is_active == 1 ? 'fa-check-circle' : 'fa-exclamation-triangle' }} fa-2x me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">
                                حالة المورد:
                                @if ($supplier->is_active == 1)
                                    <span class="badge bg-success ms-2">نشط</span>
                                @else
                                    <span class="badge bg-warning ms-2">غير نشط</span>
                                @endif
                            </h6>
                            <p class="mb-0">
                                @if ($supplier->is_active == 1)
                                    المورد نشط ومتاح للتعامل معه
                                @else
                                    المورد غير نشط حالياً
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    عدد المشتريات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $supplier->purchases->count() }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    إجمالي المشتريات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ number_format($supplier->purchases->sum('total_amount'), 2) }} ج.م
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    متوسط قيمة الشراء
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    @php
                                        $avg =
                                            $supplier->purchases->count() > 0
                                                ? $supplier->purchases->sum('total_amount') /
                                                    $supplier->purchases->count()
                                                : 0;
                                    @endphp
                                    {{ number_format($avg, 2) }} ج.م
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    آخر عملية شراء
                                </div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">
                                    @if ($supplier->purchases->count() > 0)
                                        {{ $supplier->purchases->sortByDesc('created_at')->first()->created_at->diffForHumans() }}
                                    @else
                                        لا يوجد
                                    @endif
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Supplier Details -->
        <div class="row mb-4">
            <!-- Contact Information -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header bg-primary text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-address-card me-2"></i>
                            معلومات الاتصال
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-primary">
                                    <i class="fas fa-user me-1"></i>
                                    اسم المورد:
                                </strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="text-gray-800">{{ $supplier->name }}</span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-success">
                                    <i class="fas fa-phone me-1"></i>
                                    رقم الهاتف:
                                </strong>
                            </div>
                            <div class="col-sm-8">
                                @if ($supplier->phone)
                                    <a href="tel:{{ $supplier->phone }}" class="text-success text-decoration-none">
                                        <i class="fas fa-phone-alt me-1"></i>
                                        {{ $supplier->phone }}
                                    </a>
                                @else
                                    <span class="text-muted">غير متوفر</span>
                                @endif
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-info">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني:
                                </strong>
                            </div>
                            <div class="col-sm-8">
                                @if ($supplier->email)
                                    <a href="mailto:{{ $supplier->email }}" class="text-info text-decoration-none">
                                        <i class="fas fa-at me-1"></i>
                                        {{ $supplier->email }}
                                    </a>
                                @else
                                    <span class="text-muted">غير متوفر</span>
                                @endif
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-danger">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    العنوان:
                                </strong>
                            </div>
                            <div class="col-sm-8">
                                @if ($supplier->address)
                                    <span class="text-gray-800">{{ $supplier->address }}</span>
                                @else
                                    <span class="text-muted">غير متوفر</span>
                                @endif
                            </div>
                        </div>

                        <div class="row mb-0">
                            <div class="col-sm-4">
                                <strong class="text-warning">
                                    <i class="fas fa-receipt me-1"></i>
                                    الرقم الضريبي:
                                </strong>
                            </div>
                            <div class="col-sm-8">
                                @if ($supplier->tax_number)
                                    <span class="text-gray-800">{{ $supplier->tax_number }}</span>
                                @else
                                    <span class="text-muted">غير متوفر</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Additional Information -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header bg-info text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات إضافية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-primary">
                                    <i class="fas fa-calendar-plus me-1"></i>
                                    تاريخ التسجيل:
                                </strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="text-gray-800">{{ $supplier->created_at->format('Y-m-d H:i') }}</span>
                                <small class="text-muted d-block">{{ $supplier->created_at->diffForHumans() }}</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-success">
                                    <i class="fas fa-edit me-1"></i>
                                    آخر تحديث:
                                </strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="text-gray-800">{{ $supplier->updated_at->format('Y-m-d H:i') }}</span>
                                <small class="text-muted d-block">{{ $supplier->updated_at->diffForHumans() }}</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-warning">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    ملاحظات:
                                </strong>
                            </div>
                            <div class="col-sm-8">
                                @if ($supplier->notes)
                                    <div class="alert alert-light border-0 p-2 mb-0">
                                        <small class="text-gray-800">{{ $supplier->notes }}</small>
                                    </div>
                                @else
                                    <span class="text-muted">لا توجد ملاحظات</span>
                                @endif
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="border-top pt-3">
                            <h6 class="text-gray-800 mb-3">
                                <i class="fas fa-bolt text-warning me-1"></i>
                                إجراءات سريعة
                            </h6>
                            <div class="d-grid gap-2">
                                @if (auth()->user()->isAdmin())
                                    <a href="{{ user_route('purchases.create') }}?supplier_id={{ $supplier->id }}"
                                        class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>
                                        إضافة مشترى جديد
                                    </a>
                                    <a href="{{ user_route('suppliers.edit', $supplier) }}"
                                        class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل بيانات المورد
                                    </a>
                                @endif
                                @if ($supplier->phone)
                                    <a href="tel:{{ $supplier->phone }}" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-phone me-1"></i>
                                        اتصال مباشر
                                    </a>
                                @endif
                                @if ($supplier->email)
                                    <a href="mailto:{{ $supplier->email }}" class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-envelope me-1"></i>
                                        إرسال بريد إلكتروني
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchase History -->
        @if ($supplier->purchases->count() > 0)
            <div class="card shadow">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-history me-2"></i>
                            سجل المشتريات ({{ $supplier->purchases->count() }} عملية)
                        </h6>
                        @if (auth()->user()->isAdmin())
                            <a href="{{ user_route('purchases.create') }}?supplier_id={{ $supplier->id }}"
                                class="btn btn-light btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                إضافة مشترى جديد
                            </a>
                        @endif
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 text-primary">
                                        <i class="fas fa-hashtag me-1"></i>
                                        المرجع
                                    </th>
                                    <th class="border-0 text-success">
                                        <i class="fas fa-calendar me-1"></i>
                                        التاريخ
                                    </th>
                                    <th class="border-0 text-info">
                                        <i class="fas fa-store me-1"></i>
                                        الفرع
                                    </th>
                                    <th class="border-0 text-warning">
                                        <i class="fas fa-boxes me-1"></i>
                                        المنتجات
                                    </th>
                                    <th class="border-0 text-danger">
                                        <i class="fas fa-money-bill me-1"></i>
                                        المجموع
                                    </th>
                                    <th class="border-0 text-secondary">
                                        <i class="fas fa-percentage me-1"></i>
                                        الخصم
                                    </th>
                                    <th class="border-0 text-success">
                                        <i class="fas fa-check-circle me-1"></i>
                                        المدفوع
                                    </th>
                                    <th class="border-0 text-warning">
                                        <i class="fas fa-clock me-1"></i>
                                        المتبقي
                                    </th>
                                    <th class="border-0 text-primary">
                                        <i class="fas fa-flag me-1"></i>
                                        الحالة
                                    </th>
                                    <th class="border-0 text-gray-800">
                                        <i class="fas fa-cogs me-1"></i>
                                        الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($supplier->purchases->sortByDesc('created_at') as $purchase)
                                    <tr class="border-bottom">
                                        <td class="fw-bold text-primary">
                                            <i class="fas fa-receipt me-1"></i>
                                            {{ $purchase->reference }}
                                        </td>
                                        <td>
                                            <span class="text-gray-800">{{ $purchase->purchase_date }}</span>
                                            <small
                                                class="text-muted d-block">{{ $purchase->created_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <span
                                                class="badge bg-info text-white">{{ $purchase->branch ? $purchase->branch->name : 'غير محدد' }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span
                                                class="badge bg-warning text-dark">{{ $purchase->items->count() }}</span>
                                        </td>
                                        <td class="fw-bold text-success">
                                            {{ number_format($purchase->total_amount, 2) }} ج.م
                                        </td>
                                        <td class="text-secondary">
                                            @if ($purchase->discount_type === 'percentage')
                                                {{ $purchase->discount_value }}%
                                                ({{ number_format($purchase->discount_amount, 2) }} ج.م)
                                            @else
                                                {{ number_format($purchase->discount_amount, 2) }} ج.م
                                            @endif
                                        </td>
                                        <td class="text-success">
                                            {{ number_format($purchase->paid_amount, 2) }} ج.م
                                        </td>
                                        <td
                                            class="fw-bold {{ $purchase->remaining_amount > 0 ? 'text-danger' : 'text-success' }}">
                                            {{ number_format($purchase->remaining_amount, 2) }} ج.م
                                        </td>
                                        <td>
                                            @if ($purchase->status === 'completed')
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>
                                                    مكتمل
                                                </span>
                                            @elseif($purchase->status === 'cancelled')
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>
                                                    ملغي
                                                </span>
                                            @else
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-clock me-1"></i>
                                                    قيد الانتظار
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ user_route('purchases.show', $purchase) }}"
                                                    class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if ($purchase->status === 'pending' && auth()->user()->isAdmin())
                                                    <a href="{{ user_route('purchases.edit', $purchase) }}"
                                                        class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                @endif
                                                <a href="{{ user_route('purchases.print', $purchase) }}"
                                                    class="btn btn-sm btn-outline-secondary" target="_blank"
                                                    title="طباعة">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @else
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-shopping-cart fa-4x text-gray-300"></i>
                    </div>
                    <h5 class="text-gray-800 mb-3">لا يوجد سجل مشتريات</h5>
                    <p class="text-muted mb-4">لم يتم تسجيل أي عمليات شراء لهذا المورد حتى الآن</p>
                    @if (auth()->user()->isAdmin())
                        <a href="{{ user_route('purchases.create') }}?supplier_id={{ $supplier->id }}"
                            class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة أول عملية شراء
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>
    </div>

    @push('styles')
        <style>
            .rtl {
                direction: rtl;
                text-align: right;
            }

            .rtl .table th,
            .rtl .table td {
                text-align: right;
            }

            .rtl .btn-group {
                flex-direction: row-reverse;
            }

            .rtl .btn-group .btn {
                margin-right: 0;
                margin-left: 0.25rem;
            }

            .rtl .text-start {
                text-align: right !important;
            }

            .rtl .text-end {
                text-align: left !important;
            }
        </style>
    @endpush
</x-app-layout>

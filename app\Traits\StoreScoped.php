<?php

namespace App\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

trait StoreScoped
{
    /**
     * Scope query to only include records accessible by the given user
     */
    public function scopeAccessibleBy(Builder $query, User $user): Builder
    {
        if ($user->canAccessAllBranches()) {
            return $query;
        }

        // If user has store access, filter by store
        if ($user->store_id) {
            return $query->where('store_id', $user->store_id);
        }

        // Filter by accessible stores
        $accessibleStoreIds = $user->getAccessibleStoreIds();
        if (!empty($accessibleStoreIds)) {
            return $query->whereIn('store_id', $accessibleStoreIds);
        }

        // Fallback: filter by branch stores only
        return $query->whereHas('store', function ($q) use ($user) {
            $q->where('branch_id', $user->branch_id);
        });
    }

    /**
     * Scope query to only include records for a specific store
     */
    public function scopeForStore(Builder $query, int $storeId): Builder
    {
        return $query->where('store_id', $storeId);
    }

    /**
     * Scope query to only include records for specific stores
     */
    public function scopeForStores(Builder $query, array $storeIds): Builder
    {
        return $query->whereIn('store_id', $storeIds);
    }

    /**
     * Scope query to only include records for a specific branch
     */
    public function scopeForBranch(Builder $query, int $branchId): Builder
    {
        return $query->whereHas('store', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        });
    }

    /**
     * Scope query to only include records for specific branches
     */
    public function scopeForBranches(Builder $query, array $branchIds): Builder
    {
        return $query->whereHas('store', function ($q) use ($branchIds) {
            $q->whereIn('branch_id', $branchIds);
        });
    }

    /**
     * Check if the current user can access this record
     */
    public function canBeAccessedBy(User $user): bool
    {
        if ($user->canAccessAllBranches()) {
            return true;
        }

        $store = $this->store;
        if (!$store) {
            return false;
        }

        // If user has store access, check store match
        if ($user->store_id) {
            return $this->store_id === $user->store_id;
        }

        // Check if user can access this specific store
        return $user->canAccessStore($store);
    }

    /**
     * Get the store ID for this record
     */
    public function getStoreId(): ?int
    {
        return $this->store_id;
    }

    /**
     * Get the branch ID for this record through store relationship
     */
    public function getBranchId(): ?int
    {
        return $this->store?->branch_id;
    }
}

<x-app-layout>
    <div class="container-fluid">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">لوحة تحكم المدير</h2>
                    <div class="text-muted">
                        <i class="fas fa-calendar-alt"></i> {{ now()->format('Y-m-d') }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        {{-- <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي الفروع</h6>
                                <h3 class="mb-0">{{ $stats['total_branches'] }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-store fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المخازن</h6>
                                <h3 class="mb-0">{{ $stats['total_stores'] }}</h3>
                                <small>{{ $stats['independent_stores'] }} مستقل | {{ $stats['branch_stores'] }} تابع
                                    للفرع</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-shopping-bag fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المستخدمين</h6>
                                <h3 class="mb-0">{{ $stats['total_users'] }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مبيعات اليوم</h6>
                                <h3 class="mb-0">{{ format_currency($stats['total_sales_today']) }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}

        <!-- Main Statistics Cards -->
        <div class="row mb-4">
            <!-- Branches -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">الفروع</h6>
                                <h3 class="mb-0">{{ $stats['total_branches'] }}</h3>
                                <small>فرع</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-store fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stores -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">المخازن</h6>
                                <h3 class="mb-0">{{ $stats['total_stores'] }}</h3>
                                <small>مخزن</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-warehouse fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-secondary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">المستخدمين</h6>
                                <h3 class="mb-0">{{ $stats['total_users'] }}</h3>
                                <small>مستخدم</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today Sales -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مبيعات اليوم</h6>
                                <h3 class="mb-0">{{ format_currency($stats['total_sales_today'], null, 0) }}</h3>
                                <small>جنيه</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-shopping-cart fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Cards -->
        <div class="row mb-4">
            <!-- Today Purchases -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مشتريات اليوم</h6>
                                <h3 class="mb-0">{{ format_currency($stats['total_purchases_today'], null, 0) }}</h3>
                                <small>جنيه</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-shopping-bag fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Suppliers Owe -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مستحقات الموردين</h6>
                                <h3 class="mb-0">
                                    {{ format_currency($financialSummary['suppliers_owe'] ?? 0, null, 0) }}</h3>
                                <small>جنيه</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customers Owe -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-dark text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">ديون العملاء</h6>
                                <h3 class="mb-0">
                                    {{ format_currency($financialSummary['customers_owe'] ?? 0, null, 0) }}</h3>
                                <small>جنيه</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-user-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today Direct Transfers -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white" style="background-color: #6f42c1;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">تحويلات اليوم</h6>
                                <h3 class="mb-0">{{ $branchStats['today_direct_transfers'] ?? 0 }}</h3>
                                <small>تحويل مباشر</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-exchange-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Summary -->
        <div class="row mb-4">
            <div class="col-lg-12 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-wallet"></i> الملخص المالي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center p-3 border-end">
                                    <h6 class="text-muted mb-1">إجمالي المبيعات</h6>
                                    <h4 class="text-success mb-0">
                                        {{ format_currency($financialSummary['total_sales'] ?? 0) }}</h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3">
                                    <h6 class="text-muted mb-1">إجمالي المشتريات</h6>
                                    <h4 class="text-danger mb-0">
                                        {{ format_currency($financialSummary['total_purchases'] ?? 0) }}</h4>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center p-3 border-end">
                                    <h6 class="text-muted mb-1">ديون العملاء</h6>
                                    <h4 class="text-warning mb-0">
                                        {{ format_currency($financialSummary['customers_owe'] ?? 0) }}</h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3">
                                    <h6 class="text-muted mb-1">مستحقات الموردين</h6>
                                    <h4 class="text-info mb-0">
                                        {{ format_currency($financialSummary['suppliers_owe'] ?? 0) }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>



        <!-- Recent Activities -->
        <div class="row">
            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart"></i> أحدث المبيعات
                        </h5>
                        <a href="{{ route('admin.sales.index') }}" class="btn btn-sm btn-outline-primary">عرض
                            الكل</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>الفرع</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($recentSales as $sale)
                                        <tr>
                                            <td>
                                                <a href="{{ route('admin.sales.show', $sale) }}"
                                                    class="text-decoration-none">
                                                    #{{ $sale->id }}
                                                </a>
                                            </td>
                                            <td>{{ $sale->customer->name ?? 'عميل نقدي' }}</td>
                                            <td>{{ $sale->branch->name ?? '-' }}</td>
                                            <td>{{ format_currency($sale->total_amount) }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="4" class="text-center text-muted py-3">لا توجد مبيعات
                                                حديثة
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-truck"></i> أحدث المشتريات
                        </h5>
                        <a href="{{ route('admin.purchases.index') }}" class="btn btn-sm btn-outline-primary">عرض
                            الكل</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>المورد</th>
                                        <th>الفرع</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($recentPurchases as $purchase)
                                        <tr>
                                            <td>
                                                <a href="{{ route('admin.purchases.show', $purchase) }}"
                                                    class="text-decoration-none">
                                                    #{{ $purchase->id }}
                                                </a>
                                            </td>
                                            <td>{{ $purchase->supplier->name ?? '-' }}</td>
                                            <td>{{ $purchase->branch->name ?? '-' }}</td>
                                            <td>{{ format_currency($purchase->total_amount) }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="4" class="text-center text-muted py-3">لا توجد مشتريات
                                                حديثة</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Branches -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy"></i> الفروع
                        </h5>
                        <div>
                            <span class="badge bg-primary me-2">هذا الشهر</span>
                            {{-- <a href="{{ route('admin.branches.analytics-dashboard') }}"
                                class="btn btn-sm btn-outline-primary">عرض التحليلات</a> --}}
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم الفرع</th>
                                        <th>مبيعات الشهر</th>
                                        <th>عدد المبيعات</th>
                                        {{-- <th>المخازن</th> --}}
                                        <th>الموظفين</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($topBranches as $branch)
                                        <tr>
                                            <td>
                                                <strong>{{ $branch['name'] }}</strong>
                                            </td>
                                            <td>
                                                <span class="text-success fw-bold">
                                                    {{ format_currency($branch['monthly_sales']) }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $branch['sales_count'] }} مبيعة</span>
                                            </td>
                                            {{-- <td>
                                                <span class="badge bg-secondary">{{ $branch['stores_count'] }}
                                                    مخزن</span>
                                            </td> --}}
                                            <td>
                                                <span class="badge bg-primary">{{ $branch['users_count'] }}
                                                    موظف</span>
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.branches.show', $branch['id']) }}"
                                                    class="btn btn-sm btn-outline-primary">
                                                    عرض
                                                </a>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center text-muted py-3">
                                                <i class="fas fa-building fa-2x mb-2 text-muted"></i><br>
                                                لا توجد بيانات فروع متاحة
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outstanding Balances -->
        <div class="row">
            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle text-warning"></i> العملاء المدينون
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم العميل</th>
                                        <th>المبلغ المستحق</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($topCustomersOwing as $customer)
                                        <tr>
                                            <td>{{ $customer['name'] }}</td>
                                            <td class="text-warning fw-bold">
                                                {{ format_currency($customer['owed_amount']) }}</td>
                                            <td>
                                                <a href="{{ route('admin.customers.show', $customer['id']) }}"
                                                    class="btn btn-sm btn-outline-primary">
                                                    عرض
                                                </a>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="3" class="text-center text-muted py-3">لا توجد مستحقات
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-circle text-danger"></i> مستحقات الموردين
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم المورد</th>
                                        <th>المبلغ المستحق</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($topSuppliersWeOwe as $supplier)
                                        <tr>
                                            <td>{{ $supplier['name'] }}</td>
                                            <td class="text-danger fw-bold">
                                                {{ format_currency($supplier['owed_amount']) }}</td>
                                            <td>
                                                <a href="{{ user_route('suppliers.show', $supplier['id']) }}"
                                                    class="btn btn-sm btn-outline-primary">
                                                    عرض
                                                </a>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="3" class="text-center text-muted py-3">لا توجد مستحقات
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchases', function (Blueprint $table) {
            $table->boolean('is_distributed')->default(false)->after('status');
            $table->timestamp('distributed_at')->nullable()->after('is_distributed');
            $table->foreignId('distributed_by')->nullable()->constrained('users')->after('distributed_at');
        });

        Schema::table('purchase_items', function (Blueprint $table) {
            $table->decimal('distributed_quantity', 10, 2)->default(0)->after('total_price');
            $table->boolean('is_distributed')->default(false)->after('distributed_quantity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchases', function (Blueprint $table) {
            $table->dropForeign(['distributed_by']);
            $table->dropColumn(['is_distributed', 'distributed_at', 'distributed_by']);
        });

        Schema::table('purchase_items', function (Blueprint $table) {
            $table->dropColumn(['distribution_location_type', 'distribution_location_id', 'is_distributed']);
        });
    }
};
